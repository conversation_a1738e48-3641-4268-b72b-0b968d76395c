<template>
  <div class="content">
    <div class="card-container cardStyle">
      <!-- 左侧 -->
      <div class="card_left">
        <!-- 上半部分 -->
        <div class="card_left_top">
          <div class="imgStyle">
            <img style="width: 100%; height: 100%" src="@/assets/detectingSharing/ceshi.png" alt="" />
          </div>
          <div class="imgContent">
            <!-- 左箭头 -->
            <div style="cursor: pointer">
              <img src="@/assets/device/icon_left.png" alt="" />
            </div>
            <!-- 中间图片 -->
            <div style="display: flex; align-items: center; margin: 0 10px">
              <div class="everyImgStyle" v-for="(item, index) in imgList" :key="index">
                <!-- {{ item }} -->
                <img style="width: 100%; height: 100%" src="@/assets/detectingSharing/ceshi_small.png" alt="" />
              </div>
            </div>
            <!-- 右箭头 -->
            <div style="cursor: pointer">
              <img src="@/assets/device/icon_right.png" alt="" />
            </div>
          </div>
        </div>
        <!-- 下半部分 -->
        <div class="card_left_bottom">
          <div class="title">{{ detailsData.itemName }}</div>
          <div class="everyOption">
            <div class="optionName">检测实验室：</div>
            <div class="optionValue">{{ detailsData.labName || '暂无' }}</div>
          </div>
          <div class="everyOption">
            <div class="optionName">实验室地址：</div>
            <div class="optionValue">{{ detailsData.labAddress || '暂无' }}</div>
          </div>
          <div class="everyOption">
            <div class="optionName">联系方式：</div>
            <div class="optionValue">{{ detailsData.contactPhone || '暂无' }}</div>
          </div>
          <div class="buttonStyle" @click="jumpIntention">我有意向</div>
        </div>
      </div>
      <!-- 中间 -->
      <div class="card_center_line"></div>
      <!-- 右侧 -->
      <div class="card_right">
        <div>
          <div class="content_title">
            <div class="icon"></div>
            <div class="title">检测详情</div>
          </div>
          <div class="content_desc">
            {{ detailsData.testingDetails || '暂无' }}
          </div>
        </div>
        <div style="margin-top: 41px">
          <div class="content_title">
            <div class="icon"></div>
            <div class="title">实验室介绍</div>
          </div>
          <div class="content_desc">
            {{ detailsData.labIntroduction || '暂无' }}
          </div>
        </div>
        <div style="margin-top: 41px">
          <div class="content_title">
            <div class="icon"></div>
            <div class="title">资质证件</div>
          </div>
          <div class="content_desc">
            <div class="imgList">
              <div v-for="item in certificateList" :key="item.id" class="imgItem">
                <img src="@/assets/detectingSharing/certificate.png" alt="" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { getDetectionDetail, getLaboratoryDetail } from "@/api/serviceSharing";
export default {
  name: "deviceDetail",
  data() {
    return {
      detailsData: {
        itemName: "",
        testingDetails: "",
        labName: "",
        labAddress: "",
        contactPhone: "",
        labIntroduction: "",
        cnasQualification: "",
      },
      imgList: [
        {
          id: 1,
        },
        {
          id: 2,
        },
        {
          id: 3,
        },
        {
          id: 4,
        },
        {
          id: 5,
        },
      ],
      certificateList: [
        {
          id: 1,
        },
        {
          id: 2,
        },
        {
          id: 3,
        },
        {
          id: 4,
        },
        {
          id: 5,
        },
      ],
    };
  },
  created() {
    if (this.$route.query.id) {
      this.getDetectionDetail(this.$route.query.id);
    }
    if (this.$route.query.labId) {
      this.getLaboratoryDetail(this.$route.query.labId);
    }
  },
  methods: {
    intention(id) {
      this.$router.push("/receiveOrder"); // 传id
    },
    // 获取详情
    async getDetectionDetail(id) {
      const res = await getDetectionDetail(id);
      if (res.code === 200) {
        this.detailsData.itemName = res.data.itemName;
        this.detailsData.testingDetails = res.data.testingDetails;
        this.detailsData.updateTime = res.data.updateTime;
        this.detailsData.id = res.data.id;
      }
    },
    // 获取实验室详情
    async getLaboratoryDetail(id) {
      const res = await getLaboratoryDetail(id);
      if (res.code === 200) {
        this.detailsData.labName = res.data.labName;
        this.detailsData.labAddress = res.data.labAddress;
        this.detailsData.contactPhone = res.data.contactPhone;
        this.detailsData.labIntroduction = res.data.labIntroduction;
        this.detailsData.cnasQualification = res.data.cnasQualification;
      }
    },
    jumpIntention() {
      let userInfo = JSON.parse(sessionStorage.getItem("userinfo"));
      if (!userInfo?.memberCompanyName) {
        this.$confirm("您当前尚未关联企业，是否前往操作?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
          cancelButtonClass: "cancelButtonClass",
          confirmButtonClass: "customClass",
        })
          .then(() => {
            this.$router.push("/user/userCenter");
          })
          .catch(() => { });
        return;
      } else {
        this.$router.push(`/demandInterested?demandName=${this.detailsData.itemName}&updateTime=${this.detailsData.updateTime}&intentionType=8&fieldName=检测共享&intentionId=${this.detailsData.id}`);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  background-color: #f2f2f2;
  padding: 30px 0 61px 0;
}

.cardStyle {
  height: 660px;
  background-color: #ffffff;
  padding: 60px 56px 54px 50px;
  display: flex;
}

.card_left {
  .card_left_top {
    .imgStyle {
      width: 330px;
      height: 230px;
      border-radius: 2px;
      margin-left: 10px;
    }

    .imgContent {
      margin-top: 15px;
      display: flex;
      align-items: center;

      .everyImgStyle {
        width: 54px;
        height: 50px;
        margin-left: 10px;
        cursor: pointer;
      }

      .everyImgStyle:nth-child(1) {
        margin-left: 0;
      }
    }
  }

  .card_left_bottom {
    margin-top: 30px;

    .title {
      font-family: Source Han Sans CN;
      font-weight: 500;
      font-size: 20px;
      color: #222222;
      margin-bottom: 13px;
    }

    .everyOption {
      display: flex;
      align-items: center;
      margin-top: 12px;

      .optionName {
        height: 14px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #999999;
      }

      .optionValue {
        height: 14px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #333333;
      }
    }

    .buttonStyle {
      margin-top: 32px;
      margin-left: 55px;
      width: 220px;
      height: 50px;
      background: #21c9b8;
      box-shadow: 0px 3px 10px 0px rgba(33, 201, 184, 0.6);
      border-radius: 2px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 16px;
      color: #ffffff;
      text-align: center;
      line-height: 50px;
      cursor: pointer;
    }
  }
}

.card_center_line {
  width: 1px;
  height: 100%;
  background: #e1e1e1;
  margin-left: 51px;
  margin-right: 61px;
}

.card_right {
  width: 100%;
  overflow-y: auto;

  .content_title {
    display: flex;
    align-items: center;

    .icon {
      width: 4px;
      height: 20px;
      background: #21c9b8;
    }

    .title {
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 18px;
      color: #030a1a;
      margin-left: 10px;
    }
  }

  .content_desc {
    // width: 631px;
    // height: 159px;
    margin-top: 20px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    line-height: 24px;

    .imgList {
      display: flex;
      flex-wrap: wrap;

      .imgItem {
        width: 150px;
        height: 190px;
        margin-right: 6px;
        margin-bottom: 10px;
      }
    }
  }
}
</style>

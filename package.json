{"name": "ruoyi", "version": "3.6.1", "description": "易复材共享智造工业互联网平台", "author": "若依", "license": "MIT", "scripts": {"dev": "set NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service serve", "build:prod": "set NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service build", "build:stage": "set NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "repository": {"type": "git", "url": "https://gitee.com/y_project/RuoYi-Cloud.git"}, "dependencies": {"@riophae/vue-treeselect": "0.4.0", "@rongcloud/engine": "^5.7.8", "@rongcloud/imkit": "^5.6.5", "@rongcloud/imlib-next": "^5.7.8", "animate.css": "^4.1.1", "axios": "0.24.0", "clipboard": "2.0.8", "core-js": "3.25.3", "crypto-js": "^4.2.0", "echarts": "^5.4.0", "element-ui": "2.15.10", "file-saver": "2.0.5", "fuse.js": "6.4.3", "highlight.js": "9.18.5", "js-base64": "^3.7.5", "js-beautify": "1.13.0", "js-cookie": "3.0.1", "jsencrypt": "3.0.0-rc.1", "nprogress": "0.2.0", "quill": "1.3.7", "ramda": "^0.28.0", "screenfull": "5.0.2", "sortablejs": "1.10.2", "vue": "2.6.12", "vue-count-to": "1.0.13", "vue-cropper": "0.5.5", "vue-meta": "2.4.0", "vue-router": "3.4.9", "vue-video-player": "^5.0.1", "vuedraggable": "2.24.3", "vuex": "3.6.0", "wow.js": "^1.2.2", "wowjs": "^1.1.3"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.6", "@vue/cli-plugin-eslint": "4.4.6", "@vue/cli-service": "4.4.6", "babel-eslint": "10.1.0", "babel-plugin-dynamic-import-node": "2.3.3", "chalk": "4.1.0", "compression-webpack-plugin": "5.0.2", "connect": "3.6.6", "eslint": "7.15.0", "eslint-plugin-vue": "7.2.0", "lint-staged": "10.5.3", "runjs": "4.4.2", "sass": "1.32.13", "sass-loader": "10.1.1", "script-ext-html-webpack-plugin": "2.1.5", "svg-sprite-loader": "5.1.1", "vue-template-compiler": "2.6.12"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}
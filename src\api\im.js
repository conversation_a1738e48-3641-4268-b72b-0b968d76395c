/*
 * @Author: zhc
 * @Date: 2023-06-12 13:59:08
 * @LastEditTime: 2023-06-12 13:59:09
 * @Description:
 * @LastEditors: zhc
 */
/*
 * @Author: zhc
 * @Date: 2023-06-08 15:09:03
 * @LastEditTime: 2023-06-08 15:09:08
 * @Description:
 * @LastEditors: zhc
 */

import request from "../utils/request";

// 查询消息通知列表
export function listInfo(data) {
  return request({
    url: "/system/info/list",
    method: "get",
    data,
  });
}
//获取客服用户信息
export function getCustomerServicerInfo() {
  return request({
    url: "/system/im/getCustomerServicerInfo",
    method: "get",
  });
}

// 查询公告详细
export function getInfoDetail(id) {
  return request({
    url: "/system/info/getInfo",
    method: "get",
    data: { id },
  });
}

// 撤销加入企业的申请
export function revocationApply(id) {
  return request({
    url: `/system/info/exit-company?id=${id}`,
    method: "get",
  });
}

// 根据用户id列表获取用户信息
export function getUserListByIds(ids) {
  return request({
    url: "/system/im/getUserListByIds",
    method: "post",
    data: { ids: ids },
  });
}
// 根据用户昵称模糊查询聊天用户
export function getUserListByName(realName, ids) {
  return request({
    url: "/system/im/getUserListByName",
    method: "post",
    data: { realName: realName, ids: ids },
  });
}

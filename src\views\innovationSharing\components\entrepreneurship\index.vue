<template>
  <div class="content">
    <div class="content_banner">创业孵化</div>
    <div class="card-container">
      <div class="content_top">
        <div class="content_top_item" :class="currentMenu == item.dictValue ? 'active' : ''"
          v-for="(item, index) in deviceMenuList" :key="index" @click="handleClick(item.dictValue)">
          <div class="imgStyle">
            <img :src="currentMenu == item.dictValue ? item.hoverUrl : item.url" alt="" />
          </div>
          <div :class="currentMenu == item.dictValue ? 'titleActive' : 'title'">
            {{ item.dictLabel }}
          </div>
        </div>
      </div>
      <div class="content_bottom">
        <div class="card_left">
          <!-- 上半部分 -->
          <div class="card_left_top">
            <div class="imgStyle">
              <img style="width: 100%; height: 100%" :src="currentCategory.images ? currentCategory.images : defaultImg"
                alt="" />
            </div>
            <!-- <div class="imgContent">
              <div style="cursor: pointer">
                <img src="@/assets/device/icon_left.png" alt="" />
              </div>
              <div style="display: flex; align-items: center; margin: 0 10px">
                <div class="everyImgStyle" v-for="(item, index) in imgList" :key="index">
                  <img style="width: 100%; height: 100%" src="@/assets/entrepreneurship/defult_small.png" alt="" />
                </div>
              </div>
              <div style="cursor: pointer">
                <img src="@/assets/device/icon_right.png" alt="" />
              </div>
            </div> -->
          </div>
          <!-- 下半部分 -->
          <div class="card_left_bottom">
            <div class="title">{{ currentCategory.title }}</div>
            <div class="buttonStyle" @click="jumpIntention">
              {{ currentMenu == '2' ? "合作申请" : "入驻申请" }}
            </div>
          </div>
        </div>
        <!-- 中间 -->
        <div class="card_center_line"></div>
        <div class="card_right">
          <div>
            <div class="content_title">
              <div class="icon"></div>
              <div class="title">
                {{ currentMenu == '2' ? "研究院平台介绍" : currentMenu == '3' ? '产业园介绍' : "孵化基地介绍" }}
              </div>
            </div>
            <div class="content_desc" v-html='currentCategory.content'></div>
          </div>
          <div style="margin-top: 41px">
            <div class="content_title">
              <div class="icon"></div>
              <div class="title">已入驻企业</div>
            </div>
            <div class="content_desc company">
              <div class="desc-item" v-for="item in companyList" :key="item.id">{{ item.companyName }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { factoryListData } from "@/api/innovationSharing";
import { incubationInfoListData } from "@/api/innovationSharing";
import { listData } from "@/api/system/dict/data";

export default {
  name: "deviceSharing",
  data() {
    return {
      loading: false,
      pageNum: 1,
      pageSize: 999,
      total: 0,
      currentMenu: '1',
      deviceMenuList: [
        {
          url: require("@/assets/entrepreneurship/base.png"),
          hoverUrl: require("@/assets/entrepreneurship/base_active.png"),
        },
        {
          url: require("@/assets/entrepreneurship/cooperate.png"),
          hoverUrl: require("@/assets/entrepreneurship/cooperate_active.png"),
        },
        {
          url: require("@/assets/entrepreneurship/floor.png"),
          hoverUrl: require("@/assets/entrepreneurship/floor_active.png"),
        },
      ],
      imgList: [
        {
          id: 1,
        },
        {
          id: 2,
        },
        {
          id: 3,
        },
        {
          id: 4,
        },
        {
          id: 5,
        },
      ],
      companyList: [],
      categoryList: [
        {
          id: 1,
          title: "北京融创科技研究所",
          images: require("@/assets/entrepreneurship/defult.png"),
          content: '北京融创科技研究所是一个汇聚创新力量、赋能初创企业的综合性孵化平台，致力于为创业者提供从0到1的全周期成长支持。基地坐落于城市核心创新区，占地面积7万平方米，集开放式办公空间、独立工作室、多功能路演厅、共享实验室及休闲交流区于一体，打造兼具高效协作与人文关怀的创业生态圈。'
        },
        {
          id: 2,
          title: "先进复合材料检测中心",
          images: require("@/assets/entrepreneurship/defult.png"),
          content: '先进复合材料检测中心是一个以“共创、共享、共赢”为核心理念的资源整合与协同创新平台，致力于为企业、机构及个人提供高效、精准的合作对接服务。平台依托强大的行业资源网络与数字化技术，打破信息壁垒，构建起跨领域、跨区域的合作生态，助力合作伙伴实现资源互补、价值倍增。'
        },
        {
          id: 3,
          title: "北京融创科技研究所",
          images: require("@/assets/entrepreneurship/defult.png"),
          content: '产业园是一座集产业集聚、科技创新、生态宜居于一体的现代化产业园区，位于城市核心发展区域，总占地面积5万平方米，规划建筑面积9万平方米。园区以“产业赋能、创新驱动、绿色发展”为理念，致力于打造区域经济高质量发展的新引擎。'
        },
      ],
      currentCategory: {
        title: "北京融创科技研究所",
        images: require("@/assets/entrepreneurship/defult.png"),
        content: '北京融创科技研究所是一个汇聚创新力量、赋能初创企业的综合性孵化平台，致力于为创业者提供从0到1的全周期成长支持。基地坐落于城市核心创新区，占地面积7万平方米，集开放式办公空间、独立工作室、多功能路演厅、共享实验室及休闲交流区于一体，打造兼具高效协作与人文关怀的创业生态圈。'
      },
      defaultImg: require("@/assets/entrepreneurship/defult.png"),
    };
  },

  created() {
    this.getList();
    this.getIncubationInfoList()
    this.getCertificationType()
  },
  methods: {
    // 获取工厂列表
    async getList() {
      let res = await factoryListData({
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        factoryType: this.currentMenu,
      })
      if (res.code == 200) {
        this.companyList = res.rows;
      }
    },
    // 平台分类
    getCertificationType() {
      let params = { dictType: "entrepreneurship_type" };
      listData(params).then((response) => {
        if (response.code == 200) {
          this.deviceMenuList = response.rows.map((item, index) => {
            return {
              ...item,
              url: this.deviceMenuList[index].url,
              hoverUrl: this.deviceMenuList[index].hoverUrl,
            };
          })
        }
      });
    },
    // 获取孵化平台信息
    getIncubationInfoList() {
      let params = { type: this.currentMenu };
      incubationInfoListData(params).then(res => {
        if (res.code == 200) {
          this.categoryList = res.rows
          this.currentCategory = this.categoryList[0]
        }
      })
    },
    // 切换
    handleClick(value) {
      this.currentMenu = value;
      this.getIncubationInfoList()
      this.getList();
    },
    jumpIntention() {
      let userInfo = JSON.parse(sessionStorage.getItem("userinfo"));
      if (!userInfo?.memberCompanyName) {
        this.$confirm("您当前尚未关联企业，是否前往操作?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
          cancelButtonClass: "cancelButtonClass",
          confirmButtonClass: "customClass",
        })
          .then(() => {
            this.$router.push("/user/userCenter");
          })
          .catch(() => { });
        return;
      } else {
        this.$router.push(`/demandInterested?demandName=${this.currentCategory.title}&updateTime=${this.currentCategory.updateTime}&intentionType=11&fieldName=孵化平台&intentionId=${this.currentCategory.id}`);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  padding-bottom: 60px;
}

.content_banner {
  width: 100%;
  height: 300px;
  background-image: url("../../../../assets/release/banner.png");
  background-size: 100% 100%;
  font-family: Source Han Sans CN;
  font-weight: 500;
  font-size: 40px;
  color: #000;
  text-align: center;
  line-height: 300px;
}

.content_top {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 40px;

  .content_top_item {
    width: 290px;
    height: 68px;
    background: #ffffff;
    box-shadow: 0px 3px 20px 0px rgba(0, 0, 0, 0.08);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    margin-left: 26px;

    .imgStyle {
      width: 27px;
      height: 27px;
    }

    .title {
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 18px;
      color: #333333;
      margin-left: 20px;
    }

    .titleActive {
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 18px;
      color: #ffffff;
      margin-left: 20px;
    }
  }

  .content_top_item:nth-child(1) {
    margin-left: 0;
  }

  .active {
    background-color: #0cad9d;
  }
}

.content_bottom {
  margin-top: 40px;
  display: flex;
  padding: 60px 56px 54px 50px;
  height: 660px;


  .card_left {
    .card_left_top {
      .imgStyle {
        width: 330px;
        height: 230px;
        border-radius: 2px;
        margin-left: 10px;
      }

      .imgContent {
        margin-top: 15px;
        display: flex;
        align-items: center;

        .everyImgStyle {
          width: 54px;
          height: 50px;
          margin-left: 10px;
          cursor: pointer;
        }

        .everyImgStyle:nth-child(1) {
          margin-left: 0;
        }
      }
    }

    .card_left_bottom {
      margin-top: 30px;

      .title {
        font-family: Source Han Sans CN;
        font-weight: 500;
        font-size: 20px;
        color: #222222;
        margin-bottom: 13px;
      }

      .everyOption {
        display: flex;
        align-items: center;
        margin-top: 12px;

        .optionName {
          height: 14px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          font-size: 14px;
          color: #999999;
        }

        .optionValue {
          height: 14px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          font-size: 14px;
          color: #333333;
        }
      }

      .buttonStyle {
        margin-top: 32px;
        margin-left: 55px;
        width: 220px;
        height: 50px;
        background: #21c9b8;
        box-shadow: 0px 3px 10px 0px rgba(33, 201, 184, 0.6);
        border-radius: 2px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 16px;
        color: #ffffff;
        text-align: center;
        line-height: 50px;
        cursor: pointer;
      }
    }
  }

  .card_center_line {
    width: 1px;
    height: 100%;
    background: #e1e1e1;
    margin-left: 51px;
    margin-right: 61px;
  }

  .card_right {
    width: 100%;
    overflow-y: auto;

    .content_title {
      display: flex;
      align-items: center;

      .icon {
        width: 4px;
        height: 20px;
        background: #21c9b8;
      }

      .title {
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 18px;
        color: #030a1a;
        margin-left: 10px;
      }
    }

    .content_desc {
      // width: 631px;
      // height: 159px;
      margin-top: 20px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 14px;
      color: #666666;
      line-height: 24px;
    }

    .company {
      display: flex;
      align-items: center;
      justify-content: start;
      flex-wrap: wrap;


      .desc-item {
        margin-bottom: 10px;
        background: #E8F9F8;
        border-radius: 2px;
        width: 49%;
        height: 50px;
        font-weight: 400;
        font-size: 16px;
        color: #666666;
        line-height: 50px;
        text-align: center;
      }

      .desc-item:nth-child(2n) {
        margin-left: 2%;
      }
    }
  }

}
</style>

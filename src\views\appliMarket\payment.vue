<template>
  <div class="activity-container">
    <div class="order">
      <div class="step"><img src="../../assets/images/step2.jpg" /></div>
      <div class="order_t">
        <p><strong>请确认订单信息</strong></p>
        <p>您可在个人中心查看订单信息</p>
      </div>
      <div class="order_t2">
        <p><strong>订单信息</strong></p>
      </div>
      <div class="order_detail">
        <table border="0" class="tb_order">
          <tr>
            <td bgcolor="#de791b">
              <strong style="color: #fff">应用名称</strong>
            </td>
            <td bgcolor="#de791b">
              <strong style="color: #fff">应用编号</strong>
            </td>
            <td bgcolor="#de791b">
              <strong style="color: #fff">应用提供</strong>
            </td>
            <!-- <td bgcolor="#de791b">
              <strong style="color: #fff">规格</strong>
            </td> -->
            <td bgcolor="#de791b">
              <strong style="color: #fff">可用时长</strong>
            </td>
            <td bgcolor="#de791b">
              <strong style="color: #fff">可用人数</strong>
            </td>
            <td bgcolor="#de791b">
              <strong style="color: #fff">价格</strong>
            </td>
          </tr>
          <tr>
            <td>{{ orderData.remark }}</td>
            <td>{{ orderData.appCode }}</td>
            <td>{{ orderData.supply }}</td>
            <!-- <td>{{ orderData.spec == "1" ? "基础版" : "高级版" }}</td> -->
            <td>{{ orderData.validTime == "1" ? "一年" : "永久" }}</td>
            <td>不限</td>
            <td>￥{{ orderData.price }}</td>
          </tr>
        </table>
      </div>
      <div class="order_t2">
        <p><strong>支付选择</strong></p>
      </div>
      <div>
        <el-radio v-model="payRadio" label="1">线上支付</el-radio>
        <el-radio v-model="payRadio" label="2">线下支付</el-radio>
      </div>
      <div v-show="payRadio == '1'">
        <div class="order_t2">
          <p><strong>选择支付平台</strong></p>
        </div>
        <div class="zhifu_list">
          <a href="javascript:void(0)" class="selected11"
            ><img src="../../assets/images/wechart.jpg"
          /></a>
        </div>
      </div>
      <div v-show="payRadio == '2'">
        <div class="order_t2">
          <p style="font-size: 18px">
            转账信息(请按照以下信息进行转账汇款操作)
          </p>
          <p>税号: 91370212MA3ER1RQXE</p>
          <p>注册地址: 山东省青岛市市北区敦化路119号1802室</p>
          <p>电话: 0532-88897900</p>
          <p>银行账号: 532907425710601</p>
          <p>开户行: 招商银行青岛崂山支行</p>
        </div>
      </div>
      <div class="lijizhifu">
        <a href="javascript:void(0)" @click="cancel">取消</a
        ><a href="javascript:void(0)" class="selected12" @click="zhifu"
          >立即支付</a
        >
      </div>
    </div>

    <!--购买弹窗-->
    <div class="tishi_bg" v-if="showzf">
      <div class="zhifu_bg">
        <div class="goumai_t">
          <span
            ><a href="javascript:void(0)" @click="showzf = false"
              ><img
                style="margin-top: 12px"
                src="../../assets/images/close2.png" /></a></span
          >微信扫码支付
        </div>
        <div class="goumai_c" style="padding-top: 0">
          <div class="goumai_total">
            <div class="goumai_total_l">
              <p>
                支付:<strong>￥{{ orderData.price }}</strong>
              </p>
              <p>请于2小时内支付</p>
            </div>
          </div>
          <div class="qrcode">
            <div id="myqrcode"></div>
          </div>
          <!-- <div class="zhifuewm">
            <img src="../../assets/images/erweima3.png" />
          </div> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { orderDetail } from "@/api/system/user";
import { orderPayment } from "@/api/appliMarket";

export default {
  data() {
    return {
      showLogin: false,
      userinfo: [],
      token: "",
      detail: [],
      cmsList: [],
      id: "",
      orderData: {},
      showzf: false,
      payRadio: "1",
      timer: null,
    };
  },
  created() {
    this.getOrderData();
  },
  methods: {
    getOrderData() {
      let id = this.$route.query.id;
      orderDetail(id).then((res) => {
        if (res.code === 200) {
          this.orderData = res.data;
        }
      });
    },
    zhifu() {
      if (this.payRadio == "1") {
        let data = {
          id: this.orderData.id,
          appId: this.orderData.appId,
          price: this.orderData.price,
          totalAmount: this.orderData.totalAmount,
          spec: this.orderData.spec,
          validTime: this.orderData.validTime,
          phone: this.orderData.phone,
          remark: this.orderData.remark,
        };
        orderPayment(data).then((res) => {
          if (res.code === 200) {
            console.log(res, "下单返回----------------");
            this.showzf = true;
            setTimeout(() => {
              let qr = new QRCode(document.getElementById("myqrcode"), {
                text: res.data.codeUrl,
              });
              qr._el.title = "";
            }, 500);
            //3秒轮循环判断是否支付成功
            this.timer = setInterval(() => {
              this.getOrder();
            }, 3000);
          }
        });
      }
    },
    getOrder() {
      let id = this.orderData.id;
      orderDetail(id).then((res) => {
        if (res.code === 200) {
          console.log(res.data.orderStatus, "--------");
          if (res.data.orderStatus == "2") {
            clearInterval(this.timer);
            this.$router.push({
              path: "/paySuccess",
              query: {
                price: res.data.price,
              },
            });
          }
        }
      });
    },
    cancel() {
      this.$router.go(-1);
    },
  },
};
</script>

<style lang="scss" scoped>
.order {
  width: 1000px;
  margin-top: 0;
  margin-right: auto;
  margin-bottom: 0;
  margin-left: auto;
  padding-bottom: 50px;
  min-height: 500px;
  .step {
    text-align: center;
    padding-top: 20px;
    width: 100%;
    padding-bottom: 20px;
  }
  .order_t {
    font-size: 14px;
    background-image: url("../../assets/images/step.png");
    background-repeat: no-repeat;
    background-position: left center;
    padding-left: 75px;
  }

  .order_t strong {
    font-size: 18px;
  }
  .order_t2 {
    padding-top: 30px;
  }
  table.tb_order {
    padding: 0px;
    border-collapse: collapse;
    width: 100%;
    border-top-width: 1px;
    border-right-width: 1px;
    border-bottom-width: 0px;
    border-left-width: 1px;
    border-top-style: solid;
    border-right-style: solid;
    border-bottom-style: solid;
    border-left-style: solid;
    border-top-color: #eee;
    border-right-color: #eee;
    border-bottom-color: #eee;
    border-left-color: #eee;
    margin-top: 15px;
    margin-right: 0px;
    margin-bottom: 0px;
    margin-left: 0px;
    color: #fff;
  }

  table.tb_order tr {
    margin: 0px;
    padding: 0px;
  }

  table.tb_order tr td {
    margin: 0px;
    padding: 0px;
    text-align: center;
    border-top-width: 0px;
    border-right-width: 0px;
    border-bottom-width: 1px;
    border-left-width: 0px;
    border-top-style: solid;
    border-right-style: solid;
    border-bottom-style: solid;
    border-left-style: solid;
    border-top-color: #eee;
    border-right-color: #eee;
    border-bottom-color: #eee;
    border-left-color: #eee;
    line-height: 50px;
    color: #333;
  }

  .zhifu_list {
    margin: 0px;
    padding-top: 15px;
    padding-right: 0px;
    padding-bottom: 0px;
    padding-left: 0px;
  }

  .zhifu_list a {
    border: 1px solid #eee;
    // width: 150px;
    display: inline-block;
    padding-top: 0px;
    padding-right: 15px;
    padding-bottom: 0px;
    padding-left: 15px;
    margin-top: 0px;
    margin-right: 15px;
    margin-bottom: 0px;
    margin-left: 0px;
  }

  .selected11 {
    border: 1px solid #de791b !important;
  }

  .lijizhifu {
    padding-top: 15px;
    text-align: right;
  }

  .lijizhifu a {
    display: inline-block;
    border: 1px solid #eee;
    line-height: 40px;
    padding-top: 0px;
    padding-right: 25px;
    padding-bottom: 0px;
    padding-left: 25px;
    margin-left: 10px;
  }

  .lijizhifu a:hover,
  .selected12 {
    color: #fff;
    background-color: #de791b;
  }
}

/*提示*/

.tishi_bg {
  position: fixed;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.6);
  z-index: 100;
  top: 0;
  left: 0;
  .zhifu_bg {
    width: 400px;
    position: relative;
    margin-top: 8%;
    margin-right: auto;
    margin-bottom: 0;
    margin-left: auto;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0px 0px 4px 0px rgb(51 51 51 / 20%);
    padding: 0px;
    overflow: hidden;
  }
  .goumai_t {
    background-color: #eee;
    color: #333;
    font-size: 18px;
    line-height: 50px;
    padding-right: 30px;
    padding-left: 30px;
  }

  .goumai_t span {
    float: right;
  }

  .goumai_t span img {
    width: 25px;
  }
  .zhifuewm {
    width: 250px;
    padding: 10px;
    margin-top: 15px;
    margin-right: auto;
    margin-bottom: 0;
    margin-left: auto;
    border: 1px solid #ccc;
  }

  .zhifuewm img {
    width: 100%;
  }
  .goumai_c {
    padding: 30px;
  }
  .goumai_total {
    padding-top: 15px;
    display: flex;
    flex-flow: row wrap;
    justify-content: space-between;
  }

  .goumai_total_l {
    width: 60%;
  }

  .goumai_total_l strong {
    color: #f00;
    font-size: 24px;
  }
  .qrcode {
    display: flex;
    justify-content: center;
  }
}
</style>

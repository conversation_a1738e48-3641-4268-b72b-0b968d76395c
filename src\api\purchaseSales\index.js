/*
 * @Author: jhy
 * @Date: 2023-01-30 17:58:37
 * @LastEditors: JHY
 * @LastEditTime: 2023-12-11 14:09:59
 */
import request from "@/utils/request";

// 采销互联--需求
export function getDemandList(params) {
  return request({
    url: "/system/demand/secret/list",
    method: "get",
    params,
  });
}

// 采销互联--资源
export function getSupplyList(params) {
  return request({
    url: "/system/supply/secret/list",
    method: "get",
    params,
  });
}

//  商机需求
export function gatewayDemendListTen(params) {
  return request({
    // url: "/system/demand/secret/gatewayList",
    url: "/system/demand/gatewayListTen",
    method: "get",
    params,
  });
}

//  采销互联--找需求
export function gatewayDemandListShow(params) {
  return request({
    url: "/system/demand/gatewayListShow",
    method: "get",
    params,
  });
}

// 需求详情
export function getDemandDetail(id) {
  return request({
    url: "/system/demand/secret/" + id,
    method: "get",
  });
}

// 资源大厅
export function getResourceHallList(params) {
  return request({
    // url: "/system/supply/secret/gatewayList",
    url: "/system/supply/gatewayListTen",
    method: "get",
    params,
  });
}

// 采销互联--找资源
export function gatewaySupplyListShow(params) {
  return request({
    url: "/system/supply/gatewayListShow",
    method: "get",
    params,
  });
}

// 资源详情
export function getSupplyDetail(id) {
  return request({
    url: "/system/supply/secret/" + id,
    method: "get",
  });
}

// 是否对此资源提交过意向
export function getCheckSubmit(params) {
  return request({
    url: "/system/interactRecord/check-submit",
    method: "get",
    params,
  });
}

// 采销互联--推荐企业名录
export function getCompanyHomeList(params) {
  return request({
    // url: "/system/company/mag/getCompanylist_home",
    url: "/system/company/mag/secret/getCompanylist_cxhl_list",
    method: "get",
    params,
  });
}

// 采销互联--推荐企业列表
export function getCompanyListLb(params) {
  return request({
    url: "/system/company/mag/secret/getCompanylist_cxhl_lb",
    method: "get",
    params,
  });
}

// 企业详情
export function getCompanyDetail(params) {
  return request({
    url: "/system/company/mag/detailShow",
    method: "get",
    params,
  });
}

// 采销互联--专家智库列表
export function getExpertList(params) {
  return request({
    url: "/system/expert/listTen",
    method: "get",
    params,
  });
}

// 采销互联--专家智库
export function getExpertListFour(params) {
  return request({
    url: "/system/expert/listFour",
    method: "get",
    params,
  });
}

// 专家详情
export function getExpertDetail(params) {
  return request({
    // url: "/system/expert/detail",
    url: "/system/expert/detailShow",
    method: "get",
    params,
  });
}

// 采销互联--活动广场列表
export function getActivityList(params) {
  return request({
    url: "/system/activity-portal/list",
    method: "get",
    params,
  });
}

// 活动详情
export function getActivityDetail(params) {
  return request({
    url: "/system/activity-portal/detail",
    method: "get",
    params,
  });
}

// 活动详情--立即报名
export function addActivityEnroll(data) {
  return request({
    url: "/system/activity-enroll/add",
    method: "post",
    data,
  });
}

// 设备列表
export function insList(data) {
  return request({
    url: "/system/web/tyInstrument/list",
    method: "get",
    params: data,
  });
}

// 设备详情
export function insDetail(id) {
  return request({
    url: `/system/web/tyInstrument/${id}`,
    method: "get",
  });
}

// 实验室列表
export function laboratoryList(data) {
  return request({
    url: "/system/web/wq_lab/list",
    method: "get",
    params: data,
  });
}

// 实验室详情
export function laboratoryDetail(id) {
  return request({
    url: `/system/web/wq_lab/${id}`,
    method: "get",
  });
}

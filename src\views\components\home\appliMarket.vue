<template>
  <div
    class="overal wow animate__animated animate__fadeInUp"
    data-wow-duration="1s"
  >
    <div class="card-container">
      <div class="enterpriseTitle">
        <div>应用市场</div>
        <!-- <div class="allEnterprise" @click="goAppli">查看全部>></div> -->
      </div>
      <div class="content">
        <div
          class="contentItem"
          v-for="(item, index) in data"
          :key="index"
          @click="goPurchaseapps(item.appStoreId)"
        >
          <span class="bottom"></span>
          <span class="right"></span>
          <span class="top"></span>
          <span class="left"></span>
          <div class="imgbox">
            <img :src="item.appStoreImg" alt="" />
          </div>
          <div class="nameStyle">
            {{ item.appStoreName }}
          </div>
          <div class="detail">查看详情 ></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { appData } from "@/api/home";

export default {
  data() {
    return {
      loading: false,
      data: [],
      pageNum: 1,
      pageSize: 6,
      total: 0,
    };
  },
  created() {
    this.getListData();
  },
  methods: {
    getListData() {
      let params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
      };
      appData(params).then((res) => {
        if (res.code === 200) {
          console.log(res, "7777777777777777777");
          this.data = res.rows;
        }
      });
    },
    // 跳转应用购买页面
    goPurchaseapps(id) {
      this.$router.push({
        path: "/appStoreInfo",
        query: { id },
      });
    },
    goAppli() {
      let routeData = this.$router.resolve({
        path: "/appliMarket",
      });
      window.open(routeData.href, "_blank");
    },
  },
};
</script>

<style lang="scss" scoped>
.overal {
  width: 100%;
  margin-bottom: 62px;

  // height: 760px;
  // background-image: url("../../../assets/images/home/<USER>");
  // background-size: 100% 100%;
}
.enterpriseTitle {
  width: 100%;
  font-size: 36px;
  text-align: center;
  margin: 61px 0 60px 0;
  position: relative;
  font-family: Source Han Sans CN;
  font-weight: 500;
  color: #000000;
  .allEnterprise {
    position: absolute;
    top: 8 px;
    right: 0;
    font-size: 16px;
    font-family: Source Han Sans CN;
    font-weight: 500;
    color: #21c9b8;
    line-height: 26px;
    cursor: pointer;
  }
}
.content {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  width: 100%;
  .contentItem {
    width: 380px;
    height: 350px;
    cursor: pointer;
    box-shadow: 0px 4px 20px 0px rgba(90, 90, 90, 0.1);
    border-radius: 4px;
    transition: all 0.35s ease-in;
    position: relative;
    span {
      position: absolute;
      z-index: 1;
      background-color: #37c9b8;
      transition: transform 0.5s ease;
    }
    .bottom,
    .top {
      height: 2px;
      left: -1px;
      right: -1px;
      transform: scaleX(0);
    }
    .left,
    .right {
      width: 2px;
      top: -1px;
      bottom: -1px;
      transform: scaleY(0);
    }
    .bottom {
      bottom: -1px;
      transform-origin: bottom right;
    }
    .right {
      right: -1px;
      transform-origin: top right;
    }
    .top {
      top: -1px;
      transform-origin: top left;
    }
    .left {
      left: -1px;
      transform-origin: bottom left;
    }
    .imgbox {
      overflow: hidden;
      position: relative;
      height: 250px;
      img {
        height: 100%;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        object-fit: cover;
        object-position: center;
      }
    }

    .nameStyle {
      height: 19px;
      font-family: Source Han Sans CN;
      font-weight: 500;
      font-size: 20px;
      color: #333333;
      margin-top: 22px;
      margin-left: 30px;
    }
    .detail {
      height: 14px;
      font-family: Source Han Sans CN;
      transition: color 0.35s ease-in;
      font-weight: 400;
      font-size: 14px;
      color: #999999;
      margin-left: 29px;
      margin-top: 19px;
    }
  }
  .contentItem:nth-child(n + 4) {
    margin-top: 30px;
  }
  .contentItem:hover {
    box-shadow: 0px 4px 20px 0px rgba(227, 248, 246, 1);
    .detail {
      color: #21c9b8;
    }
    .top {
      transform-origin: top right;
      transform: scaleX(1);
    }
    .left {
      transform-origin: top left;
      transform: scaleY(1);
    }
    .bottom {
      transform-origin: bottom left;
      transform: scaleX(1);
    }
    .right {
      transform-origin: bottom right;
      transform: scaleY(1);
    }
  }
}
</style>

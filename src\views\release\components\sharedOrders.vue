<template>
  <div>
    <el-form ref="form" :rules="rules" :model="form" label-position="top">
      <div class="title">基本信息</div>
      <div class="titleLine"></div>
      <el-form-item label="需求企业">
        <el-input disabled v-model="form.demandCompany" placeholder="请先绑定公司"></el-input>
      </el-form-item>
      <el-form-item label="联系人">
        <el-input disabled v-model="form.contactPerson" placeholder="请先维护联系人"></el-input>
      </el-form-item>
      <el-form-item label="联系电话">
        <el-input disabled v-model="form.contactPhone" placeholder="请先维护联系方式"></el-input>
      </el-form-item>
      <el-form-item label="交货地址">
        <el-input v-model="form.deliveryAddress" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="交货要求">
        <el-input v-model="form.fileRequirement" placeholder="请输入"></el-input>
      </el-form-item>
      <!-- <el-form-item label="开户行">
        <el-input v-model="form.bankName" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="付款账号">
        <el-input v-model="form.paymentAccount" maxlength="50" show-word-limit placeholder="请输入"></el-input>
      </el-form-item> -->
      <!-- <el-form-item label="要求完成时间" prop="technologyType">
        <el-date-picker
          v-model="form.date"
          type="date"
          placeholder="选择日期"
          value-format="yyyy-MM-dd"
          style="width: 100%"
        />
      </el-form-item> -->
      <!-- <el-form-item label="上传附件" prop="enclosure">
        <FileUpload v-model="form.enclosure" />
      </el-form-item> -->
      <div class="title">订单明细</div>
      <div class="titleLine"></div>
      <el-form-item label="需求物料">
        <div slot="label">
          <div style="display: flex; width: 1080px">
            <div>需求物料</div>
            <div class="addStyle" @click="addMaterial">新增行</div>
          </div>
        </div>
        <el-table :data="jobList">
          <el-table-column label="物料名称" align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.name"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="规格型号" align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.modelNumber"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="数量" align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.quantity" type="number"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="单位" align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.unit"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="可承接量" align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.capacity"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center">
            <template slot-scope="scope">
              <el-button type="text" icon="el-icon-delete" @click="deleteMaterial(scope.$index)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
      <el-row>
        <el-col :span="12">
          <el-form-item label="拦标价" style="width: 80%">
            <el-input v-model="form.price" placeholder="请输入" @input="handleInput">
              <template slot="suffix"> /元 </template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否允许拼单">
            <el-radio-group v-model="form.status">
              <el-radio v-for="dict in isJointOrderList" :key="dict.value" :label="dict.value" :value="dict.value">{{
                dict.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- <el-form-item label="可承接量">
        <el-input v-model="form.title" placeholder="请输入"></el-input>
      </el-form-item> -->
      <el-row>
        <el-col :span="12">
          <el-form-item label="需求截至时间" prop="deadline" style="width: 80%">
            <el-date-picker v-model="form.deadline" type="date" placeholder="选择日期" value-format="yyyy-MM-dd"
              style="width: 100%" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="交付时间" style="width: 80%">
            <el-date-picker v-model="form.bankName" type="date" placeholder="选择日期" value-format="yyyy-MM-dd"
              style="width: 100%" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item class="footer-submit">
        <el-button type="primary" @click="onSubmit">发布</el-button>
        <el-button style="margin-left: 140px" @click.once="onCancel">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import { shareOrdersAdd } from "@/api/release/index";

export default {
  data() {
    return {
      form: {
        demandCompany: "", // 公司
        contactPerson: "",
        contactPhone: "",
        deliveryAddress: "", // 交货地址
        fileRequirement: "", // 交货要求
        bankName: "", // 开户行
        paymentAccount: "", // 付款账号
        materials: [],
        deadline: "", // 需求截至时间
        status: "0", // 订单状态（0 未接单/ 1进行中/ 2已完成）
        auditStatus: "0",
      },
      // 表单校验
      rules: {
        demandCompany: [
          { required: true, message: "请先绑定公司", trigger: "blur" },
        ],
        deadline: [
          { required: true, message: "请选择需求截至时间", trigger: "change" },
        ]
        // demandType: [
        //   { required: true, message: "请选择需求类型", trigger: "change" },
        // ],
        // applicationArea: [
        //   { required: true, message: "请选择应用领域", trigger: "change" },
        // ],
        // displayRestrictions: [
        //   { required: true, message: "请选择展示限制", trigger: "change" },
        // ],
        // summary: [
        //   { required: true, message: "需求描述不能为空", trigger: "blur" },
        // ],
        // contactPerson: [
        //   { required: true, message: "联系人不能为空", trigger: "blur" },
        // ],
        // companyName: [
        //   { required: true, message: "公司名称不能为空", trigger: "blur" },
        // ],
        // contactsMobile: [
        //   { required: true, message: "联系电话不能为空", trigger: "blur" },
        // ],
      },
      jobList: [
        {
          name: "",
          modelNumber: "",
          quantity: "",
          unit: "",
          capacity: "",
        },
      ],
      isJointOrderList: [
        {
          label: "否",
          value: "0",
        },
        {
          label: "是",
          value: "1",
        },
      ],
    };
  },
  created() {
    let userInfo = JSON.parse(sessionStorage.getItem("userinfo"));
    this.form.demandCompany = userInfo.memberCompanyName;
    this.form.contactPerson = userInfo.memberRealName;
    this.form.contactPhone = userInfo.memberPhone;
  },
  methods: {
    onSubmit() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.form.materials = this.jobList;
          shareOrdersAdd(this.form).then((res) => {
            if (res.code === 200) {
              this.$message.success("发布成功");
              this.$router.go(-1);
            }
          });
        }
      });
    },
    onCancel() {
      this.$router.go(-1);
    },
    addMaterial() {
      this.jobList.push({
        name: "",
        modelNumber: "",
        quantity: "",
        unit: "",
        capacity: "",
      });
    },
    handleInput(value) {
      // 只允许数字和小数点
      this.form.price = value.toString().replace(/[^0-9.]/g, "");
    },
    deleteMaterial(index) {
      this.jobList.splice(index, 1);
    }
  },
};
</script>
<style lang="scss" scoped>
.title {
  font-family: Source Han Sans CN;
  font-weight: 500;
  font-size: 18px;
  color: #21c9b8;
}

.titleLine {
  width: 100%;
  height: 1px;
  background: #21c9b8;
  margin: 20px 0 30px 0;
}

.addStyle {
  font-family: Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #21c9b8;
  margin-left: auto;
  cursor: pointer;
}

.footer-submit {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin-top: 60px;

  .el-button {
    width: 140px;
    height: 50px;
  }
}
</style>

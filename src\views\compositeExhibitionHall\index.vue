<template>
  <div class="content">
    <div class="left">
      <div class="hall-bg">
        <img class="hall-img" :src="currentItem.url" alt="" />
        <div class="card">
          <div class="card-top">
            <div class="card-left">
              <img class="card-img" :src="currentItem.imageUrl" alt="" />
              <el-button type="primary" :icon="videoIcon" @click="handlePlayAudio">播放音频</el-button>
            </div>
            <div class="card-right">
              <div class="card-title">{{ currentItem.materialName }}</div>
              <div class="card-content">{{ currentItem.description }}</div>
            </div>
          </div>
          <div class="card-bottom" v-if="materialList.length > 0">
            <div class="card-item" v-for="(item, index) in materialList" @click="toFactory(item)" :key="index">
              <div class="card-item-title">{{ item.productName }}</div>
              <img :src="item.productImageUrl" alt="">
            </div>
          </div>
          <div class="card-empty" v-else>
            <el-empty description="暂无数据"></el-empty>
          </div>
        </div>
      </div>
    </div>
    <div class="right">
      <div class="hall-list">
        <div class="hall-item" v-for="(item, index) in deviceMenuList" @click="handleClick(item)" :key="index">
          <img :src="item.url" alt="" />
          <div class="hall-title">{{ item.materialName }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { getCompositeExhibitionHallList } from '@/api/compositeExhibitionHall'
import { listSysProduct } from "@/api/manufacturingSharing";

export default {
  name: "deviceSharing",
  data() {
    return {
      loading: false,
      pageNum: 1,
      pageSize: 10,
      total: 0,
      bgUrl: [
        require("@/assets/compositeExhibitionHall/hall1.png"),
        require("@/assets/compositeExhibitionHall/hall2.png"),
        require("@/assets/compositeExhibitionHall/hall3.png"),
        require("@/assets/compositeExhibitionHall/hall4.png"),
      ],
      deviceMenuList: [
        {
          url: require("@/assets/compositeExhibitionHall/hall1.png"),
        },
        {
          url: require("@/assets/compositeExhibitionHall/hall2.png"),
        },
        {
          url: require("@/assets/compositeExhibitionHall/hall3.png"),
        },
        {
          url: require("@/assets/compositeExhibitionHall/hall4.png"),
        },
        {
          url: require("@/assets/compositeExhibitionHall/hall1.png"),
        },
        {
          url: require("@/assets/compositeExhibitionHall/hall2.png"),
        }, {
          url: require("@/assets/compositeExhibitionHall/hall3.png"),
        }, {
          url: require("@/assets/compositeExhibitionHall/hall4.png"),
        },
      ],
      currentItem: {
        url: require("@/assets/compositeExhibitionHall/hall1.png"),
        imageUrl: require("@/assets/compositeExhibitionHall/hall_img.png"),
        materialName: "化工环保展厅",
        description: '暂无介绍',
      },
      videoIcon: 'el-icon-video-play',
      isPlay: false,
      materialList: [
        {
          productName: '玻璃钢槽式电缆桥架',
          productImageUrl: require("@/assets/compositeExhibitionHall/material1.png"),
        },
        {
          productName: '玻璃钢格栅',
          productImageUrl: require("@/assets/compositeExhibitionHall/material2.png"),
        },
        {
          productName: '钢筋钢板',
          productImageUrl: require("@/assets/compositeExhibitionHall/material3.png"),
        },
        {
          productName: '玻璃钢管道',
          productImageUrl: require("@/assets/compositeExhibitionHall/material4.png"),
        }
      ],
      productId: '',
    };
  },
  created() {
    this.productId = this.$route.query.productId ? this.$route.query.productId : '';
    this.getList();
  },
  methods: {
    getList() {
      this.loading = true;
      let params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
      };
      getCompositeExhibitionHallList(params).then((response) => {
        if (response.code === 200 && response.rows.length > 0) {
          this.deviceMenuList = response.rows;
          this.deviceMenuList.sort((a, b) => a.sortOrder - b.sortOrder);
          // 按顺序循环bgUrl
          this.deviceMenuList.forEach((item, index) => {
            item.url = this.bgUrl[index % this.bgUrl.length];
          })
          if (this.productId) {
            getCompositeExhibitionHallList({
              pageNum: 1,
              pageSize: 1000,
              productId: this.productId,
            }).then((res) => {
              if (res.code === 200 && res.rows.length > 0){
                let index = this.deviceMenuList.findIndex(item => item.id === res.rows[0].id);
                this.handleClick(this.deviceMenuList[index]);
              }
            })
          }else{
            this.handleClick(this.deviceMenuList[0]);
          }
          // this.handleClick(this.deviceMenuList[0]);
        }
      });
    },
    handleClick(item) {
      console.log(item, 'item')
      this.currentItem.url = item.url;
      this.currentItem.materialName = item.materialName;
      this.currentItem.imageUrl = item.imageUrl ? item.imageUrl : require("@/assets/compositeExhibitionHall/hall_img.png");
      this.currentItem.description = item.description ? item.description : '暂无介绍';
      // if (item.imageList) {
      //   this.materialList = item.imageList.split(',').map(item => {
      //     let name = item.split('/')[item.split('/').length - 1].split('_');
      //     name.pop();
      //     return {
      //       name: name.join('_'),
      //       img: item
      //     }
      //   })
      // } else {
      //   this.materialList = []
      // }
      listSysProduct({
        exhibitionHallId: item.id,
        pageNum: 1,
        pageSize: 1000,
      }).then((res) => {
        if (res.code === 200) {
          this.materialList = res.rows || [];
        }
      });
    },
    handlePlayAudio() {
      if (this.isPlay) {
        this.videoIcon = 'el-icon-video-play'
        this.isPlay = false
      } else {
        this.videoIcon = 'el-icon-video-pause'
        this.isPlay = true
      }
    },
    toFactory(item) {
      this.$router.push({
        path: '/manufacturingSharing',
        query: {
          index: 2,
          page: 2,
          productId: item.productId,
        }
      })
    }
  },
};
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  height: auto;
  padding: 40px;
  box-sizing: border-box;
  background-color: #F7FBFF;
  display: flex;
  justify-content: space-between;

  .left {
    width: 80%;
    position: relative;

    .hall-img {
      width: 100%;
    }

    .card {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background-color: #F7FBFF;
      border-radius: 10px;
      padding: 40px;
      box-sizing: border-box;
      width: 84%;
      height: 74%;

      .card-top {
        height: 52%;
        display: flex;
        justify-content: space-between;

        .card-left {
          width: 29%;

          .card-img {
            width: 100%;
            margin-bottom: 1vw;
          }
        }

        .card-right {
          width: 65%;

          .card-title {
            font-weight: 500;
            font-size: 1.1vw;
            color: #222222;
            margin-bottom: 0.8vw;
          }

          .card-content {
            font-weight: 400;
            font-size: 0.7vw;
            line-height: 1vw;
            color: #787878;
            overflow-y: auto;
            box-sizing: border-box;
            height: 90%;
            overflow: auto;
          }
        }
      }

      .card-bottom {
        height: 48%;
        margin-top: 2vw;
        display: flex;
        justify-content: flex-start;

        .card-item {
          width: 22%;
          margin: 0.5vw;
          margin-right: 1.3vw;

          .card-item-title {
            width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            font-weight: 400;
            font-size: 0.9vw;
            color: #222222;
            margin-bottom: 14px;
          }

          img {
            width: 100%;
            height: 75%;
          }
        }
      }

      .card-empty {
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }

  .right {
    width: 18%;
    height: 46vw;
    overflow-y: auto;
    background-color: transparent;

    .hall-list {
      width: 100%;

      .hall-item {
        width: 100%;
        position: relative;
        cursor: pointer;

        img {
          width: 100%;
          margin-bottom: 1.5vw;
        }

        .hall-title {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          font-weight: 500;
          font-size: 1.1vw;
          color: #000000;
        }
      }
    }
  }
}
</style>

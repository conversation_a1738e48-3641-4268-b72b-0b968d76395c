<template>
  <div>
    <demandHall v-if="currentIndex == 0"></demandHall>
    <supplyHall v-if="currentIndex == 1"></supplyHall>
  </div>
</template>
<script>
import demandHall from "./components/demandHall";
import supplyHall from "./components/supplyHall";
export default {
  name: "supplyDemandDocking",
  components: { demandHall, supplyHall },
  data() {
    return {
      currentIndex: 0,
    };
  },
  watch: {
    "$route.query.index": {
      handler(val) {
        this.currentIndex = val || 0;
      },
      deep: true,
    },
  },

  methods: {},
  created() {
    this.currentIndex = this.$route.query.index || 0;
  },
};
</script>
<style></style>

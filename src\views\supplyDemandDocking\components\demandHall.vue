<template>
  <div class="content">
    <div class="content_banner">
      <div style="height: 37px">需求大厅</div>
      <div style="height: 33px; margin-top: 21px">Demand Hall</div>
      <div class="activity-title-content">
        <div class="activity-search-box">
          <el-form ref="form" class="activity-search-form">
            <el-form-item>
              <el-input v-model="keywords" placeholder="请输入搜索内容" class="activity-search-input">
                <el-button slot="append" class="activity-search-btn" @click="onSearch">搜索</el-button>
              </el-input>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
    <div class="appliType">
      <div class="everyType" v-for="(item, index) in appliTypeData" :key="item.dictValue"
        @click="getappliData(item.dictValue)" @mouseenter="handleMouseEnter(index)"
        @mouseleave="handleMouseLeave(index)">
        <!-- <div class="everyImg"> -->
        <div class="long_l" :id="`rock_${index}`" :style="{
          'background-image': `url(${appliTypeImgList[index].url})`,
        }"></div>
        <!-- <img
            :id="`rock_${index}`"
            :src="appliTypeImgList[index].url"
            alt=""
          /> -->
        <!-- </div> -->
        <div class="everyTitle">{{ item.dictLabel }}</div>
        <div class="everyIcon" v-show="flag === item.dictValue"></div>
      </div>
    </div>
    <!-- 底部内容 -->
    <div class="card-container" v-loading="loading">
      <div class="content_bottom" v-if="demandList && demandList.length > 0 && flag != '-1'">
        <div class="content_bottom_item" v-for="(item, index) in demandList" :key="index" @click="goDetail(item.id)">
          <div class="detailTitle tr2 textOverflow1">
            {{ item.title }}
          </div>
          <div class="demandChunk">
            <!-- 左侧图片 -->
            <div>
              <img style="width: 130px; height: 130px" :src="getImage(item)" alt="" />
            </div>
            <!-- 右侧内容 -->
            <div class="demand_right">
              <div class="demandTopRightflex">
                <div class="detailrightTitle tr2">需求方：</div>
                <div class="detailrightContent tr2 textOverflow1">
                  {{ item.companyName }}
                </div>
              </div>
              <!-- <div class="demandTopRightflex">
                <div class="detailrightTitle">应用领域：</div>
                <div class="detailrightContent textOverflow1">
                  {{ item.applicationAreaName }}
                </div>
              </div> -->
              <!-- <div class="detailrightTitle2 textOverflow2">
                {{ item.desc }}
              </div> -->
              <div class="demandTopRightflex">
                <div class="detailrightTitle tr2">需求类型：</div>
                <div class="detailrightContent tr2 textOverflow1">
                  {{ getType(item.type) }}
                </div>
              </div>
              <div class="demandTopRightflex">
                <div class="detailrightTitle tr2">发布时间：</div>
                <div class="detailrightContent tr2 textOverflow1">
                  {{ item.createTime }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="content_bottom" v-else-if="detectionList && detectionList.length > 0 && flag == '-1'">
        <div class="content_bottom_item" v-for="(item, index) in detectionList" :key="index"
          @click="goDetectionDetail(item.id)">
          <div class="detailTitle textOverflow1">
            {{ item.testingContent }}
          </div>
          <div class="demandChunk">
            <!-- 左侧图片 -->
            <div>
              <img style="width: 130px; height: 130px" :src="getImage(item)" alt="" />
            </div>
            <!-- 右侧内容 -->
            <div class="demand_right">
              <div class="demandTopRightflex">
                <div class="detailrightTitle tr2">需求方：</div>
                <div class="detailrightContent tr2 textOverflow1">
                  {{ item.companyName }}
                </div>
              </div>
              <div class="demandTopRightflex">
                <div class="detailrightTitle tr2">检测内容：</div>
                <div class="detailrightContent textOverflow1">
                  {{ item.testingRequirements }}
                </div>
              </div>
              <div class="demandTopRightflex">
                <div class="detailrightTitle tr2">发布时间：</div>
                <div class="detailrightContent tr2 textOverflow1">
                  {{ item.createTime }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="none-class" v-else>
        <el-image style="width: 160px; height: 160px" :src="require('@/assets/user/none.png')"></el-image>
        <div class="text">暂无数据</div>
      </div>
      <!-- 分页 -->
      <div class="pageStyle">
        <el-pagination v-if="demandList && demandList.length > 0" background layout="prev, pager, next"
          class="activity-pagination" :page-size="pageSize" :current-page="pageNum" :total="total"
          @size-change="handleSizeChange" @current-change="handleCurrentChange">
        </el-pagination>
      </div>
    </div>
  </div>
</template>
<script>
import { listData } from "@/api/system/dict/data";
import { demandData } from "@/api/home";
import { releaseDetectionList } from "@/api/release";
const T = 1600;

// const T = 240;
const I = 80;

// const T = 1790;
// const T = 310;
// const T = 900;
// const I = 150;
const D = [];
const LIST = [];
export default {
  name: "demandHall",
  data() {
    return {
      loading: false,
      pageNum: 1,
      pageSize: 10,
      total: 0,
      keywords: "",
      form: {},
      flag: "",
      appliTypeData: [],
      appliTypeImgList: [
        {
          url: require("../../../assets/appliMarket/new/1.png"),
        },
        {
          url: require("../../../assets/appliMarket/new/2.png"),
        },
        {
          url: require("../../../assets/appliMarket/new/3.png"),
        },
        {
          url: require("../../../assets/appliMarket/new/4.png"),
        },
        {
          url: require("../../../assets/appliMarket/new/5.png"),
        },
        {
          url: require("../../../assets/appliMarket/new/6.png"),
        },
        {
          url: require("../../../assets/appliMarket/new/7.png"),
        },
        {
          url: require("../../../assets/appliMarket/new/8.png"),
        },
        {
          url: require("../../../assets/appliMarket/new/9.png"),
        },
        {
          url: require("../../../assets/appliMarket/new/10.png"),
        },
      ],
      demandList: [],
      detectionList: [],
    };
  },
  async mounted() {
    this.getList();
    await this.getDictList();
    this.$nextTick(() => {
      for (var i = 0; i <= this.appliTypeImgList.length; i++) {
        LIST.push({ num: 0 });
        D.push(document.getElementById(`rock_${i}`));
      }
      // console.log(" mounted ~ D:", D);
    });
  },
  methods: {
    handleMouseEnter(index) {
      this.clearAwayTimedTask(index);
      LIST[index].enter = setInterval(() => {
        LIST[index].num += I;
        D[index].style.backgroundPositionY = "-" + LIST[index].num + "px";
        if (LIST[index].num > T - I) {
          clearInterval(LIST[index].enter);
          LIST[index].enter = null;
        }
      }, 30);
      // }, 100);
    },
    handleMouseLeave(index) {
      this.clearAwayTimedTask(index);
      LIST[index].leave = setInterval(() => {
        LIST[index].num -= I;
        D[index].style.backgroundPositionY = "-" + LIST[index].num + "px";
        if (LIST[index].num <= 0) {
          clearInterval(LIST[index].leave);
          LIST[index].leave = null;
        }
      }, 30);
      // }, 100);
    },
    clearAwayTimedTask(index) {
      clearInterval(LIST[index].leave);
      LIST[index].leave = null;
      clearInterval(LIST[index].enter);
      LIST[index].enter = null;
    },
    /** 查询字典数据列表 */
    async getDictList() {
      let params = { dictType: "demand_type" };
      let response = await listData(params);
      // .then((response) => {
      this.appliTypeData = response.rows;
      this.appliTypeData.unshift({ dictValue: "", dictLabel: "全部" });
      // this.appliTypeData.push({ dictValue: "-1", dictLabel: "检测需求" });
      // });
    },
    getList() {
      this.loading = true;
      let params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        type: this.flag,
        keyword: this.keywords,
      };
      demandData(params).then((res) => {
        if (res.code === 200) {
          this.demandList = res.rows;
          this.total = res.total;
          this.loading = false;
        }
      });
    },
    getDetectionList() {
      this.loading = true;
      let params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        keyword: this.keywords,
      };
      releaseDetectionList(params).then((res) => {
        if (res.code === 200) {
          this.detectionList = res.rows;
          this.total = res.total;
          this.loading = false;
        }
      });
    },
    onSearch() {
      this.getList();
    },
    getappliData(value) {
      this.flag = value;
      this.pageNum = 1;
      if (value == -1) {
        this.getDetectionList();
      } else {
        this.getList();
      }
    },
    handleSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.getList();
    },
    handleCurrentChange(pageNum) {
      this.pageNum = pageNum;
      this.getList();
    },
    goDetail(id) {
      this.$router.push("/demandDetail?id=" + id);
    },
    goDetectionDetail(id) {
      this.$router.push("/detectionDetail?id=" + id);
    },
    getImage(item) {
      if (item.imageUrl) {
        return item.imageUrl;
      } else if (item.alFileDetailVOs && item.alFileDetailVOs.length > 0) {
        return item.alFileDetailVOs[0].fileFullPath;
      } else {
        return require("@/assets/demand/xqimgdefault.png");
      }
    },
    getType(id) {
      let type = this.appliTypeData.find((item) => item.dictValue == id);
      type = type?.dictLabel ? type.dictLabel : "";
      return type;
    },
  },
};
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  padding-bottom: 60px;
}

.content_banner {
  width: 100%;
  height: 300px;
  background-image: url("../../../assets/release/banner.png");
  background-size: 100% 100%;
  text-align: center;
  margin: 0 auto;
  padding-top: 71px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  font-size: 40px;
  color: #000;
}

.activity-title-content {
  width: 100%;

  // background-color: #fff;
  .activity-title-box {
    width: 336px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 60px 0 40px;

    .activity-title {
      font-size: 40px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #333;
      line-height: 40px;
      padding: 0 40px;
    }

    .activity-divider {
      width: 48px;
      height: 4px;
      background: #21c9b8;
    }
  }

  .activity-search-box {
    margin-top: 40px;

    .activity-search-form {
      text-align: center;

      .activity-search-input {
        width: 792px;
        height: 54px;

        .activity-search-btn {
          width: 100px;
        }
      }
    }
  }
}

.appliType {
  width: 1200px;
  margin: 40px auto 0;
  display: flex;
  justify-content: space-between;

  .everyType {
    width: 102px;
    // height: 160px;
    text-align: center;
    cursor: pointer;

    .everyImg {
      // width: 63px;
      // height: 78px;
      // margin-left: calc((100% - 63px) / 2);

      // img {
      // width: 100%;
      // height: 100%;
      // }
    }

    .everyTitle {
      font-size: 18px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #979797;
      margin-top: 10px;
    }

    .everyIcon {
      width: 63px;
      height: 4px;
      background: #21c9b8;
      margin-top: 10px;
      margin-left: calc((100% - 63px) / 2);
    }
  }
}

.content_bottom {
  display: flex;
  align-items: center;
  flex-wrap: wrap;

  .content_bottom_item {
    margin-top: 20px;
    width: 590px;
    height: 208px;
    background: #ffffff;
    box-shadow: 0px 4px 18px 2px #e8f1fa;
    border-radius: 10px;
    padding: 20px;
    position: relative;
    cursor: pointer;
    z-index: 1;
    overflow: hidden;

    &:before {
      content: "";
      z-index: -1;
      position: absolute;
      top: 100%;
      left: 100%;
      width: 86px;
      height: 86px;
      border-radius: 50%;
      background-color: #21c9b8;
      transform-origin: center;
      transform: translate3d(-50%, -50%, 0) scale3d(0, 0, 0);
      transition: transform 0.3s ease-in;
    }

    .detailTitle {
      height: 30px;
      color: rgba(51, 51, 51, 1);
      font-size: 18px;
      margin-bottom: 10px;
    }

    .textOverflow1 {
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
    }

    .demandChunk {
      display: flex;
      justify-content: space-between;

      .demand_right {
        width: 413px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
      }

      .demandTopRightflex {
        display: flex;
        line-height: 24px;
      }

      .detailrightTitle {
        color: rgba(153, 153, 153, 1);
        font-size: 14px;
      }

      .detailrightTitle2 {
        color: rgba(0, 0, 0, 0.85);
        font-size: 14px;
      }

      .detailrightContent {
        width: 343px;
        color: rgba(51, 51, 51, 1);
        font-size: 14px;
      }
    }
  }

  .content_bottom_item:hover {
    box-shadow: 0px 3px 20px 0px rgba(132, 212, 178, 0.6);
    scale: 1.01;

    div {
      color: #ffffff !important;
    }

    &::before {
      transform: translate3d(-50%, -50%, 0) scale3d(15, 15, 15);
    }
  }

  .content_bottom_item:nth-child(2n) {
    margin-left: 20px;
  }
}

.pageStyle {
  margin-top: 60px;
  width: 100%;
  text-align: center;
}
</style>
<style lang="scss">
.activity-search-input {
  .el-input__inner {
    height: 54px;
    background: #fff;
    border-radius: 27px 0 0 27px;
    border: 1px solid #d9d9d9;
    font-size: 16px;
    font-family: PingFangSC-Regular, PingFang SC;
    line-height: 24px;
    padding-left: 30px;
  }

  .el-input-group__append {
    border-radius: 0px 100px 100px 0px;
    background: #21c9b8;
    font-size: 16px;
    font-family: PingFangSC-Regular, PingFang SC;
    color: #fff;
    line-height: 24px;
  }
}

.none-class {
  text-align: center;
  padding: 8% 0;

  .text {
    font-size: 14px;
    font-weight: 400;
    color: #999999;
    line-height: 14px;
  }
}
</style>

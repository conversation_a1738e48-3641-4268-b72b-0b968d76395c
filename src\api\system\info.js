/*
 * @Author: zhc
 * @Date: 2023-02-06 14:52:03
 * @LastEditTime: 2023-02-15 17:13:19
 * @Description:
 *
 * @LastEditors: zhc
 */
import request from "@/utils/request";

// 查询消息通知列表
export function listInfo(query) {
  return request({
    url: "/system/info/list",
    method: "get",
    params: query,
  });
}

// 查询公告详细
export function getInfoDetail(id) {
  return request({
    url: "/system/info/getInfo",
    method: "get",
    params: { id: id },
  });
}
// 撤销加入企业的申请
export function revocationApply(id) {
  return request({
    url: "/system/info/exit-company",
    method: "get",
    params: { id: id },
  });
}

// 删除消息通知
export function deleteInfo(data) {
  return request({
    url: "/system/info/remove",
    method: "post",
    params: data,
  });
}

// 修改公告
export function updateNotice(data) {
  return request({
    url: "/system/notice",
    method: "put",
    data: data,
  });
}

// 删除公告
export function delNotice(noticeId) {
  return request({
    url: "/system/notice/" + noticeId,
    method: "delete",
  });
}

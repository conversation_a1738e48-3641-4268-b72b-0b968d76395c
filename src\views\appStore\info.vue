<template>
    <div style="background-color: #fff;">
        <div class="info-top">
            <div style="width: 1128px;margin: 0px auto;">

                <div style="width: 797px;display: inline-block;vertical-align: top;">
                    <div style="margin-top: 60px;width: 100%;">
                        <span style="color: rgba(51, 51, 51, 1);font-size: 42px;line-height: 66px;">{{ info.appStoreName
                        }}</span>
                        <span style="line-height: 34px;border-radius: 2px;background-color: #21c9b8;color: #fff;
								font-size: 14px;margin: 16px 30px;padding: 0px 10px;">{{ info.appLabel }}</span>
                    </div>
                    <div style="color: rgba(102, 102, 102, 1);font-size: 16px;overflow: hidden;
						text-overflow: ellipsis;
						display: -webkit-box;
						-webkit-box-orient: vertical;
						-webkit-line-clamp: 2;">{{ info.appStoreIntroduction }}</div>
                    <div style="line-height: 36px;color: #21c9b8;font-size: 20px;margin: 20px 0">
                        {{ info.appStorePrice }}元起 / 年</div>
                    <div style="margin-top: 30px;">
                        <span class="btn" style="background: transparent linear-gradient(105deg, #21c9b8 0%, #7AB2FF 100%) 0% 0% no-repeat padding-box;
								color: rgba(255, 255, 255, 1);" @click="">立即订阅</span>
                        <!--							<span class="btn" @click="dingyue" v-if="detail.issub==0">立即收藏</span>-->
                        <!--							<span class="btn" @click="quxiaodingyue" v-else>已收藏</span>-->
                        <!--							<span class="btn" @click="getUrl()">跳转使用</span>-->
                    </div>
                </div>

                <div class="right-info" v-show="info.supply !== ''">
                    <div>
                        <p style="padding-top: 10px;">应用提供：{{ info.supply }}</p>
                        <p>联系人：{{ info.appStoreContactsName }}</p>
                        <p>联系电话：{{ info.appStoreContactsPhone }}</p>
                    </div>
                </div>
            </div>
        </div>
        <div style="width: 1226px; margin: 60px auto;">
            <h3 style="line-height: 30px;color: #333;font-size: 20px;font-weight: 400;padding-bottom: 20px;">
                <span style="display: inline-block;vertical-align: top;height: 20px;width: 3px;
					background-color: #428AFA;border-radius: 3px;margin: 5px 18px 5px 0px;"></span>
                应用介绍
            </h3>
            <div>
                {{ info.appStoreContent }}
            </div>
        </div>
    </div>
</template>

<script>
import { getAppStoreDetail } from "@/api/appStore";
import { appliCollect } from "@/api/appliMarket";
import "@/assets/styles/index.css";

export default {
    name: "appStoreInfo",
    data() {
        return {
            showLogin: false,
            userinfo: [],
            token: '',
            id: '',
            info: {},
            detail: [],
            Dataradio: "",
            showGm: false,
            team: '',
            phone: window.sessionStorage.getItem('userName') ? window.sessionStorage.getItem('userName') : ''
        };
    },
    created() {
        if (this.$route.query.id) {
            this.id = this.$route.query.id
            this.getInfo()
        }
    },
    methods: {
        updateDataList() {
            console.log(this.checked)
        },
        changeHandler() {
            if (this.Dataradio == 'month') {
                this.detail.price = '1000'
            } else if (this.Dataradio == 'year') {
                this.detail.price = '30000'
            } else if (this.Dataradio == 'permanent') {
                this.detail.price = '68000'
            }
        },
        goto() {
            window.open(this.info.erweima)
        },
        async getInfo() {
            let res = await getAppStoreDetail({ appStoreId: this.id })
            if (res.code == 200) {
                this.info = res.data;
                console.log(this.info,'info')
                this.detail = res.data;
            }
        },
        async dingyue() {
            let res = await appliCollect({ appId: this.id, userId: window.sessionStorage.getItem('userId') })
            if (res.code == 200) {
                this.getInfo();
            } else {
                alert(res.msg)
            }
        },
        quxiaodingyue() {
            YS.postFetch('uuc/store/unsubscribe', {
                id: this.id,
                userId: window.sessionStorage.getItem('userId')
            }).then(res => {
                if (res.code == 200) {
                    this.getInfo();
                } else {
                    alert(res.msg)
                }

            });
        },
    },
};
</script>

<style type="text/css">
.info-top {
    background-image: url('../../assets/appStore/appbanner.png');
    height: 360px;
    background-size: 100%;
}

.info-top span {
    display: inline-block;
    text-align: center;
    vertical-align: top;
}

.info-top .right-info {
    display: inline-block;
    vertical-align: top;
    width: 331px;
    height: 326px;
    background-image: url('../../assets/appStore/appbannersub.png');
    background-size: 100%;
    margin-top: 8px;
    float: right;
}

.info-top .right-info>div {
    width: 304px;
    height: 184px;
    margin-top: 80px;
    margin-left: 20px;
    opacity: 0.75;
    border-radius: 2px;
    background-color: rgba(255, 255, 255, 1);
}

.info-top .right-info>div p {
    line-height: 36px;
    padding-left: 30px;
    padding-bottom: 8px;
    color: rgba(102, 102, 102, 1);
    font-size: 14px;
    text-align: left;
}

.info-top span.btn {
    cursor: pointer;
    width: 104px;
    height: 36px;
    line-height: 36px;
    border-radius: 4px;
    color: #428AFA;
    font-size: 16px;
    border: 1px solid #21c9b8;
    margin-right: 20px;
}
</style>
<template>
  <div class="scienceFunding">
    <div class="content_banner">
      <div>人才服务</div>
      <!-- <div style="height: 33px; margin-top: 41px;font-size: 20px;">“众筹科研"新模式，构建"共投、共研、共担、共赢”新机制。</div> -->
    </div>
    <div class="card-container">
      <div class="content_top">
        <div class="content_top_item" :class="currentMenu == item.dictValue ? 'active' : ''"
          v-for="(item, index) in deviceMenuList" :key="index" @click="handleClick(item.dictValue)">
          <div class="imgStyle">
            <img :src="currentMenu == item.dictValue ? item.hoverUrl : item.url" alt="" />
          </div>
          <div :class="currentMenu == item.dictValue ? 'titleActive' : 'title'">
            {{ item.dictLabel }}
          </div>
        </div>
      </div>
      <div class="content">
        <div class="desc">枣强县复合材料产业以“产业基底+共享智造”双轮驱动为核心，构建了覆盖全产业链的人才服务生态体系，
          通过技能鉴定、人才培养、人才流动、品牌培育四大功能模块，实现人才与产业需求的精准匹配，推动传统复材产业集群向智
          能化、高端化转型升级。这一生态体系不仅强化了“枣强复材师”品牌的技术竞争力，更通过共享智造平台打破传统用工壁垒，
          形成“技能认证-人才培育-灵活调配-智能管理”的闭环服务链，为产业高质量发展提供可持续的人力资源保障。</div>

        <div class="content-item" v-if="currentMenu == 1">
          <img src="@/assets/talentPool/img1.png" class="info-img" alt="">
          <div class="info">
            <img src="@/assets/talentPool/title1.png" class="info-title" alt="">
            <div class="info-text">枣强县复合材料产业始于上世纪60年代，历经60年发展， 形成占地20平方公里的现
              代化产业集群，构建全链条体系，汇聚8万余名技术人才，培育"枣强复材师"劳务品
              牌。产品覆盖传统及航空航天、新能源等高端领域，产量占全国1/3，稳居"复材之乡
              "与河北重点产业集群。通过共享智造打破边界，该品牌成为全国产业链智能节点，
              成为复合材料产业+共享智造平台双轮驱动的劳务品牌新范式。
            </div>
          </div>
        </div>
        <div class="content-item" v-if="currentMenu == 2">
          <div class="left">
            <img src="@/assets/talentPool/img2.png" class="info-img" alt="">
            <div style="display: flex;justify-content: space-between; width: 100%;">
              <div class="info-btn" @click="$router.push('/talentJoinHengshui')">报名鉴定</div>
              <div class="info-btn" @click="$router.push('/serviceSharing?index=4')">证书查询</div>
            </div>
          </div>
          <div class="info">
            <img src="@/assets/talentPool/title2.png" class="info-title" alt="">
            <div class="info-text" style="max-height: 100px;">复材产业职业技能鉴定中心，整合拉挤、缠绕、 模压等智能产线资源， 构建“实训-认证-就业”
              一体化服务体系，覆盖建筑建材、航空航天等8大领域。开展玻璃钢制品手糊、模压、拉挤、缠绕
              、灌注及喷射工等工种的五至一级技能等级评定，服务从业者超8万人，推动全产业链技能人才专
              业化发展。
            </div>
          </div>
        </div>
        <div class="content-item" v-if="currentMenu == 3">
          <div class="left">
            <img src="@/assets/talentPool/img3.png" class="info-img" alt="">
            <div class="info-btn info-btn-full" @click="$router.push('/talentJoinTrain')">报名培训</div>
          </div>
          <div class="info">
            <img src="@/assets/talentPool/title3.png" class="info-title" alt="">
            <div class="info-text" style="max-height: 100px;">共享智造人才培训基地，聚焦复合材料产业高技能人才培养。通过设立实训基地、开展技能评选，
              激励职工提升技术；联合高校与科研机构，利用智能平台资源定向培养创新与实践并重的复合型
              人才。建立灵活流动机制，构建可持续人才梯队，为产业融合提供动力。
            </div>
          </div>
        </div>
        <div class="content-item" v-if="currentMenu == 4">
          <img src="@/assets/talentPool/img4.png" class="info-img" alt="">
          <div class="info">
            <img src="@/assets/talentPool/title4.png" class="info-title" alt="">
            <div class="info-text">枣强县依托共享智造平台打造复合材料产业“人才库"，通过整合行业、协会、企业资源构建人才
              库，实现技术工人在产业链内按需流动。 该平台运用数字化管理， 支持企业间灵活调配人力，旺
              季共享用工、淡季统筹安置，既缓解企业"用工荒"和人才外流风险， 又通过统一协调降低招工成
              本，促进产业集群人才稳定与高效利用。
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      currentMenu: '1',
      deviceMenuList: [
        {
          dictLabel: '区域品牌',
          dictValue: '1',
          url: require("@/assets/entrepreneurship/base.png"),
          hoverUrl: require("@/assets/entrepreneurship/base_active.png"),
        },
        {
          dictLabel: '技能鉴定',
          dictValue: '2',
          url: require("@/assets/entrepreneurship/floor.png"),
          hoverUrl: require("@/assets/entrepreneurship/floor_active.png"),
        },
        {
          dictLabel: '人才培训',
          dictValue: '3',
          url: require("@/assets/entrepreneurship/cooperate.png"),
          hoverUrl: require("@/assets/entrepreneurship/cooperate_active.png"),
        },
        {
          dictLabel: '行业人才库',
          dictValue: '4',
          url: require("@/assets/detectingSharing/other.png"),
          hoverUrl: require("@/assets/detectingSharing/other_active.png"),
        },
      ],
    }
  },
  mounted() {
  },
  methods: {
    // 切换
    handleClick(value) {
      this.currentMenu = value;
    },
  }
}
</script>


<style lang="scss" scoped>
.scienceFunding {
  width: 100%;
  padding-bottom: 60px;
  background-color: #F2F2F2;

  .content_banner {
    width: 100%;
    height: 300px;
    background-image: url("../../../../assets/release/banner.png");
    background-size: 100% 100%;
    text-align: center;
    margin: 0 auto;
    font-weight: 500;
    font-size: 40px;
    color: #000;
    font-family: DOUYU;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
}

.card-container {
  width: 1200px;
  margin: 0 auto;

  .content_top {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 40px;

    .content_top_item {
      width: 290px;
      height: 68px;
      background: #ffffff;
      box-shadow: 0px 3px 20px 0px rgba(0, 0, 0, 0.08);
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      margin-left: 26px;

      .imgStyle {
        width: 27px;
        height: 27px;
      }

      .title {
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 18px;
        color: #333333;
        margin-left: 20px;
      }

      .titleActive {
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 18px;
        color: #ffffff;
        margin-left: 20px;
      }
    }

    .content_top_item:nth-child(1) {
      margin-left: 0;
    }

    .active {
      background-color: #0cad9d;
    }
  }

  .content {
    width: 100%;
    min-height: 500px;
    background-color: #fff;
    margin-top: 30px;
    border-radius: 5px;
    padding: 90px 110px;
    box-sizing: border-box;

    .desc {
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 18px;
      color: #333333;
      line-height: 34px;
      margin-bottom: 70px;
    }

    .content-item {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin: 60px 0;

      .left {
        display: flex;
        flex-direction: column;
        align-items: center;

        .info-img {
          height: 100%;
        }

        .info-btn {
          width: 48%;
          height: 50px;
          background: #0DAE9E;
          border-radius: 2px;
          cursor: pointer;
          text-align: center;
          font-family: Source Han Sans CN;
          font-weight: 400;
          font-size: 16px;
          color: #FFFFFF;
          line-height: 50px;
          margin-top: 20px;
        }
        .info-btn-full{
          width: 100%;
        }
      }

      .info {
        width: 600px;
        height: 225px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;

        .info-title {
          height: 30px;
          margin-bottom: 20px;
          object-fit: contain;
        }

        .info-text {
          font-family: Source Han Sans CN;
          font-weight: 400;
          font-size: 16px;
          color: #5A5A5A;
          line-height: 24px;
          max-height: 160px;
          overflow: auto;
        }


      }


    }
  }

}
</style>

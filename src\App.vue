<!--
 * @Author: zhc
 * @Date: 2023-02-12 10:55:27
 * @LastEditTime: 2023-05-31 17:46:26
 * @Description: 
 * @LastEditors: zhc
-->
<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script>
import ThemePicker from "@/components/ThemePicker";

export default {
  name: "App",
  components: { ThemePicker },
  metaInfo() {
    return {
      title:
        this.$store.state.settings.dynamicTitle &&
        this.$store.state.settings.title,
      titleTemplate: (title) => {
        return title
          ? `${title} - ${process.env.VUE_APP_TITLE}`
          : process.env.VUE_APP_TITLE;
      },
    };
  },
};
</script>
<style lang="scss">
::v-deep .receive-status {
  display: none;
}
</style>

<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="2.5" :xs="24">
        <user-menu activeIndex="1" />
      </el-col>
      <el-col :span="20" :xs="24" style="width: calc(100% - 180px)">
        <div class="main-content">
          <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane label="我申请的" name="0">
              <el-form
                class="queryForm"
                :model="queryParams"
                ref="queryForm"
                size="small"
                :inline="true"
              >
                <el-form-item label="企业名称" prop="companyName">
                  <el-input
                    v-model="queryParams.companyName"
                    placeholder="请输入企业名称"
                    clearable
                  >
                  </el-input>
                </el-form-item>
                <!-- <el-form-item label="状态" prop="intentionStatus">
                  <el-select v-model="queryParams.intentionStatus" placeholder="请选择" clearable>
                    <el-option v-for="dict in intentionStatus" :key="dict.dictValue" :label="dict.dictLabel"
                      :value="dict.dictValue" />
                  </el-select>
                </el-form-item> -->
                <el-form-item>
                  <el-button
                    type="primary"
                    icon="el-icon-search"
                    size="mini"
                    @click="handleQuery"
                    >搜索</el-button
                  >
                  <el-button
                    icon="el-icon-refresh"
                    size="mini"
                    @click="resetQuery"
                    >重置</el-button
                  >
                </el-form-item>
              </el-form>
              <div class="table">
                <div style="width: 100%">
                  <el-table
                    :data="cooperationList"
                    style="width: 100%"
                    :v-loading="loading"
                  >
                    <el-table-column
                      align="center"
                      prop="intentionTypeName"
                      label="资源类型"
                      width="180"
                    >
                    </el-table-column>
                    <el-table-column
                      align="center"
                      prop="title"
                      label="资源名称"
                    >
                    </el-table-column>
                    <el-table-column
                      align="center"
                      prop="intentionContent"
                      label="申请内容"
                    >
                    </el-table-column>
                    <el-table-column
                      align="center"
                      prop="companyName"
                      label="申请公司"
                    >
                    </el-table-column>
                    <el-table-column
                      align="center"
                      prop="createTime"
                      label="申请时间"
                    >
                    </el-table-column>
                    <!-- <el-table-column align="center" prop="intentionStatus" label="状态">
                      <template slot-scope="scope">
                        <el-tag v-if="scope.row.intentionStatus === '0'" type="danger">待回复</el-tag>
                        <el-tag v-if="scope.row.intentionStatus === '1'" type="warning">已申请</el-tag>
                        <el-tag v-if="scope.row.intentionStatus === '2'" type="primary">对接中</el-tag>
                        <el-tag v-if="scope.row.intentionStatus === '3'" type="info">已完结</el-tag>
                      </template>
                    </el-table-column> -->
                    <!-- <el-table-column align="center" label="操作">
                      <template slot-scope="scope">
                        <el-button type="text" size="small" @click="handleView(scope.row)">查看</el-button>
                        <el-button type="text" size="small" @click="handleDelete(scope.row)">删除</el-button>
                      </template>
                    </el-table-column> -->
                  </el-table>
                </div>
                <!-- 分页 -->
                <div class="pageStyle">
                  <el-pagination
                    v-if="tableData && tableData.length > 0"
                    background
                    layout="prev, pager, next"
                    class="activity-pagination"
                    :page-size="pageSize"
                    :current-page="pageNum"
                    :total="total"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                  >
                  </el-pagination>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="被申请的" name="1">
              <el-form
                class="queryForm"
                :model="queryParams1"
                ref="queryForm"
                size="small"
                :inline="true"
              >
                <el-form-item label="企业名称" prop="companyName">
                  <el-input
                    v-model="queryParams1.intentionCompanyName"
                    placeholder="请输入企业名称"
                    clearable
                  >
                  </el-input>
                </el-form-item>
                <!-- <el-form-item label="状态" prop="intentionStatus">
                  <el-select v-model="queryParams1.intentionStatus" placeholder="请选择" clearable>
                    <el-option v-for="dict in intentionStatus" :key="dict.dictValue" :label="dict.dictLabel"
                      :value="dict.dictValue" />
                  </el-select>
                </el-form-item> -->
                <el-form-item>
                  <el-button
                    type="primary"
                    icon="el-icon-search"
                    size="mini"
                    @click="handleQuery"
                    >搜索</el-button
                  >
                  <el-button
                    icon="el-icon-refresh"
                    size="mini"
                    @click="resetQuery"
                    >重置</el-button
                  >
                </el-form-item>
              </el-form>
              <div class="table">
                <div style="width: 100%">
                  <el-table
                    :data="cooperationList"
                    style="width: 100%"
                    :v-loading="loading"
                  >
                    <el-table-column
                      align="center"
                      prop="intentionTypeName"
                      label="资源类型"
                      width="180"
                    >
                    </el-table-column>
                    <el-table-column
                      align="center"
                      prop="title"
                      label="资源名称"
                    >
                    </el-table-column>
                    <el-table-column
                      align="center"
                      prop="intentionContent"
                      label="申请内容"
                    >
                    </el-table-column>
                    <el-table-column
                      align="center"
                      prop="companyName"
                      label="供需发布公司"
                    >
                    </el-table-column>
                    <el-table-column
                      align="center"
                      prop="createTime"
                      label="申请时间"
                    >
                    </el-table-column>
                    <!-- <el-table-column align="center" prop="intentionStatus" label="状态">
                      <template slot-scope="scope">
                        <el-tag v-if="scope.row.intentionStatus === '0'" type="danger">待回复</el-tag>
                        <el-tag v-if="scope.row.intentionStatus === '1'" type="warning">已申请</el-tag>
                        <el-tag v-if="scope.row.intentionStatus === '2'" type="primary">对接中</el-tag>
                        <el-tag v-if="scope.row.intentionStatus === '3'" type="info">已完结</el-tag>
                      </template>
                    </el-table-column> -->
                    <el-table-column align="center" label="操作">
                      <template slot-scope="scope">
                        <el-button
                          type="text"
                          size="small"
                          @click="handleView(scope.row)"
                          >查看</el-button
                        >
                        <el-button
                          type="text"
                          size="small"
                          @click="handleDelete(scope.row)"
                          >删除</el-button
                        >
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
                <!-- 分页 -->
                <div class="pageStyle">
                  <el-pagination
                    v-if="tableData && tableData.length > 0"
                    background
                    layout="prev, pager, next"
                    class="activity-pagination"
                    :page-size="pageSize"
                    :current-page="pageNum"
                    :total="total"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                  >
                  </el-pagination>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import UserMenu from "../components/userMenu.vue";
import { listData } from "@/api/system/dict/data";
import { dockingList } from "@/api/system/user";
export default {
  name: "User",
  components: { UserMenu },
  data() {
    return {
      loading: false,
      tableData: [
        {
          category: "供给",
          name: "玻璃钢格栅",
          content: "申请内容",
          company: "恒润集团有限公司",
          time: "2025-12-05",
          status: "待回复",
        },
        {
          category: "供给",
          name: "玻璃钢格栅",
          content: "申请内容",
          company: "恒润集团有限公司",
          time: "2025-12-05",
          status: "对接中",
        },
        {
          category: "供给",
          name: "玻璃钢格栅",
          content: "申请内容",
          company: "恒润集团有限公司",
          time: "2025-12-05",
          status: "已完结",
        },
        {
          category: "供给",
          name: "玻璃钢格栅",
          content: "申请内容",
          company: "恒润集团有限公司",
          time: "2025-12-05",
          status: "已申请",
        },
      ],
      showLogin: false,
      userinfo: [],
      token: "",
      cooperationList: [],
      partnerList: [],
      total: 1,
      pageNum: 1,
      pageSize: 10,
      activeName: "0",
      queryType: "my",
      intentionStatus: [],
      queryParams: {
        companyName: "",
        intentionStatus: "",
      },
      queryParams1: {
        intentionCompanyName: "",
        intentionStatus: "",
      },
    };
  },
  created() {
    this.getList();
    this.getDicts();
  },
  methods: {
    getList() {
      dockingList({
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        queryType: this.queryType,
        companyName: this.queryParams.companyName,
        intentionStatus: this.queryParams.intentionStatus,
        intentionCompanyName: this.queryParams1.intentionCompanyName,
      }).then((res) => {
        this.cooperationList = res.rows;
        this.total = res.total;
      });
    },
    handleClick(tab, event) {
      if (this.activeName === "0") {
        this.queryType = "my";
      } else {
        this.queryType = "";
      }
      this.getList();
    },
    handleQuery() {
      this.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.queryParams = {
        companyName: "",
        intentionStatus: "",
      };
      this.queryParams1 = {
        intentionCompanyName: "",
        intentionStatus: "",
      };
      this.getList();
    },
    handleSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.getList();
    },
    handleCurrentChange(pageNum) {
      this.pageNum = pageNum;
      this.getList();
    },
    getDicts() {
      let params = { dictType: "intention_status" };
      listData(params).then((response) => {
        if (response.code == 200) {
          this.intentionStatus = response.rows;
        }
      });
    },
    handleDelete(row) {
      this.$confirm("是否确认删除该记录?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.deleteDocking(row.id);
        })
        .catch(function () {});
      // let delurl = '/portalweb/IntentionApply/' + id
      // YS.deleteJsonFetch(delurl, {}).then(res => {
      //   alert('删除成功')
      //   this.getCooperationList()
      // });
    },
    handleView(row) {
      console.log(row);
      this.$router.push({
        path: "dockingRecordsDetail",
        query: {
          id: row.id,
          createTime: row.createTime ? row.createTime : "",
          intentionTypeName: row.intentionTypeName ? row.intentionTypeName : "",
          intentionContent: row.intentionContent ? row.intentionContent : "",
          completionDate: row.completionDate ? row.completionDate : "",
          quantity: row.quantity ? row.quantity : "",
          price: row.price ? row.price : "",
          rate: row.rate ? row.rate : "",
          shippingFee: row.shippingFee ? row.shippingFee : "",
          sum: row.sum ? row.sum : "",
          term: row.term ? row.term : "",
          title: row.title ? row.title : "",
        },
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.app-container {
  background: linear-gradient(to right, rgb(225, 247, 240), rgb(244, 252, 250));
  height: 100vh;
}

.main-content {
  padding: 20px;

  .table {
    margin-top: 20px;
    background: #fff;
    border-radius: 10px;
    padding: 20px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    justify-content: space-around;

    .pageStyle {
      width: 100%;
      margin-top: 61px;
      display: flex;
      justify-content: center;
    }
  }
}
</style>

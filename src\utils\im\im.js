/*
 * @Author: zhc
 * @Date: 2023-05-18 14:57:53
 * @LastEditTime: 2023-05-30 17:42:40
 * @Description:
 * @LastEditors: zhc
 */
// 用于向 IMKit SDK 提供用户信息、群组信息等数据
// import custom_service from "./custom_service.js";
// im
import * as RongIMLib from "@rongcloud/imlib-next";
import { defineCustomElements, imkit } from "@rongcloud/imkit";
import { CoreEvent } from "@rongcloud/imkit";

// export function InitIm() {
//   // IMLib 的初始化配置，appkey 为必填参数，其他字段参见 IInitOption
//   let libOption = { appkey: RONGYUN_APP_KEY };
//   // 获取 RongIMLib 实例对象，请务必保证此过程只被执行一次

//   RongIMLib.init(libOption);

//   imkit.init({
//     appkey: RONGYUN_APP_KEY,
//     service: custom_service,
//     libOption: libOption,
//   });
// }

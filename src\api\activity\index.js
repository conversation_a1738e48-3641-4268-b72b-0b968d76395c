/*
 * @Author: jhy
 * @Date: 2023-01-30 17:58:37
 * @LastEditors: jhy
 * @LastEditTime: 2023-02-02 13:45:35
 */
import request from "@/utils/request";

// 活动详情
export function getActivityDetail(params) {
  return request({
    url: "/system/activity-portal/detail",
    method: "get",
    params,
  });
}

// 活动详情--立即报名
export function addActivityEnroll(data) {
  return request({
    url: "/system/activity-enroll/add",
    method: "post",
    data,
  });
}

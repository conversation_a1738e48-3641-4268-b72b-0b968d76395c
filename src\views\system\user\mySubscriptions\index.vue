<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="2.5" :xs="24">
        <user-menu />
      </el-col>
      <el-col :span="20" :xs="24">
        <div class="content">
          <div class="content_type">
            <div class="title">我的订阅</div>
            <div class="right_content">
              <div style="color: #21c9b8">
                您有
                <span
                  ><el-tag>{{ feesNum }}</el-tag></span
                >
                个待续费应用，请尽快续费，以免影响正常使用
              </div>
            </div>
          </div>
          <div class="tableStyle" v-loading="loading">
            <div class="everyItem" v-for="item in subscribeList" :key="item.id">
              <div class="orderNumTime">
                <div>订单编号: {{ item.id }}</div>
                <div style="margin-left: 10%">
                  下单时间: {{ item.createTime }}
                </div>
              </div>
              <div class="driver"></div>
              <div class="item_content">
                <div class="item_img">
                  <img :src="item.appLogo" alt="" />
                </div>
                <div class="item_desc">
                  <div class="title">{{ item.remark }}</div>
                  <!-- <div style="font-size: 14px; margin-top: 10px">
                    <span style="color: #999999">规格:</span>
                    <span style="margin-left: 5px">{{
                      item.specs == "1" ? "基础版" : "高级版"
                    }}</span>
                  </div> -->
                  <div style="font-size: 14px; margin-top: 10px">
                    <span style="color: #999999">可用时长:</span>
                    <span style="margin-left: 5px">{{
                      item.validTime == "1" ? "一年" : "永久"
                    }}</span>
                  </div>
                  <div style="font-size: 14px; margin-top: 10px">
                    <span style="color: #999999">可用人数:</span>
                    <span style="margin-left: 5px">不限</span>
                    <!-- <span style="margin-left: 5px">{{ item.userNumber }}</span> -->
                  </div>
                </div>
                <div class="item_amounts">
                  <div style="color: #999999; font-size: 14px">订单金额</div>
                  <div style="margin-top: 10px; font-weight: 400">
                    ￥{{ item.price }}
                  </div>
                </div>
                <div class="driverVertical"></div>
                <div>
                  <div v-if="item.orderStatus">
                    {{
                      orderStatusList.filter(
                        (item1) => item1.dictValue == item.orderStatus
                      )[0].dictLabel
                    }}
                  </div>
                  <!-- <div
                    style="margin-top: 10px; color: #21C9B8; cursor: pointer"
                    @click="goDetail(item.id)"
                  >
                    订单详情
                  </div> -->
                </div>
                <!-- 待支付 支付中 -->
                <div style="margin: 0 7%">
                  <div
                    style="color: #21c9b8; cursor: pointer"
                    @click="goDetail(item.id)"
                  >
                    订单详情
                  </div>
                  <div
                    v-if="
                      item.orderStatus == 1 ||
                      item.orderStatus == 6 ||
                      item.orderStatus == 8
                    "
                    style="margin-top: 10px; color: #21c9b8; cursor: pointer"
                    @click="goPay(item.id)"
                  >
                    去支付
                  </div>
                  <div
                    v-if="item.orderStatus == 1 || item.orderStatus == 8"
                    style="margin-top: 10px; color: #21c9b8; cursor: pointer"
                    @click="cancelOrder(item.id)"
                  >
                    取消订单
                  </div>
                </div>
                <div
                  v-if="item.orderStatus == 1 || item.orderStatus == 8"
                  style="color: #21c9b8; cursor: pointer; margin: 0 1%"
                  @click="tryout(item)"
                >
                  前往试用
                </div>
                <!-- 待发货 -->
                <div
                  v-if="item.orderStatus == 2"
                  style="color: #21c9b8; cursor: pointer; margin: 0 1%"
                  @click="tryout(item)"
                >
                  前往试用
                </div>
                <div
                  style="margin: 0 1%"
                  v-if="
                    item.orderStatus != 1 &&
                    item.orderStatus != 2 &&
                    item.orderStatus != 8 &&
                    item.orderStatus != 9
                  "
                >
                  <div
                    style="color: #21c9b8; cursor: pointer"
                    @click="getInvoiceData(item.id)"
                  >
                    {{ item.applyBilling == 0 ? "申请开票" : "重新开票" }}
                  </div>
                  <div
                    v-if="item.makeinvoice == 1"
                    style="margin-top: 10px; color: #21c9b8; cursor: pointer"
                    @click="viewInvoiceData(item.id)"
                  >
                    查看发票
                  </div>
                  <div
                    v-if="item.orderStatus == 4"
                    style="margin-top: 10px; color: #21c9b8; cursor: pointer"
                    @click="confirmReceipt(item.id)"
                  >
                    确认收货
                  </div>
                </div>
                <!-- 已成交 -->
                <!-- <div
                  style="margin: 0 7%"
                  v-if="item.orderStatus == 5 && item.makeinvoice == 1"
                >
                  <div
                    style="margin-top: 10px; color: #21C9B8; cursor: pointer"
                    @click="cancelOrder(item.id)"
                  >
                    已开票
                  </div>
                </div> -->
              </div>
            </div>
            <div style="text-align: center; margin-top: 45px">
              <el-pagination
                v-show="total > 0"
                :total="total"
                background
                layout="prev, pager, next"
                :page-size="5"
                :current-page="pageNum"
                @current-change="handleCurrentChange"
              >
              </el-pagination>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
    <el-dialog
      title="开票信息"
      :visible.sync="invoiceVisible"
      width="750px"
      append-to-body
    >
      <el-form :model="invoiceData" label-width="80px">
        <el-form-item label="发票类型:" prop="realName">
          {{ invoiceData.invoiceType == 1 ? "专票" : "普票" }}
        </el-form-item>
        <el-form-item label="公司名称:" prop="phonenumber">
          {{ invoiceData.companyName }}
        </el-form-item>
        <el-form-item label="税号:" prop="weixin">
          {{ invoiceData.dutyParagraph }}
        </el-form-item>
        <el-form-item label="公司地址:" prop="email">
          {{ invoiceData.address }}
        </el-form-item>
        <el-form-item label="公司电话:" prop="email">
          {{ invoiceData.phone }}
        </el-form-item>
        <el-form-item label="开户银行:" prop="email">
          {{ invoiceData.openAccount }}
        </el-form-item>
        <el-form-item label="银行账号:" prop="email">
          {{ invoiceData.bankAccount }}
        </el-form-item>
        <el-form-item label="邮箱地址:" prop="email">
          {{ invoiceData.email }}
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelDialog">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  sublist,
  cancelOrder,
  invoiceList,
  applyInvoice,
  downLoadInvoice,
  pendingFeesNum,
  modifyStatus,
} from "@/api/system/user";
import UserMenu from "../components/userMenu.vue";

export default {
  name: "Operlog",
  dicts: ["sys_oper_type", "sys_common_status"],
  components: { UserMenu },
  data() {
    return {
      subscribeList: [
        {
          id: 1,
          title: "檬豆云供应链管理系统",
          specs: "正式版",
          duration: "永久",
          number: "不限",
          amounts: "9999",
        },
        {
          id: 2,
          title: "檬豆云供应链管理系统",
          specs: "正式版",
          duration: "永久",
          number: "不限",
          amounts: "9999",
        },
        {
          id: 3,
          title: "檬豆云供应链管理系统",
          specs: "正式版",
          duration: "永久",
          number: "不限",
          amounts: "9999",
        },
        {
          id: 4,
          title: "檬豆云供应链管理系统",
          specs: "正式版",
          duration: "永久",
          number: "不限",
          amounts: "9999",
        },
        {
          id: 5,
          title: "檬豆云供应链管理系统",
          specs: "正式版",
          duration: "永久",
          number: "不限",
          amounts: "9999",
        },
      ],
      pageNum: 1,
      total: 0,
      flag: 1,
      orderStatusList: [
        {
          dictValue: 1,
          dictLabel: "待支付",
        },
        {
          dictValue: 2,
          dictLabel: "待发货",
        },
        {
          dictValue: 3,
          dictLabel: "支付失败",
        },
        {
          dictValue: 4,
          dictLabel: "已发货",
        },
        {
          dictValue: 5,
          dictLabel: "已成交",
        },
        {
          dictValue: 6,
          dictLabel: "待续费",
        },
        {
          dictValue: 7,
          dictLabel: "已关闭",
        },
        {
          dictValue: 8,
          dictLabel: "支付中",
        },
        {
          dictValue: 9,
          dictLabel: "已取消",
        },
      ],
      loading: false,
      invoiceVisible: false,
      invoiceData: {},
      currentId: null,
      feesNum: 0,
    };
  },
  created() {
    this.getPendingFeesNum();
    this.getList();
  },
  methods: {
    getPendingFeesNum() {
      let params = {
        userId: this.$store.state.user.userId,
        orderStatus: "6",
      };
      pendingFeesNum(params).then((res) => {
        if (res.code === 200) {
          console.log(res, "--------");
          this.feesNum = res.data;
        }
      });
    },
    getList() {
      this.loading = true;
      let params = {
        pageNum: this.pageNum,
        pageSize: 5,
      };
      sublist(params).then((res) => {
        this.loading = false;
        if (res.code === 200) {
          this.subscribeList = res.rows;
          this.total = res.total;
        }
      });
    },
    handleCurrentChange(pageNum) {
      this.pageNum = pageNum;
      this.getList();
    },
    goDetail(id) {
      this.$router.push({
        path: "/user/orderSubDetail",
        query: {
          id,
        },
      });
    },
    cancelOrder(id) {
      this.$confirm("订单取消后无法恢复，请谨慎操作!", "取消订单", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          cancelOrder(id).then((res) => {
            if (res.code === 200) {
              this.$message.success("操作成功!");
              this.getList();
            }
          });
        })
        .catch(() => {});
    },
    tryout(item) {
      // window.open(href);

      console.log(item);
      if (item.appName == "云端研发") {
        let url;
        let hostname;
        var result;
        hostname = " https://yunduanyanfa.ningmengdou.com/login ";
        result = encodeURIComponent(hostname);
        url = "https://sso.ningmengdou.com/single/login?returnUrl=" + result;
        window.open(url, "_blank");
      } else if (item.appName == "檬豆云供应链管理系统") {
      } else if (item.appName == "集采平台") {
        window.open("https://mdy.ningmengdou.com");
      } else if (item.appName == "云MES") {
        let userid = "18660283726";
        console.log(userid);
        let jsonData = { U: userid, P: "12a", A: "acb" };
        console.log(jsonData);
        const encodedData = btoa(JSON.stringify(jsonData));
        console.log(encodedData);
        window.open(
          "http://mes.ningmengdou.com/default.html?parm=" + encodedData,
          "_blank"
        );
      } else {
        window.open("//" + item.webexperienceUrl, "_blank");
      }
    },
    getInvoiceData(id) {
      this.currentId = id;
      invoiceList().then((res) => {
        if (res.code === 200) {
          this.invoiceData = res.data;
          this.invoiceVisible = true;
        }
      });
    },
    submitForm() {
      let data = {
        invoiceMedium: "1",
        invoiceType: "1",
        issueType: "1",
        invoiceHeader: this.invoiceData.companyName,
        dutyParagraph: this.invoiceData.dutyParagraph,
        email: this.invoiceData.email,
        orderId: this.currentId,
        sendTo: this.invoiceData.userId,
      };
      applyInvoice(data).then((res) => {
        if (res.code === 200) {
          this.invoiceVisible = false;
          this.$message.success("操作成功!");
          this.getList();
        }
      });
    },
    cancelDialog() {
      this.invoiceVisible = false;
    },
    viewInvoiceData(id) {
      let params = {
        orderid: id,
      };
      downLoadInvoice(params).then((res) => {
        let url = res[0].url;
        window.open(url);
      });
    },
    confirmReceipt(id) {
      this.$confirm("确认后订单状态无法变更，确认收货吗？", "确认收货", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let data = {
            id,
            orderStatus: 5,
          };
          modifyStatus(data).then((res) => {
            if (res.code === 200) {
              this.$message.success("操作成功!");
              this.getList();
            }
          });
        })
        .catch(() => {});
    },
    goPay(id) {
      this.$router.push({
        path: "/payment",
        query: {
          id,
        },
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.app-container {
  background: #f4f5f9;
}
.content {
  width: 100%;
  padding: 40px;
  background: #ffffff;
  // height: calc(100vh - 150px);
  // background: rgb(242, 248, 255);
  .content_type {
    display: flex;
    width: 100%;
    margin-bottom: 30px;
    .title {
      width: 100px;
      padding-left: 20px;
      height: 30px;
      line-height: 30px;
      border-left: 4px solid #21c9b8;
      font-weight: 600;
      font-size: 18px;
    }
    .right_content {
      width: calc(100% - 100px);
      text-align: right;
    }
  }
  .tableStyle {
    .everyItem {
      width: 100%;
      height: 200px;
      box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.06);
      margin-top: 20px;
      padding: 20px;
      // background: #ffffff;
      .orderNumTime {
        display: flex;
      }
      .driver {
        width: 100%;
        height: 1px;
        background: #ccc;
        margin: 15px 0;
      }
      .item_content {
        width: 100%;
        // height: 100%;
        display: flex;
        align-items: center;
        .item_img {
          width: 14%;
          height: 110px;
          img {
            width: 100%;
            height: 100%;
          }
        }
        .item_desc {
          margin-left: 20px;
          width: 25%;
          .title {
            font-size: 16px;
            font-family: Source Han Sans CN;
            font-weight: 500;
            color: #333333;
          }
        }
        .item_amounts {
          width: 10%;
          text-align: right;
        }
        .driverVertical {
          width: 1px;
          height: 110px;
          background: #ccc;
          margin: 0 8%;
        }
      }
    }
  }
  .company-tab-pagination {
    width: 280px;
    margin-left: calc(45% - 200px);
    // margin: 0 auto;
    .btn-prev,
    .btn-next,
    .btn-quickprev {
      width: 32px;
      height: 32px;
      background: #ffffff;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      margin: 0 6px;
      color: #333;
    }
    .el-pager {
      .number {
        width: 32px;
        height: 32px;
        border: 1px solid #d9d9d9;
        background: #ffffff;
        border-radius: 4px;
        line-height: 32px;
        margin: 0 6px;
        &.active {
          background: #21c9b8;
          border: 1px solid #21c9b8;
          color: #fff;
        }
      }
    }
  }
}
</style>

<template>
  <div class="content">
    <div class="card-container cardStyle">
      <!-- 左侧 -->
      <div class="card_left">
        <div class="card_left_bottom">
          <div class="title">{{ detailsData.positionName }}</div>
          <div class="everyOption">
            <div class="optionName">薪资：</div>
            <div class="optionValue" v-if="salaryRangeList.length > 0 && detailsData.salaryRange">
              {{
                salaryRangeList.filter(
                  (item1) => item1.dictValue == detailsData.salaryRange
                )[0].dictLabel
              }}
            </div>
          </div>
          <div class="everyOption">
            <div class="optionName">年龄限制：</div>
            <div class="optionValue">{{ detailsData.ageLimit }}</div>
          </div>
          <div class="everyOption">
            <div class="optionName">用工单位：</div>
            <div class="optionValue">{{ detailsData.company }}</div>
          </div>
          <div class="everyOption">
            <div class="optionName">用工地点：</div>
            <div class="optionValue" v-if="locationList.length > 0 && detailsData.location">
              {{
                locationList.filter(
                  (item1) => item1.dictValue == detailsData.location
                )[0].dictLabel
              }}
            </div>
          </div>
          <div class="everyOption">
            <div class="optionName">联系方式：</div>
            <div class="optionValue">{{ detailsData.contactPhone }}</div>
          </div>
          <div class="buttonStyle" @click="jumpIntention">我有意向</div>
        </div>
      </div>
      <!-- 中间 -->
      <div class="card_center_line"></div>
      <!-- 右侧 -->
      <div class="card_right">
        <div>
          <div class="content_title">
            <div class="icon"></div>
            <div class="title">岗位要求</div>
          </div>
          <div class="desc">
            {{ detailsData.requirements }}
          </div>
        </div>
        <div style="margin-top: 32px">
          <div class="content_title">
            <div class="icon"></div>
            <div class="title">岗位职责</div>
          </div>
          <div class="desc">
            {{ detailsData.responsibilities }}
          </div>
        </div>
        <div style="margin-top: 32px">
          <div class="content_title">
            <div class="icon"></div>
            <div class="title">其他限制</div>
          </div>
          <div class="desc">
            {{ detailsData.otherLimits }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { listData } from "@/api/system/dict/data";
import { employDetailData } from "@/api/serviceSharing";

export default {
  name: "deviceDetail",
  data() {
    return {
      detailsData: {},
      id: null,
      salaryRangeList: [], // 薪资范围
      locationList: [], // 用工地点
    };
  },
  created() {
    this.id = this.$route.query.id;
    this.getSalaryRange();
    this.getLocation();
    this.getDetailData();
  },
  methods: {
    // 薪资范围字典
    getSalaryRange() {
      let params = { dictType: "salary_range" };
      listData(params).then((response) => {
        this.salaryRangeList = response.rows;
      });
    },
    // 用工地点字典
    getLocation() {
      let params = { dictType: "location" };
      listData(params).then((response) => {
        this.locationList = response.rows;
      });
    },
    getDetailData() {
      employDetailData(this.id).then((res) => {
        if (res.code === 200) {
          this.detailsData = res.data;
        }
      });
    },
    jumpIntention() {
      let userInfo = JSON.parse(sessionStorage.getItem("userinfo"));
      if (!userInfo?.memberCompanyName) {
        this.$confirm("您当前尚未关联企业，是否前往操作?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
          cancelButtonClass: "cancelButtonClass",
          confirmButtonClass: "customClass",
        })
          .then(() => {
            this.$router.push("/user/userCenter");
          })
          .catch(() => { });
        return;
      } else {
        this.$router.push(`/demandInterested?demandName=${this.detailsData.positionName}&updateTime=${this.detailsData.updateTime}&intentionType=7&fieldName=用工信息&intentionId=${this.detailsData.id}`);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  background-color: #f2f2f2;
  padding: 30px 0 61px 0;
}

.cardStyle {
  height: 534px;
  background-color: #ffffff;
  padding: 60px 56px 54px 50px;
  display: flex;
}

.card_left {
  .card_left_bottom {
    width: 220px;

    .title {
      font-family: Source Han Sans CN;
      font-weight: 500;
      font-size: 20px;
      color: #222222;
      margin-bottom: 25px;
    }

    .everyOption {
      display: flex;
      align-items: center;
      margin-top: 12px;

      .optionName {
        // height: 14px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #999999;
      }

      .optionValue {
        // height: 14px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #333333;
      }
    }

    .buttonStyle {
      margin-top: 32px;
      // margin-left: 55px;
      width: 220px;
      height: 50px;
      background: #21c9b8;
      box-shadow: 0px 3px 10px 0px rgba(33, 201, 184, 0.6);
      border-radius: 2px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 16px;
      color: #ffffff;
      text-align: center;
      line-height: 50px;
      cursor: pointer;
    }
  }
}

.card_center_line {
  width: 1px;
  height: 100%;
  background: #e1e1e1;
  margin-left: 60px;
  margin-right: 61px;
}

.card_right {
  width: 100%;
  overflow-y: auto;

  .content_title {
    display: flex;
    align-items: center;

    .icon {
      width: 4px;
      height: 20px;
      background: #21c9b8;
    }

    .title {
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 18px;
      color: #030a1a;
      margin-left: 10px;
    }
  }

  .desc {
    margin-top: 22px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    line-height: 24px;
  }
}
</style>

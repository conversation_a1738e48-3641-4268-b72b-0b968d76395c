/*
 * @Author: JHY
 * @Date: 2023-02-14 08:44:00
 * @LastEditors: zhc
 * @LastEditTime: 2023-04-26 15:28:47
 */
/*
 * @Author: jhy
 * @Date: 2023-01-29 16:34:18
 * @LastEditors: zhc
 * @LastEditTime: 2023-02-13 19:03:35
 */
import Vue from "vue";
import Router from "vue-router";

Vue.use(Router);

/* Layout */
import Layout from "@/layout";

/**
 * Note: 路由配置项
 *
 * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * query: '{"id": 1, "name": "ry"}' // 访问路由的默认传递参数
 * roles: ['admin', 'common']       // 访问路由的角色权限
 * permissions: ['a:a:a', 'b:b:b']  // 访问路由的菜单权限
 * meta : {
    noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示
    activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。
  }
 */

// 公共路由
export const constantRoutes = [
  {
    path: "/redirect",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "/redirect/:path(.*)",
        component: () => import("@/views/redirect"),
      },
    ],
  },
  {
    path: "/login",
    component: () => import("@/views/login"),
    hidden: true,
  },
  {
    path: "/sso/callback",
    component: () => import("@/views/sso-callback"),
    hidden: true,
  },
  {
    path: "/register",
    component: () => import("@/views/register"),
    hidden: true,
  },
  {
    path: "/404",
    component: () => import("@/views/error/404"),
    hidden: true,
  },
  {
    path: "/401",
    component: () => import("@/views/error/401"),
    hidden: true,
  },
  {
    path: "",
    name: "main",
    component: Layout,
    redirect: "index",
    children: [
      {
        path: "index",
        component: () => import("@/views/index"),
        name: "Index",
        // hidden: true,
        meta: {
          title: "首页",
          icon: "dashboard",
          affix: true,
        },
      },

      // 多余代码后续删
      // hidden: true 是否隐藏在顶部导航显示
      // 例如详情路由
      // {
      //   meta: {
      //     title: "机构详情",
      //     affix: true,
      //   },
      //   path: "enterpriseDetail",
      //   name: "enterpriseDetail",
      //   component: () =>
      //     import("@/views/purchaseSales/component/enterpriseList/detail"),
      //   hidden: true,
      // },

      {
        meta: {
          title: "集采共享",
          affix: true,
        },
        path: "shopping",
        name: "shopping",
        // component: () => import("@/views/compositeMall"),
      },
      {
        meta: {
          title: "制造共享",
          affix: true,
        },
        path: "manufacturingSharing",
        name: "manufacturingSharing",
        component: () => import("@/views/manufacturingSharing"),
      },

      {
        meta: {
          title: "服务共享",
          affix: true,
        },
        path: "serviceSharing",
        name: "serviceSharing",
        component: () => import("@/views/serviceSharing"),
      },

      {
        meta: {
          title: "创新共享",
          affix: true,
        },
        path: "innovationSharing",
        name: "innovationSharing",
        component: () => import("@/views/innovationSharing"),
      },

      {
        meta: {
          title: "资源协同",
          affix: true,
        },
        path: "supplyDemandDocking",
        name: "supplyDemandDocking",
        component: () => import("@/views/supplyDemandDocking"),
      },

      {
        meta: {
          title: "解决方案",
          affix: true,
        },
        path: "solution",
        name: "solution",
        component: () => import("@/views/solution"),
        hidden: true,
      },
      {
        meta: {
          title: "应用商店",
          affix: true,
        },
        path: "appStore",
        name: "appStore",
        component: () => import("@/views/appStore"),
        hidden: true,
      },

      {
        meta: {
          title: "复材展厅",
          affix: true,
        },
        path: "compositeExhibitionHall",
        name: "compositeExhibitionHall",
        component: () => import("@/views/compositeExhibitionHall"),
        hidden: true,
      },

      {
        meta: {
          title: "应用商店详情",
          affix: true,
        },
        path: "appStoreInfo",
        name: "appStoreInfo",
        component: () => import("@/views/appStore/info"),
        hidden: true,
      },

      {
        meta: {
          title: "关于我们",
          affix: true,
        },
        path: "aboutUs",
        name: "aboutUs",
        component: () => import("@/views/aboutUs"),
      },

      {
        meta: {
          title: "发布",
          affix: true,
        },
        path: "release",
        name: "release",
        component: () => import("@/views/release"),
        hidden: true,
      },

      {
        meta: {
          title: "设备共享详情",
          affix: true,
        },
        path: "deviceDetail",
        name: "deviceDetail",
        component: () =>
          import("@/views/manufacturingSharing/components/deviceDetail"),
        hidden: true,
      },

      {
        meta: {
          title: "车间共享详情",
          affix: true,
        },
        path: "workshopDetail",
        name: "workshopDetail",
        component: () =>
          import("@/views/manufacturingSharing/components/workshopDetail"),
        hidden: true,
      },

      {
        meta: {
          title: "生产订单详情",
          affix: true,
        },
        path: "productOrderDetail",
        name: "productOrderDetail",
        component: () =>
          import("@/views/manufacturingSharing/components/productOrderDetail"),
        hidden: true,
      },

      {
        meta: {
          title: "订单共享-立即入驻",
          affix: true,
        },
        path: "joinNow",
        name: "joinNow",
        component: () =>
          import("@/views/manufacturingSharing/components/joinNow"),
        hidden: true,
      },
      {
        meta: {
          title: "众筹科研-立即报名",
          affix: true,
        },
        path: "joinSupply",
        name: "joinSupply",
        component: () =>
          import("@/views/innovationSharing/components/scienceFunding/join"),
        hidden: true,
      },

      // 制造共享-生产订单-我要接单
      {
        meta: {
          title: "我要接单",
          affix: true,
        },
        path: "receiveOrder",
        name: "receiveOrder",
        component: () =>
          import("@/views/manufacturingSharing/components/receiveOrder"),
        hidden: true,
      },

      // 制造共享-工序外协-我有意向
      {
        meta: {
          title: "我有意向",
          affix: true,
        },
        path: "interested",
        name: "interested",
        component: () =>
          import("@/views/manufacturingSharing/components/interested"),
        hidden: true,
      },

      // 制造共享-工序外协-我有意向
      {
        meta: {
          title: "入驻工厂详情",
          affix: true,
        },
        path: "factoryDetail",
        name: "factoryDetail",
        component: () =>
          import("@/views/manufacturingSharing/components/factoryDetail"),
        hidden: true,
      },

      {
        meta: {
          title: "人才库详情",
          affix: true,
        },
        path: "talentDetail",
        name: "talentDetail",
        component: () =>
          import("@/views/serviceSharing/components/talentPool/detail"),
        hidden: true,
      },

      {
        meta: {
          title: "服务共享-人才库-立即入驻",
          affix: true,
        },
        path: "talentJoinNow",
        name: "talentJoinNow",
        component: () =>
          import("@/views/serviceSharing/components/talentPool/joinNow"),
        hidden: true,
      },
      {
        meta: {
          title: "服务共享-人才服务-衡水",
          affix: true,
        },
        path: "talentJoinHengshui",
        name: "talentJoinHengshui",
        component: () =>
          import("@/views/serviceSharing/components/talentPool/joinHengshui"),
        hidden: true,
      },
      {
        meta: {
          title: "服务共享-人才服务-培训",
          affix: true,
        },
        path: "talentJoinTrain",
        name: "talentJoinTrain",
        component: () =>
          import("@/views/serviceSharing/components/talentPool/joinTrain"),
        hidden: true,
      },
      {
        meta: {
          title: "用工信息详情",
          affix: true,
        },
        path: "employmentInfoDetail",
        name: "employmentInfoDetail",
        component: () =>
          import("@/views/serviceSharing/components/employmentInfo/detail"),
        hidden: true,
      },
      {
        meta: {
          title: "检测共享详情",
          affix: true,
        },
        path: "detectingSharingDetail",
        name: "detectingSharingDetail",
        component: () =>
          import("@/views/serviceSharing/components/detectingSharing/detail"),
        hidden: true,
      },
      {
        meta: {
          title: "实验室共享详情",
          affix: true,
        },
        path: "laboratorySharingDetail",
        name: "laboratorySharingDetail",
        component: () =>
          import("@/views/serviceSharing/components/laboratorySharing/detail"),
        hidden: true,
      },

      // 创新共享-文件共享-我有意向
      {
        meta: {
          title: "我有意向",
          affix: true,
        },
        path: "fileInterested",
        name: "fileInterested",
        component: () =>
          import("@/views/innovationSharing/components/fileSharing/interested"),
        hidden: true,
      },
      {
        meta: {
          title: "证书查询结果",
          affix: true,
        },
        path: "certificateQueryResult",
        name: "certificateQueryResult",
        component: () =>
          import("@/views/serviceSharing/components/certificateQuery/result"),
        hidden: true,
      },
      {
        meta: {
          title: "需求详情",
          affix: true,
        },
        path: "demandDetail",
        name: "demandDetail",
        component: () =>
          import("@/views/supplyDemandDocking/components/demandDetail"),
        hidden: true,
      },
      {
        meta: {
          title: "检测需求详情",
          affix: true,
        },
        path: "detectionDetail",
        name: "detectionDetail",
        component: () =>
          import("@/views/supplyDemandDocking/components/detectionDetail"),
        hidden: true,
      },
      {
        meta: {
          title: "供给详情",
          affix: true,
        },
        path: "supplyDetail",
        name: "supplyDetail",
        component: () =>
          import("@/views/supplyDemandDocking/components/supplyDetail"),
        hidden: true,
      },
      {
        meta: {
          title: "资讯详情",
          affix: true,
        },
        path: "dynamicInfoDetail",
        name: "dynamicInfoDetail",
        component: () => import("@/views/aboutUs/components/dynamicInfoDetail"),
        hidden: true,
      },
      // {
      //   meta: {
      //     title: "设备共享",
      //     affix: true,
      //   },
      //   path: "deviceSharing",
      //   name: "deviceSharing",
      //   component: () =>
      //     import("@/views/manufacturingSharing/components/deviceSharing"),
      //   hidden: true,
      // },

      // {
      //   meta: {
      //     title: "在线申报",
      //     affix: true,
      //   },
      //   path: "addPolicy",
      //   name: "AddPolicy",
      //   component: () => import("@/views/form/addPolicy.vue"),
      //   hidden: true,
      // },

      // {
      //   meta: {
      //     title: "添加需求/供给",
      //     affix: true,
      //   },
      //   path: "addSource",
      //   name: "AddSource",
      //   component: () => import("@/views/form/addDemandAndSupply.vue"),
      //   hidden: true,
      // },
      // {
      //   meta: {
      //     title: "机构详情",
      //     affix: true,
      //   },
      //   path: "enterpriseDetail",
      //   name: "enterpriseDetail",
      //   component: () =>
      //     import("@/views/purchaseSales/component/enterpriseList/detail"),
      //   hidden: true,
      // },
      // {
      //   meta: {
      //     title: "专家智库",
      //     affix: true,
      //   },
      //   path: "expertLibrary",
      //   name: "expertLibrary",
      //   component: () =>
      //     import("@/views/purchaseSales/component/expertLibrary"),
      //   hidden: true,
      // },
      // {
      //   meta: {
      //     title: "专家详情",
      //     affix: true,
      //   },
      //   path: "expertDetail",
      //   name: "expertDetail",
      //   component: () =>
      //     import("@/views/purchaseSales/component/expertLibrary/detail"),
      //   hidden: true,
      // },
      // {
      //   meta: {
      //     title: "案例详情",
      //     affix: true,
      //   },
      //   path: "caseDetail",
      //   name: "caseDetail",
      //   component: () => import("@/views/classicCase/caseDetail"),
      //   hidden: true,
      // },
      {
        meta: {
          title: "方案详情",
          affix: true,
        },
        path: "solutionDetail",
        name: "solutionDetail",
        component: () => import("@/views/solution/detail"),
        hidden: true,
      },
      // {
      //   meta: {
      //     title: "典型案例",
      //     affix: true,
      //   },
      //   path: "classicCase",
      //   name: "classicCase",
      //   component: () => import("@/views/classicCase"),
      //   hidden: true,
      // },
      // {
      //   meta: {
      //     title: "应用市场",
      //     affix: true,
      //   },
      //   path: "appliMarket",
      //   name: "appliMarket",
      //   component: () => import("@/views/appliMarket"),
      //   hidden: true,
      // },
      // {
      //   meta: {
      //     title: "下单支付",
      //     affix: true,
      //   },
      //   path: "payment",
      //   name: "payment",
      //   component: () => import("@/views/appliMarket/payment"),
      //   hidden: true,
      // },
      // {
      //   meta: {
      //     title: "支付成功",
      //     affix: true,
      //   },
      //   path: "paySuccess",
      //   name: "paySuccess",
      //   component: () => import("@/views/appliMarket/paySuccess"),
      //   hidden: true,
      // },
      {
        meta: {
          title: "购买应用",
          affix: true,
        },
        path: "purchaseapp",
        name: "purchaseapp",
        component: () => import("@/views/appliMarket/purchaseapp"),
        hidden: true,
      },
      // {
      //   meta: {
      //     title: "服务机构",
      //     affix: true,
      //   },
      //   path: "enterpriseList",
      //   name: "enterpriseList",
      //   component: () =>
      //     import("@/views/purchaseSales/component/enterpriseList"),
      //   hidden: true,
      // },
      // {
      //   meta: {
      //     title: "专家智库",
      //     affix: true,
      //   },
      //   path: "thinkTank",
      //   name: "thinkTank",
      //   component: () => import("@/views/thinkTank"),
      // },
      // {
      //   meta: {
      //     title: "新闻中心",
      //     affix: true,
      //   },
      //   path: "newsCenter",
      //   name: "newsCenter",
      //   component: () => import("@/views/newsCenter"),
      // },
      // {
      //   meta: {
      //     title: "新闻详情",
      //     affix: true,
      //   },
      //   path: "newsDetail",
      //   name: "newsDetail",
      //   component: () => import("@/views/newsCenter/detail"),
      //   hidden: true,
      // },
      {
        path: "publishEmInformation",
        component: () =>
          import("@/views/system/user/publishEmInformation/index"),
        name: "",
        meta: {
          title: "发布用工信息",
        },
        hidden: true,
      },
      {
        path: "publishEquipment",
        component: () => import("@/views/system/user/publishEquipment/index"),
        name: "",
        meta: {
          title: "发布设备信息",
        },
        hidden: true,
      },
      {
        path: "publishWorkshop",
        component: () => import("@/views/system/user/publishWorkshop/index"),
        name: "",
        meta: {
          title: "发布车间信息",
        },
        hidden: true,
      },

      {
        path: "demandInterested",
        component: () =>
          import("@/views/supplyDemandDocking/components/interested"),
        name: "",
        meta: {
          title: "需求-我有意向",
        },
        hidden: true,
      },
    ],
  },
  {
    path: "/user",
    component: Layout,
    hidden: true,
    redirect: "noredirect",
    children: [
      // 首页
      {
        path: "profile",
        component: () => import("@/views/system/user/profile/index"),
        name: "Profile",
        meta: {
          title: "个人中心",
        },
      },
      {
        path: "supplyDemand",
        component: () => import("@/views/system/user/supplyDemand/index"),
        name: "",
        meta: {
          title: "供需管理",
        },
      },
      {
        path: "reApplication",
        component: () => import("@/views/system/user/reApplication/index"),
        name: "",
        meta: {
          title: "入驻申请",
        },
      },

      {
        path: "sharedOrders",
        component: () => import("@/views/system/user/sharedOrders/index"),
        name: "",
        meta: {
          title: "共享订单",
        },
      },

      {
        path: "emInformation",
        component: () => import("@/views/system/user/emInformation/index"),
        name: "",
        meta: {
          title: "用工信息",
        },
      },

      {
        path: "equipmentManagement",
        component: () =>
          import("@/views/system/user/equipmentManagement/index"),
        name: "",
        meta: {
          title: "设备管理",
        },
      },
      // 车间管理
      {
        path: "workshopManagement",
        component: () => import("@/views/system/user/workshopManagement/index"),
        name: "",
        meta: {
          title: "车间管理",
        },
      },

      {
        path: "dockingRecords",
        component: () => import("@/views/system/user/dockingRecords/index"),
        name: "",
        meta: {
          title: "对接记录",
        },
      },
      {
        path: "dockingRecordsDetail",
        component: () => import("@/views/system/user/dockingRecords/detail"),
        name: "",
        meta: {
          title: "对接记录详情",
        },
      },
      {
        path: "userCenter",
        component: () => import("@/views/system/user/userCenter/index"),
        name: "",
        meta: {
          title: "用户中心",
        },
      },
      {
        path: "talentJoinNow",
        component: () => import("@/views/system/user/talentJoinNow/index"),
        name: "",
        meta: {
          title: "人才入驻",
        },
      },
      {
        path: "talentDetail",
        component: () => import("@/views/system/user/talentDetail/index"),
        name: "",
        meta: {
          title: "人才详情",
        },
      },
      {
        path: "spCertification",
        component: () => import("@/views/system/user/spCertification/index"),
        name: "spCertification",
        meta: {
          title: "认证设置",
        },
      },
      // {
      //   path: "im",
      //   component: () => import("@/views/system/user/im/index"),
      //   name: "IM",
      //   meta: {
      //     title: "即时沟通",
      //     icon: "user",
      //   },
      // },
      // {
      //   path: "userInfo",
      //   component: () => import("@/views/system/user/userInfo/index"),
      //   name: "UserInfo",
      //   meta: {
      //     title: "个人资料",
      //   },
      // },
      // {
      //   path: "invoiceInfo",
      //   component: () => import("@/views/system/user/invoiceInfo/index"),
      //   name: "invoiceInfo",
      //   meta: {
      //     title: "发票信息",
      //   },
      // },
      // {
      //   path: "companyInfo",
      //   component: () => import("@/views/system/user/companyInfo/index"),
      //   name: "CompanyInfo",
      //   meta: {
      //     title: "公司信息",
      //   },
      // },
      // {
      //   path: "myCollect",
      //   component: () => import("@/views/system/user/myCollect/index"),
      //   name: "myCollect",
      //   meta: {
      //     title: "我的收藏",
      //   },
      // },
      // {
      //   path: "mySubscriptions",
      //   component: () => import("@/views/system/user/mySubscriptions/index"),
      //   name: "mySubscriptions",
      //   meta: {
      //     title: "我的订阅",
      //   },
      // },
      // {
      //   path: "orderSubDetail",
      //   component: () => import("@/views/system/user/mySubscriptions/detail"),
      //   name: "orderSubDetail",
      //   meta: {
      //     title: "订阅详情",
      //   },
      // },
      // {
      //   path: "application",
      //   component: () => import("@/views/system/user/application/index"),
      //   name: "application",
      //   meta: {
      //     title: "应用管理",
      //   },
      // },
      // {
      //   path: "publishAppli",
      //   component: () => import("@/views/system/user/application/publish"),
      //   name: "publishAppli",
      //   meta: {
      //     title: "发布应用",
      //   },
      // },
      // {
      //   path: "appliDetail",
      //   component: () => import("@/views/system/user/application/detail"),
      //   name: "appliDetail",
      //   meta: {
      //     title: "应用详情",
      //   },
      // },
      // {
      //   path: "orderManage",
      //   component: () => import("@/views/system/user/orderManage/index"),
      //   name: "orderManage",
      //   meta: {
      //     title: "订单管理",
      //   },
      // },
      // {
      //   path: "orderManageDetail",
      //   component: () => import("@/views/system/user/orderManage/detail"),
      //   name: "orderManageDetail",
      //   meta: {
      //     title: "订单详情",
      //   },
      // },
      // {
      //   path: "policyDeclare",
      //   component: () => import("@/views/system/user/policyDeclare/index"),
      //   name: "PolicyDeclare",
      //   meta: {
      //     title: "政策申报",
      //   },
      // },
      // {
      //   path: "policyDeclareDetail",
      //   component: () =>
      //     import("@/views/system/user/policyDeclare/detail/index"),
      //   name: "PolicyDeclareDetail",
      //   meta: {
      //     title: "政策申报详情",
      //   },
      // },
      // {
      //   path: "noninductive",
      //   component: () => import("@/views/system/user/noninductive/index"),
      //   name: "Noninductive",
      //   meta: {
      //     title: "",
      //   },
      // },
      // {
      //   path: "noninductiveDetail",
      //   component: () =>
      //     import("@/views/system/user/noninductive/detail/index"),
      //   name: "NoninductiveDetail",
      //   meta: {
      //     title: "详情",
      //   },
      // },
      // {
      //   path: "commercial",
      //   component: () => import("@/views/system/user/commercial/index"),
      //   name: "commercial",
      //   meta: {
      //     title: "商机推荐",
      //   },
      // },
      // {
      //   path: "companyDemand",
      //   component: () => import("@/views/system/user/companyDemand/index"),
      //   name: "CompanyDemand",
      //   meta: {
      //     title: "企业需求",
      //   },
      // },
      // {
      //   path: "companyDemandDetail",
      //   component: () =>
      //     import("@/views/system/user/companyDemand/detail/index"),
      //   name: "CompanyDemandDetail",
      //   meta: {
      //     title: "企业需求详情",
      //   },
      // },
      // {
      //   path: "companyDemandDetail1",
      //   component: () =>
      //     import("@/views/system/user/companyDemand/detail/index1"),
      //   name: "CompanyDemandDetail1",
      //   meta: {
      //     title: "企业需求详情",
      //   },
      // },
      // {
      //   path: "companyApply",
      //   component: () => import("@/views/system/user/companyApply/index"),
      //   name: "CompanyApply",
      //   meta: {
      //     title: "企业供给",
      //   },
      // },
      // {
      //   path: "companyApplyDetail",
      //   component: () =>
      //     import("@/views/system/user/companyApply/detail/index"),
      //   name: "companyApplyDetail",
      //   meta: {
      //     title: "企业供给详情",
      //   },
      // },
      // {
      //   path: "companyApplyDetail1",
      //   component: () =>
      //     import("@/views/system/user/companyApply/detail/index1"),
      //   name: "companyApplyDetail1",
      //   meta: {
      //     title: "企业供给详情",
      //   },
      // },
      // {
      //   path: "abutmentRecord",
      //   component: () => import("@/views/system/user/abutmentRecord/index"),
      //   name: "AbutmentRecord",
      //   meta: {
      //     title: "对接记录",
      //   },
      // },
      // {
      //   path: "approveSetting",
      //   component: () => import("@/views/system/user/approveSetting/index"),
      //   name: "ApproveSetting",
      //   meta: {
      //     title: "认证设置",
      //   },
      // },
      // {
      //   path: "teamManage",
      //   component: () => import("@/views/system/user/teamManage/index"),
      //   name: "TeamManage",
      //   meta: {
      //     title: "团队管理",
      //   },
      // },
      {
        path: "notice",
        component: () => import("@/views/system/user/notice/index"),
        name: "Notice",
        meta: {
          title: "通知中心",
        },
      },
      // {
      //   path: "noticeDetail",
      //   component: () => import("@/views/system/user/notice/detail/index"),
      //   name: "NoticeDetail",
      //   meta: {
      //     title: "通知详情",
      //   },
      // },
    ],
  },
];

// 动态路由，基于用户权限动态去加载
export const dynamicRoutes = [
  {
    path: "/system/user-auth",
    component: Layout,
    hidden: true,
    permissions: ["system:user:edit"],
    children: [
      {
        path: "role/:userId(\\d+)",
        component: () => import("@/views/system/user/authRole"),
        name: "AuthRole",
        meta: {
          title: "分配角色",
          activeMenu: "/system/user",
        },
      },
    ],
  },
  {
    path: "/system/role-auth",
    component: Layout,
    hidden: true,
    permissions: ["system:role:edit"],
    children: [
      {
        path: "user/:roleId(\\d+)",
        component: () => import("@/views/system/role/authUser"),
        name: "AuthUser",
        meta: {
          title: "分配用户",
          activeMenu: "/system/role",
        },
      },
    ],
  },
  {
    path: "/system/dict-data",
    component: Layout,
    hidden: true,
    permissions: ["system:dict:list"],
    children: [
      {
        path: "index/:dictId(\\d+)",
        component: () => import("@/views/system/dict/data"),
        name: "Data",
        meta: {
          title: "字典数据",
          activeMenu: "/system/dict",
        },
      },
    ],
  },
  {
    path: "/monitor/job-log",
    component: Layout,
    hidden: true,
    permissions: ["monitor:job:list"],
    children: [
      {
        path: "index/:jobId(\\d+)",
        component: () => import("@/views/monitor/job/log"),
        name: "JobLog",
        meta: {
          title: "调度日志",
          activeMenu: "/monitor/job",
        },
      },
    ],
  },
  {
    path: "/tool/gen-edit",
    component: Layout,
    hidden: true,
    permissions: ["tool:gen:edit"],
    children: [
      {
        path: "index/:tableId(\\d+)",
        component: () => import("@/views/tool/gen/editTable"),
        name: "GenEdit",
        meta: {
          title: "修改生成配置",
          activeMenu: "/tool/gen",
        },
      },
    ],
  },
];

// 防止连续点击多次路由报错
let routerPush = Router.prototype.push;
Router.prototype.push = function push(location) {
  return routerPush.call(this, location).catch((err) => err);
};

export default new Router({
  mode: "history", // 去掉url中的#
  scrollBehavior: () => ({
    y: 0,
  }),
  routes: constantRoutes,
});

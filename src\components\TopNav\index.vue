<template>
  <el-menu :default-active="activeMenu" menu-trigger="hover" mode="horizontal" background-color="transparent"
    @select="handleSelect">
    <template v-for="(item, index) in menus">
      <el-menu-item :style="{ '--theme': '#45c9b8' }" :index="item.path" class="nav_span" :key="index">
        {{ item.meta.title }}
      </el-menu-item>
    </template>
    <!-- 弹窗---- -->
    <!-- 供需对接 -->
    <div class="supplyDemandDocking">
      <div class="sub-purchase-content">
        <div class="content_item head_title_line" v-for="(item, index) in supplyDemandDocking" :key="index"
          @click="goSupplyDemandDocking(index)">
          <div class="title">{{ item.title }}</div>
          <div class="titleLine"></div>
          <div class="desc">{{ item.desc }}</div>
        </div>
      </div>
    </div>
    <!-- 制造共享 -->
    <div class="manufacturingShare">
      <div class="sub-purchase-content">
        <div class="content_item head_title_line" v-for="(item, index) in manufacturingSharing" :key="index"
          @click="goManufacturingSharing(index)">
          <div class="title">{{ item.title }}</div>
          <div class="titleLine"></div>
          <div class="desc">{{ item.desc }}</div>
        </div>
      </div>
    </div>
    <!-- 服务共享 -->
    <div class="serviceShare">
      <div class="sub-purchase-content">
        <div class="content_item head_title_line" v-for="(item, index) in serviceSharing" :key="index"
          @click="goServiceSharing(index)">
          <div class="title">{{ item.title }}</div>
          <div class="titleLine"></div>
          <div class="desc">{{ item.desc }}</div>
        </div>
      </div>
    </div>
    <!-- 创新共享 -->
    <div class="innovationShare">
      <div class="sub-purchase-content">
        <div class="content_item head_title_line" v-for="(item, index) in innovationSharing" :key="index"
          @click="goInnovationSharing(index)">
          <div class="title">{{ item.title }}</div>
          <div class="titleLine"></div>
          <div class="desc">{{ item.desc }}</div>
        </div>
      </div>
    </div>
    <!-- 关于我们 -->
    <div class="aboutUs">
      <div class="sub-purchase-content">
        <div class="content_item head_title_line" v-for="(item, index) in aboutUsList" :key="index"
          @click="goAboutUs(index)">
          <div class="title">{{ item.title }}</div>
          <div class="titleLine"></div>
          <div class="desc">{{ item.desc }}</div>
        </div>
      </div>
    </div>
    <!-- 弹窗 -->

    <!-- <div class="sub-purchase-popper">
      <div class="sub-purchase-content">
        <div class="sub-purchase-left">
          <div class="sub-left-img">
            <img src="../../assets/purchaseSales/purchaseNav.png" alt="" />
          </div>
          <div class="sub-left-title">采销互联</div>
          <div class="sub-left-info">
            利用平台实现区域互采互销，支持产业链上下游企业间的供需对接，切实推进本地企业产品互采互用，实现区域内企业互利共赢，共同发展。
          </div>
        </div>
        <div class="sub-purchase-right">
          <div class="sub-right-each">
            <div class="sub-right-item" @click="goDemandHall">
              <div class="sub-right-title">
                需求大厅
                <span class="sub-right-arrow">>></span>
              </div>
              <div class="sub-right-info">
                汇集企业发展的瓶颈和难题，谁有能力谁来揭榜
              </div>
            </div>
            <div class="sub-right-item" @click="goSupply">
              <div class="sub-right-title">
                供给大厅
                <span class="sub-right-arrow">>></span>
              </div>
              <div class="sub-right-info">
                整合产业链资源，工业信息互联供需精准对接
              </div>
            </div>
            <div class="sub-right-item" @click="goCompany">
              <div class="sub-right-title">
                企业名录
                <span class="sub-right-arrow">>></span>
              </div>
              <div class="sub-right-info">
                企业黄页大全，产业链上下游企业精准筛选
              </div>
            </div>
          </div>
          <div class="sub-right-each">
            <div class="sub-right-item" @click="goExpertLibrary">
              <div class="sub-right-title">
                专家智库
                <span class="sub-right-arrow">>></span>
              </div>
              <div class="sub-right-info">
                开放科研院所、行业专家资源、解决企业卡脖子难题
              </div>
            </div>
            <div class="sub-right-item" @click="goActivity">
              <div class="sub-right-title">
                活动广场
                <span class="sub-right-arrow">>></span>
              </div>
              <div class="sub-right-info">参与最新、最全的线上、线下活动</div>
            </div>
          </div>
        </div>
      </div>
    </div> -->
    <!-- <div class="sub-purchase-left">
          <div class="sub-left-img">
            <img src="../../assets/policyDeclare/policyNav.png" alt="" />
          </div>
          <div class="sub-left-title">政策大厅</div>
          <div class="sub-left-info" style="text-align: center">
            中央到镇街五级政府政策，一键查询
          </div>
        </div>
        <div class="sub-purchase-right">
          <div class="sub-right-each">
            <div class="sub-right-item" @click="goPolicyInformation">
              <div class="sub-right-title">
                政策资讯
                <span class="sub-right-arrow">>></span>
              </div>
              <div class="sub-right-info">最新政策 在线查看</div>
            </div>
            <div class="sub-right-item">
              <div class="sub-right-title" @click="add">
                政策画像
                <span class="sub-right-arrow">>></span>
              </div>
              <div class="sub-right-info">权威便捷、高效查询</div>
            </div>
            <div class="sub-right-item" @click="goPolicyApply">
              <div class="sub-right-title">
                政策申报
                <span class="sub-right-arrow">>></span>
              </div>
              <div class="sub-right-info">精准查询 申报无忧</div>
            </div>
          </div>
        </div> -->
  </el-menu>
</template>

<script>
import { find, propEq, replace, filter, map } from "ramda";

import { constantRoutes } from "@/router";
export default {
  data() {
    const mainMenu = find(propEq("name", "main"), constantRoutes) || {};
    const menus = filter((item) => !item.hidden, mainMenu.children || []);
    const $menus = map((item) => item.path, menus);
    const $path = replace("/", "", this.$route.path);
    return {
      activeMenu: $menus.includes($path) ? $path : "index",
      menus,
      paths: $menus,
      // 顶部栏初始数
      visibleNumber: 6,
      // 当前激活菜单的 index
      currentIndex: undefined,
      mobile: "",
      key: "QmRlODJTVGhkNg==",
      type: "cG9ydHJhaXQ=",
      base64EncodeChars:
        "ABCDEFGHIJKLMNOPORSTUWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",
      text: {},
      wwk: {},
      cashType: "cG9saWN5Y2FzaA==",
      supplyDemandDocking: [
        {
          title: "需求大厅",
          desc: "企业发布需求",
        },
        {
          title: "供给大厅",
          desc: "企业发布供给",
        },
        {
          title: "解决方案",
          desc: "提供行业问题解决策略与案例",
        },
        {
          title: "应用商店",
          desc: "汇聚工业工具与应用的下载平台",
        },
        {
          title: "复材展厅",
          desc: "展示复合材料产品与技术的平台",
        },
      ],
      manufacturingSharing: [
        {
          title: "设备共享",
          desc: "线上提供模具、工业设备租赁，企业在线申请",
        },
        {
          title: "车间共享",
          desc: "提供车间租赁，企业在线申请。",
        },
        {
          title: "订单共享",
          desc: "企业发布生产订单协同需求，工厂接单响应生产交付。",
        },
      ],
      serviceSharing: [
        {
          title: "人才服务",
          desc: "企业招聘、个人简历或能力信息，人才供需对接。",
        },
        {
          title: "企业用工",
          desc: "劳务用工信息汇集，海量高薪职位等你来选。",
        },
        {
          title: "检验检测",
          desc: "专业第三方检测机构提供检验检测服务，检测项目一键申请。",
        },
        {
          title: "实验室共享",
          desc: "实验室资源共享，低成本实现创新。",
        }
      ],
      innovationSharing: [
        {
          title: "创业孵化",
          desc: "提供创业基地给初创企业，了解加入共享创新园区。",
        },
        {
          title: "文件共享",
          desc: "专利、标准、商标等知识库信息开放。",
        },
        {
          title: "众筹科研",
          desc: "汇聚科研众智众筹资金，搭建成果孵化、资源共享的创新协作平台",
        },
      ],
      aboutUsList: [
        {
          title: "平台介绍",
          desc: "以“共享智造”赋能特色产业集群，实现资源利用的最大化。",
        },
        {
          title: "动态资讯",
          desc: "提供最新的新闻资讯、让您迅掌握产业动态。",
        },
      ],
    };
  },
  watch: {
    $route(route) {
      const { path } = route;
      const $path = replace("/", "", path);
      if (this.paths.includes($path)) {
        if ($path !== this.activeMenu) {
          this.activeMenu = $path;
        }
      }
    },
  },
  computed: {
    theme() {
      return this.$store.state.settings.theme;
    },
  },

  methods: {
    goSupplyDemandDocking(index) {
      if (index == 2) {
        this.$router.push({
          path: "/solution",
        })
      } else if (index == 3) {
        this.$router.push({
          path: "/appStore",
        })
      } else if (index == 4) {
        this.$router.push({
          path: "/compositeExhibitionHall",
        })
      } else {
        this.$router.push("/supplyDemandDocking?index=" + index);
      }
    },
    goManufacturingSharing(index) {
      this.$router.push("/manufacturingSharing?index=" + index);
    },
    goServiceSharing(index) {
      this.$router.push("/serviceSharing?index=" + index);
    },

    goInnovationSharing(index) {
      this.$router.push("/innovationSharing?index=" + index);
    },

    goAboutUs(index) {
      this.$router.push("/aboutUs?index=" + index);
    },

    // add() {
    //   if (JSON.parse(localStorage.getItem("sessionObj"))) {
    //     this.text = JSON.parse(localStorage.getItem("sessionObj"));
    //     this.wwk = JSON.parse(this.text.data);
    //     this.mobile = this.wwk.username;
    //     this.mobile = this.$Base64.encode(this.mobile);
    //     // this.type = this.$Base64.encode(this.type);
    //     window.open(
    //       `https://cyqyfw.com/index/user/login?key=${this.key}&type=${this.type}&mobile=${this.mobile}`
    //     );
    //   } else {
    //     // window.open('https://120.221.94.235/index/policy/portrait.html')
    //     // window.open('https://cyqyfw.com ')
    //     window.open("https://cyqyfw.com/index/policy/portrait.html");
    //   }
    // },
    // senselessCashing() {
    //   if (JSON.parse(localStorage.getItem("sessionObj"))) {
    //     this.text = JSON.parse(localStorage.getItem("sessionObj"));
    //     this.wwk = JSON.parse(this.text.data);
    //     this.mobile = this.wwk.username;
    //     this.mobile = this.$Base64.encode(this.mobile);
    //     window.open(
    //       `https://cyqyfw.com/index/user/login?key=${this.key}&type=${this.cashType}&mobile=${this.mobile}`
    //     );
    //   } else {
    //     // window.open("https://120.221.94.235");
    //     window.open("https://cyqyfw.com ");
    //   }
    // },
    handleSelect(index) {
      console.log(index, "----------");
      if (index && index !== "shopping") {
        this.$router.push(`/${index}`);
      } else {
        window.open("http://61.240.145.100:1001/");
      }
      //  else {
      //   this.senselessCashing();
      // }
    },
    // // 跳转到需求大厅
    // goDemandHall() {
    //   this.$router.push("/demandHall");
    // },
    // // 跳转到资源大厅
    // goSupply() {
    //   this.$router.push("/resourceHall");
    // },
    // // 跳转到企业名录
    // goCompany() {
    //   this.$router.push("/enterpriseList");
    // },
    // // 跳转到专家智库
    // goExpertLibrary() {
    //   this.$router.push("/expertLibrary");
    // },
    // // 跳转到活动广场
    // goActivity() {
    //   this.$router.push("/activitySquare");
    // },
    // // 跳转政策资讯
    // goPolicyInformation() {
    //   this.$router.push("/policy?specialLoc=policyInfo");
    // },
    // // 跳转政策画像
    // goPolicyPortrait() {
    //   this.$router.push("/policy?specialLoc=policyProtrait");
    // },
    // // 跳转政策申报
    // goPolicyApply() {
    //   this.$router.push("/policy?specialLoc=applyPolicy");
    // },
  },
};
</script>

<style lang="scss" scoped>
.is-active {
  background-color: #fff !important;
}

.head_title_line {
  .titleLine {
    position: relative;

    &::after {
      content: "";
      position: absolute;
      bottom: 0;
      width: 0%;
      width: 0%;
      height: 2px;
      background-color: #37c9b8;
      transition: all 0.35s ease-in;
      left: 0;
      z-index: 1;
    }
  }

  &:hover {
    .titleLine::after {
      width: 100%;
    }
  }
}

.nav_span {
  position: relative;

  &::before {
    content: "";
    position: absolute;
    left: 50%;
    bottom: -3px;
    width: 100%;
    height: 4px;
    background-color: #37c9b8;
    transform-origin: center;
    transform: translate(-50%, 0) scaleX(0);
    transition: transform 0.3s ease-in;
  }

  &:hover {
    background-color: #fff !important;

    &::before {
      transform: translate(-50%, 0) scaleX(1);
    }
  }
}

.topmenu-container.el-menu--horizontal>.el-menu-item {
  height: 80px !important;
  line-height: 80px !important;
  color: #333333 !important;
  font-size: 16px !important;
  font-weight: 500 !important;
  padding: 0 10px !important;
  margin: 0 10px !important;
}

.topmenu-container.el-menu--horizontal>.el-menu-item:hover {
  color: #{"var(--theme)"} !important;
}

.topmenu-container.el-menu--horizontal>.el-menu-item.is-active,
.el-menu--horizontal>.el-submenu.is-active .el-submenu__title {
  color: #21c9b8 !important;
  border-bottom: 4px solid #21c9b8 !important;
  // color: #fff !important;
  // background-color: #{"var(--theme)"} !important;
  // border-bottom: none !important;
}

/* submenu item */
.topmenu-container.el-menu--horizontal>.el-submenu .el-submenu__title {
  height: 80px !important;
  line-height: 80px !important;
  color: #333333 !important;
  padding: 0 5px !important;
  margin: 0 10px !important;
  font-size: 16px !important;
  font-weight: 500 !important;
}

.topmenu-container.el-menu--horizontal>.el-menu-item:nth-child(6):hover~.supplyDemandDocking {
  display: block;
}

.topmenu-container.el-menu--horizontal>.el-menu-item:nth-child(3):hover~.manufacturingShare {
  display: block;
}

.topmenu-container.el-menu--horizontal>.el-menu-item:nth-child(4):hover~.serviceShare {
  display: block;
}

.topmenu-container.el-menu--horizontal>.el-menu-item:nth-child(5):hover~.innovationShare {
  display: block;
}

.topmenu-container.el-menu--horizontal>.el-menu-item:nth-child(7):hover~.aboutUs {
  display: block;
}

.supplyDemandDocking,
.manufacturingShare,
.serviceShare,
.innovationShare,
.aboutUs {
  position: fixed;
  display: none;
  left: 0 !important;
  top: 80px !important;
  width: 100vw;
  // height: 246px;
  margin-top: 0;
  padding: 0;
  background-color: #fff;

  .sub-purchase-content {
    display: flex;
    flex-wrap: wrap;
    // margin-left: 28.96vw;
    // justify-content: center;
    padding: 50px;

    .content_item {
      width: 15%;
      margin-left: 5%;
      cursor: pointer;

      .title {
        font-weight: 700;
        font-size: 20px;
        position: relative;
        transition: all 0.2s ease-in;
      }

      .titleLine {
        height: 2px;
        width: 100%;
        background-color: #999;
        margin: 20px 0;
      }

      .desc {
        font-size: 14px;
        color: #999;
      }
    }

    .content_item:hover .title {
      color: #21c9b8;
      // transition: all 0.6s ease-in-out;
    }

    .content_item:hover .titleLine {
      // background-color: #21c9b8;
    }

    .content_item:nth-child(5n + 1) {
      margin-left: 0;
    }

    .content_item:nth-child(n + 6) {
      margin-top: 50px;
    }

    // .sub-purchase-left {
    //   margin: 4px 80px 0 0;
    //   width: 400px;
    //   font-family: PingFangSC-Regular, PingFang SC;
    //   .sub-left-img {
    //     width: 64px;
    //     height: 64px;
    //     margin: 0 auto;
    //     img {
    //       width: 100%;
    //       height: 100%;
    //     }
    //   }
    //   .sub-left-title {
    //     font-weight: 500;
    //     color: #333;
    //     line-height: 26px;
    //     padding: 18px 0 8px 0;
    //     text-align: center;
    //   }
    //   .sub-left-info {
    //     color: #666;
    //     line-height: 26px;
    //     white-space: normal;
    //   }
    // }
    // .sub-purchase-right {
    //   display: flex;
    //   .sub-right-each {
    //     .sub-right-item {
    //       cursor: pointer;
    //       font-family: PingFangSC-Regular, PingFang SC;
    //       .sub-right-title {
    //         display: flex;
    //         align-items: center;
    //         font-size: 16px;
    //         font-weight: 500;
    //         color: #333;
    //         line-height: 16px;
    //       }
    //       .sub-right-arrow {
    //         display: none;
    //       }
    //       .sub-right-info {
    //         color: #999999b3;
    //         line-height: 14px;
    //         padding-top: 12px;
    //       }
    //       &:hover {
    //         .sub-right-title {
    //           color: #21c9b8;
    //         }
    //         .sub-right-arrow {
    //           display: block;
    //         }
    //       }
    //       & + .sub-right-item {
    //         padding-top: 32px;
    //       }
    //     }
    //     & + .sub-right-each {
    //       margin-left: 80px;
    //     }
    //   }
    // }
  }

  &:hover {
    display: block;
  }
}

.show-content {
  .sub-purchase-popper {
    display: none;
  }
}

.el-menu--horizontal>.el-submenu {
  &:hover {
    .sub-purchase-title {
      color: #21c9b8 !important;
    }
  }
}
</style>

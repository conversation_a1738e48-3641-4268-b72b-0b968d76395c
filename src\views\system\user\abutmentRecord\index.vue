<!--
 * @Author: zhc
 * @Date: 2023-02-03 11:06:49
 * @LastEditTime: 2023-02-20 10:41:34
 * @Description: 
 * @LastEditors: zhc
-->
<template>
  <div class="app-container">
    <div class="abutmrnt-record-page">
      <el-row :gutter="20">
        <el-col :span="2.5" :xs="24">
          <user-menu />
        </el-col>
        <el-col :span="20" :xs="24">
          <div>
            <el-tabs
              class="abutmrnt-record-tab"
              v-model="activeName"
              @tab-click="handleClick"
            >
              <el-tab-pane label="我提交的申请" name="first">
                <el-form
                  :model="queryParams"
                  ref="queryForm"
                  size="small"
                  :inline="true"
                >
                  <el-form-item>
                    <el-select
                      v-model="queryParams.queryType"
                      placeholder="查询方式"
                      clearable
                      style="width: 140px"
                      @change="getAbutmentList"
                    >
                      <el-option
                        v-for="dict in timeDic"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item>
                    <el-select
                      v-model="queryParams.status"
                      placeholder="状态"
                      clearable
                      style="width: 140px"
                      @change="getAbutmentList"
                    >
                      <el-option
                        v-for="dict in statusDic"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item>
                    <el-button size="mini" @click="resetQuery">重置</el-button>
                  </el-form-item>
                </el-form>
                <div class="abutmrnt-message">
                  <div
                    class="none-class"
                    v-if="!records || records.length == 0"
                  >
                    <el-image
                      style="width: 160px; height: 160px"
                      :src="require('@/assets/user/none.png')"
                      :fit="fit"
                    ></el-image>
                    <div class="text">暂无数据</div>
                  </div>
                  <div
                    class="abutmrnt-message-item"
                    v-for="item in records"
                    v-else
                    :key="item.id"
                  >
                    <div class="item-content">
                      <div class="left">
                        <div class="title">
                          {{ item.resourceTitle }}
                        </div>
                        <div class="company-name">
                          {{ item.resourceCompanyName }}
                        </div>
                        <div class="tag">{{ item.resourceTypeName }}</div>
                      </div>
                      <div class="right">
                        <div
                          :class="['status-tag', getStatusClass(item.status)]"
                        >
                          {{ item.statusName }}
                        </div>
                        <div class="date">{{ item.createTimeStr }}</div>
                      </div>
                    </div>
                  </div>
                </div>
                <el-pagination
                  v-show="total > 0"
                  background
                  layout="prev, pager, next"
                  :page-size="4"
                  :current-page.sync="queryParams.pageNum"
                  @current-change="submitPageChange"
                  :total="total"
                >
                </el-pagination
              ></el-tab-pane>
              <el-tab-pane label="我收到的申请" name="second">
                <el-form
                  :model="queryParams"
                  ref="queryForm"
                  size="small"
                  :inline="true"
                >
                  <el-form-item>
                    <el-select
                      v-model="queryParams.queryType"
                      placeholder="查询方式"
                      clearable
                      style="width: 140px"
                      @change="getAbutmentList"
                    >
                      <el-option
                        v-for="dict in timeDic"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item>
                    <el-select
                      v-model="queryParams.status"
                      placeholder="状态"
                      clearable
                      style="width: 140px"
                      @change="getAbutmentList"
                    >
                      <el-option
                        v-for="dict in statusDic"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item>
                    <el-button size="mini" @click="resetQuery">重置</el-button>
                  </el-form-item>
                </el-form>
                <div class="abutmrnt-message">
                  <div
                    class="none-class"
                    v-if="!records || records.length == 0"
                  >
                    <el-image
                      style="width: 160px; height: 160px"
                      :src="require('@/assets/user/none.png')"
                      :fit="fit"
                    ></el-image>
                    <div class="text">暂无数据</div>
                  </div>
                  <div
                    class="abutmrnt-message-item"
                    v-for="item in records"
                    :key="item.id"
                  >
                    <div class="item-content">
                      <div class="left">
                        <div class="title">
                          {{ item.resourceTitle }}
                        </div>
                        <div class="company-name">
                          {{ item.resourceCompanyName }}
                        </div>
                        <div class="tag">{{ item.resourceTypeName }}</div>
                      </div>

                      <div class="right right_200" v-if="item.showOperate == 1">
                        <div class="tags">
                          <a
                            class="status-tag blue_white"
                            @click="operateAbutment(item.id, 1)"
                            >接受</a
                          >
                          <a
                            class="status-tag red ml_20"
                            @click="operateAbutment(item.id, 4)"
                            >忽略</a
                          >
                        </div>
                      </div>
                      <div class="right">
                        <div
                          :class="['status-tag', getStatusClass(item.status)]"
                          v-if="item.showOperate != 1"
                        >
                          {{ item.statusName }}
                        </div>
                        <div class="date">{{ item.createTimeStr }}</div>
                      </div>
                    </div>
                    <div
                      class="unread-tag"
                      v-if="item.receiveReadStatus == 0"
                    ></div>
                    <div class="unread-text" v-if="item.receiveReadStatus == 0">
                      未读
                    </div>
                  </div>
                </div>
                <el-pagination
                  v-show="total > 0"
                  background
                  layout="prev, pager, next"
                  :page-size="4"
                  :current-page.sync="queryParams.pageNum"
                  @current-change="submitPageChange"
                  :total="total"
                >
                </el-pagination
              ></el-tab-pane>
            </el-tabs>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import UserMenu from "../components/userMenu.vue";
import { getAbutmentList, operateAbutment } from "@/api/system/abutment";

export default {
  name: "AbutmentRecord",
  components: { UserMenu },
  data() {
    return {
      activeName: "first",
      records: [],
      fit: "cover",
      timeDic: [
        {
          label: "按时间查询",
          value: 1,
        },
        {
          label: "按名称查询",
          value: 2,
        },
      ],
      statusDic: [
        {
          label: "已申请",
          value: 1,
        },
        {
          label: "进行中",
          value: 2,
        },
        {
          label: "对接完成",
          value: 3,
        },
        {
          label: "已拒绝",
          value: 4,
        },
        {
          label: "全部",
          value: 5,
        },
      ],
      queryParams: {
        pageNum: 1,
        pageSize: 4,
        listType: "1",
      },

      total: 0,
    };
  },
  created() {
    this.getAbutmentList();
  },
  methods: {
    handleClick(tab, event) {
      if (this.activeName === "first") {
        this.queryParams.listType = "1";
      } else {
        this.queryParams.listType = "2";
      }
      this.queryParams.pageNum = 1;
      this.queryParams.pageSize = 4;

      this.getAbutmentList();
    },

    submitPageChange(res) {
      this.queryParams.pageNum = res;
      this.getAbutmentList();
    },
    getAbutmentList() {
      getAbutmentList({ ...this.queryParams }).then((response) => {
        this.records = response.rows;
        this.total = response.total;
      });
    },
    operateAbutment(id, type) {
      operateAbutment({ id: id, operateStatus: type }).then((response) => {
        if (response.code == 200) {
          this.$modal.msgSuccess("操作成功");
          this.getAbutmentList();
        }
      });
    },
    systemPageChange(res) {
      this.abutmrntParams.pageNum = res;
      this.getSystemList();
    },
    resetQuery() {
      this.queryParams.pageNum = 1;
      this.queryParams.status = undefined;
      this.queryParams.queryType = undefined;
      this.queryParams.pageSize = 4;
      this.getAbutmentList();
    },

    getStatusClass(status) {
      switch (status) {
        case 1:
          return "blue";
        case 2:
          return " green";
        case 3:
          return "grey";
        case 4:
          return "red";
      }
    },
  },
};
</script>

<style lang="scss">
.app-container {
  background: #f4f5f9;
  .abutmrnt-record-page {
    .none-class {
      text-align: center;
      padding: 10% 0;
      .text {
        font-size: 14px;
        font-weight: 400;
        color: #999999;
        line-height: 14px;
      }
    }
    .abutmrnt-record-tab {
      .el-tabs__nav {
        width: 100%;
        height: 60px;
        padding: 0 43%;
        display: flex;
        // justify-content: space-between;
      }
      .el-tabs__nav-wrap::after {
        background-color: transparent;
      }
      .el-tabs__active-bar {
        background-color: transparent;
      }
      .el-tabs__item {
        padding: 0 20px !important;
        background-color: #fff;
        border-radius: 20px;
        box-shadow: 0px 4px 16px 0px rgba(38, 74, 116, 0.1);
      }
      .el-tabs__item.is-active {
        background-color: #21c9b8 !important;
        color: #fff;
      }

      .el-tabs__item#tab-first {
        padding-right: 40px !important;
      }
      .el-tabs__item#tab-second {
        padding-left: 40px !important;
        margin-left: -30px;
        z-index: -1;
      }
      .el-tabs__item.is-active#tab-first {
        padding-right: 15px !important;
      }
      .el-tabs__item.is-active#tab-second {
        padding-left: 20px !important;
        margin-left: -30px;
        z-index: 999;
      }
    }
    .el-button {
      background: #21c9b8;
      color: #fff;
      border-color: transparent;
    }
    .abutmrnt-message {
      width: 100%;
      height: 600px;
      .abutmrnt-message-item {
        width: 100%;
        vertical-align: middle;
        padding: 22px 22px;
        margin-bottom: 20px;
        background-color: #fff;

        display: flex;
        border-bottom: 1px solid #e8e8e8;
        position: relative;

        .iamge {
          margin: auto 0;
        }
        .item-content {
          margin-left: 10px;
          display: flex;
          width: 100%;
          justify-content: space-between;
          .left {
            width: 900px;
            .title {
              font-size: 16px;
              font-weight: 500;
              width: 900px;
              color: #333333;
              line-height: 20px;
              overflow: hidden;
              -webkit-line-clamp: 1;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-box-orient: vertical;
            }
            .company-name {
              font-size: 14px;
              font-weight: 400;
              color: #666666;
              line-height: 50px;
              height: 50px;
            }
            .tag {
              width: 72px;
              height: 24px;
              border-radius: 4px;
              border: 1px solid #214dc5;
              text-align: center;
              font-size: 12px;
              font-weight: 400;
              color: #214dc5;
              line-height: 24px;
            }
          }
          .right {
            width: 100px;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            .status-tag {
              width: 74px;
              height: 30px;
              border-radius: 4px;
              text-align: center;
              font-weight: 500;
              font-size: 14px;
              line-height: 30px;
            }
            .blue {
              background: rgba(33, 77, 197, 0.15);
              color: #0044ff;
              font-size: 14px;
              font-weight: 500;
              color: #0044ff;
            }
            .blue_white {
              background: #305ae8;
              color: #fff;
            }
            .green {
              background: rgba(21, 188, 132, 0.15);
              font-size: 14px;
              font-weight: 500;
              color: #15bc84;
            }
            .red {
              background: rgba(255, 77, 77, 0.15);
              color: #fff;
              font-size: 14px;
              font-weight: 500;
              color: #ff4d4d;
            }
            .grey {
              background: #d2d2d2;
              font-size: 14px;
              font-weight: 500;
              color: #b7b7b7;
            }
            .date {
              font-size: 14px;
              font-weight: 400;
              color: #666666;
              line-height: 30px;
            }
          }
          .tags {
            display: flex;
            justify-content: flex-end;
          }
          .ml_20 {
            margin-left: 20px;
          }
          .right_200 {
            width: 200px;
            text-align: right;
          }
        }
        .unread-tag {
          position: absolute;
          top: -40px;
          left: -50px;
          width: 0;
          height: 0;
          border: 40px solid #ff5151;
          border-bottom-color: transparent;
          border-top-color: transparent;
          border-left-color: transparent;
          transform: rotateZ(45deg);
        }
        .unread-text {
          position: absolute;
          top: 10px;
          left: 2px;
          transform: rotateZ(-45deg);
          font-size: 12px;
          font-weight: 500;
          color: #ffffff;
          line-height: 12px;
        }
        .delete-icon {
          right: 30px;
          top: 40px;
          margin: 0 auto;
          position: absolute;
        }
        .re-icon {
          right: 80px;
          top: 40px;
          margin: 0 auto;
          position: absolute;
        }
      }
    }
    .el-pagination {
      width: 100%;
      margin-top: 20px;
      text-align: center;
    }
    .el-pagination.is-background .el-pager li {
      background-color: #fff;
    }
    .el-pagination.is-background .el-pager li:not(.disabled).active {
      background-color: #21c9b8;
      color: #ffffff;
    }
    .el-pagination.is-background .el-pager li:not(.disabled):hover {
      color: #21c9b8;
    }
  }
}
</style>

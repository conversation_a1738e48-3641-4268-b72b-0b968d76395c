<template>
  <div
    class="card-container wow animate__animated animate__fadeInUp"
    data-wow-duration="1s"
  >
    <div class="enterpriseTitle">
      <div>新闻中心</div>
      <div class="allEnterprise" @click="goNewsCenter">查看全部>></div>
    </div>
    <div class="content" v-loading="loading">
      <div class="content_left">
        <div
          class="platDynamics"
          :class="flag === '1' ? 'platDyHover' : ''"
          @click="getFlag('1')"
        >
          <div class="platImg">
            <img
              v-show="flag !== '1'"
              src="../../../assets/images/home/<USER>"
              alt=""
            />
            <img
              v-show="flag === '1'"
              src="../../../assets/images/home/<USER>"
              alt=""
            />
          </div>
          <div class="platTitle">平台动态</div>
        </div>
        <div
          class="platDynamics"
          :class="flag === '2' ? 'platDyHover' : ''"
          @click="getFlag('2')"
        >
          <div class="platImg">
            <img
              v-show="flag !== '2'"
              src="../../../assets/images/home/<USER>"
              alt=""
            />
            <img
              v-show="flag === '2'"
              src="../../../assets/images/home/<USER>"
              alt=""
            />
          </div>
          <div class="platTitle">行业动态</div>
        </div>
        <div
          class="platDynamics"
          :class="flag === '3' ? 'platDyHover' : ''"
          @click="getFlag('3')"
        >
          <div class="platImg">
            <img
              v-show="flag !== '3'"
              src="../../../assets/images/home/<USER>"
              alt=""
            />
            <img
              v-show="flag === '3'"
              src="../../../assets/images/home/<USER>"
              alt=""
            />
          </div>
          <div class="platTitle">政策法规</div>
        </div>
      </div>
      <div class="content_middle">
        <div class="newsImg">
          <img src="../../../assets/images/home/<USER>" alt="" />
        </div>
        <div
          class="newsContent"
          v-if="newsFirstData.updateTime"
          @click="goDetail(newsFirstData.id)"
        >
          <div class="news_left">
            <div class="news_year">
              {{ newsFirstData.updateTime.slice(0, 7) }}
            </div>
            <div class="news_day">
              {{ newsFirstData.updateTime.slice(8, 10) }}
            </div>
          </div>
          <div class="news_right">
            <div class="title">{{ newsFirstData.title }}</div>
            <div class="desc">
              {{ newsFirstData.brief }}
            </div>
          </div>
        </div>
      </div>
      <div class="content_right">
        <div
          class="newsContent"
          v-for="item in newsRightList"
          :key="item.id"
          @click="goDetail(item.id)"
        >
          <div class="news_left">
            <div class="news_year">{{ item.updateTime.slice(0, 7) }}</div>
            <div class="news_day">{{ item.updateTime.slice(8, 10) }}</div>
          </div>
          <div class="news_right">
            <div class="title">{{ item.title }}</div>
            <div class="desc">
              {{ item.brief }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { newsList } from "@/api/newsCenter";

export default {
  data() {
    return {
      loading: false,
      data: [],
      pageNum: 1,
      pageSize: 4,
      total: 0,
      flag: "1",
      newsFirstData: {},
      newsRightList: [],
    };
  },
  created() {
    // this.initData();
  },
  methods: {
    initData() {
      this.loading = true;
      let params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        kind: "0",
        typeTop: this.flag,
      };
      newsList(params).then((res) => {
        if (res.code === 200) {
          this.loading = false;
          this.newsFirstData = res.rows[0];
          this.newsRightList = res.rows.slice(1, 4);
        }
      });
      // newsType().then((res) => {
      //   if (res.code === 200) {
      //     console.log(res, "-----------------");
      //   }
      // });
      // getDicts("case_industry").then((res) => {
      //   const { code, data = [] } = res;
      //   if (code === 200) {
      //     this.caseTypeList = data;
      //     this.getCaseList();
      //   }
      // });
    },
    // 跳转到详情页面
    goDetail(id) {
      this.$router.push({
        path: "/newsDetail",
        query: {
          id,
        },
      });
    },
    goNewsCenter() {
      let routeData = this.$router.resolve({
        path: "/newsCenter",
      });
      window.open(routeData.href, "_blank");
    },
    getFlag(value) {
      this.flag = value;
      this.initData();
    },
  },
};
</script>

<style lang="scss" scoped>
.enterpriseTitle {
  width: 100%;
  font-size: 36px;
  text-align: center;
  margin: 60px 0;
  position: relative;
  font-family: Source Han Sans CN;
  font-weight: 500;
  color: #000000;
  .allEnterprise {
    position: absolute;
    top: 8 px;
    right: 0;
    font-size: 16px;
    font-family: Source Han Sans CN;
    font-weight: 500;
    color: #21c9b8;
    line-height: 26px;
    cursor: pointer;
  }
}
.content {
  display: flex;
  width: 100%;
  height: 330px;
  margin-bottom: 86px;
  .content_left {
    width: 300px;
    height: 100%;
    .platDynamics {
      width: 100%;
      height: 96px;
      background-color: rgb(229, 247, 243);
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 21px;
      cursor: pointer;
      .platImg {
        width: 42px;
        height: 40px;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .platTitle {
        font-size: 20px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #21c9b8;
        margin-left: 20px;
      }
    }
    .platDynamics:nth-child(1) {
      margin-top: 0;
    }
    .platDyHover {
      background: #21c9b8;
      .platTitle {
        color: #ffffff;
      }
    }
  }
  .content_middle {
    width: 460px;
    height: 100%;
    margin: 0 20px 0 30px;
  }
  .content_right {
    width: 390px;
    height: 100%;
  }
  .newsImg {
    width: 100%;
    height: 220px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .newsContent {
    width: 100%;
    height: 110px;
    background: #f9f9f9;
    padding: 22px 26px;
    display: flex;
    align-items: center;
    cursor: pointer;
    .news_left {
      width: 100px;
      font-size: 16px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #666666;
      margin-right: 20px;
      text-align: center;
      .news_year {
        font-size: 16px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #666666;
      }
      .news_day {
        font-size: 30px;
        font-family: Source Han Sans CN;
        font-weight: 500;
        color: #222222;
        line-height: 26px;
        margin-top: 10px;
      }
    }
    .news_right {
      width: calc(100% - 100px);
      .title {
        font-size: 16px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #222222;
        overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
        text-overflow: ellipsis;
        word-wrap: break-word;
      }
      .desc {
        margin-top: 13px;
        font-size: 14px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #666666;
        overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        text-overflow: ellipsis;
        word-wrap: break-word;
      }
    }
  }
  .newsContent:hover {
    background: rgb(235, 252, 240);
    .title {
      color: #21c9b8;
    }
  }
}
</style>

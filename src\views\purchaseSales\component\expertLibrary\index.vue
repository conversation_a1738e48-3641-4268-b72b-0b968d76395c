<!--
 * @Author: jhy
 * @Date: 2023-02-03 15:14:30
 * @LastEditors: JHY
 * @LastEditTime: 2023-12-11 14:39:06
-->
<template>
  <div class="expert-library-container">
    <!-- banner图 -->
    <div class="expert-library-banner">
      <img
        src="../../../../assets/expertLibrary/expertLibraryBanner.png"
        alt=""
      />
    </div>
    <div v-loading="loading">
      <div class="expert-library-title-content">
        <div class="expert-library-title-box">
          <div class="expert-library-divider"></div>
          <div class="expert-library-title">专家智库</div>
          <div class="expert-library-divider"></div>
        </div>
        <div class="expert-library-search-box">
          <el-form ref="form" class="expert-library-search-form" :model="form">
            <el-form-item>
              <el-input
                v-model="form.keywords"
                placeholder="请输入搜索内容"
                class="expert-library-search-input"
                :maxlength="255"
              >
                <el-button
                  slot="append"
                  class="expert-library-search-btn"
                  @click="onSearch"
                  >搜索</el-button
                >
              </el-input>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div class="expert-library-card">
        <div class="expert-library-content">
          <div class="expert-library-search-type-box">
            <el-form ref="formInfo" :model="formInfo">
              <div class="expert-library-search-line">
                <el-form-item
                  label="技术类别"
                  class="expert-library-search-line-item"
                >
                  <el-radio-group
                    v-model="formInfo.techniqueTypeName"
                    class="expert-library-search-radio"
                    @input="changeRadio"
                  >
                    <el-radio-button label="">全部</el-radio-button>
                    <el-radio-button
                      v-for="(item, index) in techniqueTypeList"
                      :key="index"
                      :label="item.dictLabel"
                      >{{ item.dictLabel }}</el-radio-button
                    >
                  </el-radio-group>
                </el-form-item>
              </div>
            </el-form>
          </div>
          <div class="expert-library-list">
            <div
              v-for="(item, index) in data"
              :key="index"
              class="list-item-content"
              @click="goExpertLibrary(item.id)"
            >
              <div class="list-item-box">
                <div class="item-headline">
                  <div class="item-title">
                    {{ item.expertName }}
                  </div>
                </div>
                <div class="expert-library-label">
                  <div
                    v-for="(val, index1) in item.techniqueTypeName"
                    :key="index1"
                    class="library-label-item"
                  >
                    <span v-if="index1 < 2" class="expert-library-type">{{
                      `#${val}`
                    }}</span>
                    <span v-else>…</span>
                  </div>
                </div>
                <div class="expert-library-box">
                  {{ item.synopsis }}
                </div>
              </div>
              <div class="list-item-img">
                <img v-if="item.headPortrait" :src="item.headPortrait" alt="" />
                <img
                  v-else
                  src="../../../../assets/expertLibrary/defaultImg.png"
                  alt=""
                />
              </div>
            </div>
          </div>
          <div class="expert-library-page-end">
            <el-button class="expert-library-page-btn" @click="goHome"
              >首页</el-button
            >
            <el-pagination
              v-if="data && data.length > 0"
              background
              layout="prev, pager, next"
              class="expert-library-pagination"
              :page-size="pageSize"
              :current-page="pageNum"
              :total="total"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            >
            </el-pagination>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getExpertList } from "@/api/purchaseSales";
import { getDicts } from "@/api/system/dict/data";
import CryptoJS from "crypto-js";
let secretKey = "9zVn0%bqmUYSGw2n";

export default {
  data() {
    return {
      loading: false,
      form: {
        keywords: "", //搜索内容
      },
      formInfo: {
        techniqueTypeName: "", //技术类别
      },
      techniqueTypeList: [], //技术类别列表
      data: [],
      pageNum: 1,
      pageSize: 10,
      total: 0,
    };
  },
  created() {
    this.getDictsList("technique_type", "techniqueTypeList");
    this.search();
  },
  methods: {
    search() {
      this.loading = true;
      getExpertList({
        ...this.form,
        ...this.formInfo,
        pageNum: this.pageNum,
        // pageSize: this.pageSize,
      })
        .then((res) => {
          let key = CryptoJS.enc.Utf8.parse(secretKey);
          let decrypt = CryptoJS.AES.decrypt(res, key, {
            mode: CryptoJS.mode.ECB,
            padding: CryptoJS.pad.Pkcs7,
          });
          res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));

          this.loading = false;
          let { rows, total } = res || [];
          this.data = rows;
          this.data.forEach((item) => {
            item.techniqueTypeName = item.techniqueTypeName
              ? item.techniqueTypeName.split(",")
              : [];
          });
          this.total = total;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    // 字典
    getDictsList(code, propertyName) {
      getDicts(code).then((res) => {
        this[propertyName] = res.data || [];
      });
    },
    changeRadio() {
      console.log(this.techniqueTypeList, "哈哈哈哈");
      this.onSearch();
    },
    onSearch() {
      this.pageNum = 1;
      this.search();
    },
    handleSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.onSearch();
    },
    handleCurrentChange(pageNum) {
      this.pageNum = pageNum;
      this.search();
    },
    // 跳转到专家详情页面
    goExpertLibrary(id) {
      let routeData = this.$router.resolve({
        path: "/expertDetail",
        query: { id },
      });
      window.open(routeData.href, "_blank");
    },
    // 跳转到首页
    goHome() {
      this.$router.push({ path: "/index" });
    },
  },
};
</script>

<style lang="scss" scoped>
.expert-library-container {
  width: 100%;
  .expert-library-banner {
    width: 100%;
    height: 50vh;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .expert-library-title-content {
    width: 100%;
    padding-bottom: 18px;
    .expert-library-title-box {
      width: 336px;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 60px 0 40px;
      .expert-library-title {
        font-size: 40px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #333;
        line-height: 40px;
        padding: 0 40px;
      }
      .expert-library-divider {
        width: 48px;
        height: 4px;
        background: #21c9b8;
      }
    }
    .expert-library-search-box {
      .expert-library-search-form {
        text-align: center;
        .expert-library-search-input {
          width: 792px;
          height: 54px;
          .expert-library-search-btn {
            width: 100px;
          }
        }
      }
    }
  }
  .expert-library-card {
    background: #f4f5f9;
    padding-top: 40px;
    .expert-library-content {
      width: 1200px;
      margin: 0 auto;
      .expert-library-search-type-box {
        background: #fff;
        .expert-library-search-line {
          padding: 14px 24px 4px;
          .expert-library-search-line-item {
            margin-bottom: 0;
            .expert-library-search-radio {
              width: 1050px;
              margin-top: 11px;
            }
          }
          & + .expert-library-search-line {
            border-top: 1px solid #f5f5f5;
          }
        }
        .expert-library-search-more-line {
          display: flex;
          justify-content: space-between;
          padding: 14px 24px 4px;
          border-top: 1px solid #f5f5f5;
          border-bottom: 1px solid #f5f5f5;
          .expert-library-search-more-line-item {
            flex: 1;
            margin-bottom: 0;
            display: flex;
            &.advanced {
              overflow: hidden;
              height: 45px;
            }
            .more-radio {
              margin-top: 11px;
              flex: 1;
            }
          }
          .expert-library-search-more-line-btn {
            display: inline-block;
            width: 64px;
            height: 24px;
            background: #fff;
            border-radius: 2px;
            border: 1px solid #d9d9d9;
            font-size: 12px;
            font-family: PingFangSC-Regular, PingFang SC;
            color: #333;
            display: flex;
            align-items: center;
            padding: 0 16px;
            margin-top: 5px;
            &:hover {
              border: 1px solid #21c9b8;
              color: #21c9b8;
            }
          }
        }
      }
      .expert-library-list {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        width: 100%;
        .list-item-content {
          display: flex;
          justify-content: space-between;
          width: 578px;
          background: #fff;
          margin-top: 36px;
          padding: 28px 32px;
          min-height: 240px;
          .list-item-box {
            flex: 1;
            .item-headline {
              display: flex;
              justify-content: space-between;
              align-items: center;
              .item-title {
                width: 280px;
                font-size: 32px;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 500;
                color: #333;
                line-height: 32px;
                text-overflow: ellipsis;
                white-space: nowrap;
                overflow: hidden;
                word-wrap: break-word;
              }
            }
            .expert-library-label {
              display: flex;
              flex-wrap: wrap;
              margin: 0 0 16px;
              .library-label-item {
                max-width: 350px;
                padding: 6px 12px;
                background: #f4f5f9;
                border-radius: 4px;
                font-size: 12px;
                font-family: PingFangSC-Regular, PingFang SC;
                color: #666;
                line-height: 12px;
                margin: 24px 16px 0 0;
                .expert-library-type {
                  word-wrap: break-word;
                }
              }
            }
            .expert-library-box {
              width: 370px;
              font-size: 16px;
              font-family: PingFangSC-Regular, PingFang SC;
              color: #666;
              line-height: 32px;
              overflow: hidden;
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 2;
              text-overflow: ellipsis;
              word-wrap: break-word;
            }
          }
          .list-item-img {
            width: 120px;
            height: 168px;
            margin-left: 24px;
            img {
              width: 100%;
              height: 100%;
            }
          }
          &:hover {
            cursor: pointer;
          }
        }
      }
      .expert-library-page-end {
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 0 auto;
        padding: 24px 0 60px;
        .expert-library-page-btn {
          width: 82px;
          height: 32px;
          background: #fff;
          border-radius: 4px;
          border: 1px solid #d9d9d9;
          font-family: PingFangSC-Regular, PingFang SC;
          color: #333;
          line-height: 10px;
        }
      }
    }
  }
}
</style>

<style lang="scss">
.expert-library-container {
  .expert-library-search-input {
    .el-input__inner {
      height: 54px;
      background: #fff;
      border-radius: 27px 0 0 27px;
      border: 1px solid #d9d9d9;
      font-size: 16px;
      font-family: PingFangSC-Regular, PingFang SC;
      line-height: 24px;
      padding-left: 30px;
    }
    .el-input-group__append {
      border-radius: 0px 100px 100px 0px;
      background: #21c9b8;
      font-size: 16px;
      font-family: PingFangSC-Regular, PingFang SC;
      color: #fff;
      line-height: 24px;
    }
  }
  .el-form-item__label {
    width: 88px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #999;
    padding-right: 32px;
    text-align: left;
  }
  .el-radio-button {
    padding-bottom: 20px;
    .el-radio-button__inner {
      border: none;
      padding: 0 32px 0 0;
      background: none;
      &:hover {
        color: #21c9b8;
      }
    }
    &.is-active {
      .el-radio-button__inner {
        color: #21c9b8;
        background: none;
      }
    }
    .el-radio-button__orig-radio:checked {
      & + .el-radio-button__inner {
        box-shadow: unset;
      }
    }
  }
  .expert-library-page-end {
    .expert-library-pagination {
      .btn-prev,
      .btn-next,
      .btn-quickprev {
        width: 32px;
        height: 32px;
        background: #fff;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        color: #333;
      }
      &.is-background {
        .el-pager {
          .number {
            width: 32px;
            height: 32px;
            border: 1px solid #d9d9d9;
            background: #fff;
            border-radius: 4px;
            line-height: 32px;
            &.active {
              background: #21c9b8;
              border: 1px solid #21c9b8;
            }
          }
        }
      }
    }
  }
}
</style>

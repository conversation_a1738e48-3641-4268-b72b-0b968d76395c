<template>
    <div class="main">
        <div class="card-container content">
            <div class="content_title">
                <div class="icon"></div>
                <div class="title">证书查询</div>
            </div>
            <div class="tab">
                <div class="tab-total">查询出<span class="tab-num">{{ total }}条</span>结果</div>
                <div class="tab-content">
                    <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
                        <el-tab-pane v-for="item in list" :label="item.certificateName" :key="item.id">
                            <div class="tab-item" v-if="item.idCard">
                                <div class="tab-left">
                                    <div class="content_title">
                                        <div class="icon"></div>
                                        <div class="title">基础信息</div>
                                    </div>
                                    <table class="table">
                                        <tr class="table-tr">
                                            <td class="table-td table-title">姓名</td>
                                            <td class="table-td table-content">{{ item.name }}</td>
                                        </tr>
                                        <tr class="table-tr">
                                            <td class="table-td table-title">身份证号</td>
                                            <td class="table-td table-content">{{ item.idCard }}</td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="tab-right">
                                    <div class="content_title">
                                        <div class="icon"></div>
                                        <div class="title">证书信息</div>
                                    </div>
                                    <table class="table">
                                        <tr class="table-tr">
                                            <td class="table-td table-title">职业名称</td>
                                            <td class="table-td table-content">{{ item.positionName }}</td>
                                            <td class="table-td table-title">职业技能证书</td>
                                            <td class="table-td table-content">{{ item.certificateName }}</td>
                                        </tr>
                                        <tr class="table-tr">
                                            <td class="table-td table-title">职业技能等级</td>
                                            <td class="table-td table-content">{{ item.positionLevel }}</td>
                                            <td class="table-td table-title">证书编号</td>
                                            <td class="table-td table-content">{{ item.certificateNo }}</td>
                                        </tr>
                                        <tr class="table-tr">
                                            <td class="table-td table-title">评价机构</td>
                                            <td class="table-td table-content">{{ item.company }}</td>
                                            <td class="table-td table-title">发证日期</td>
                                            <td class="table-td table-content">{{ item.issueDate }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                            <div class="tab-item" v-else>
                                <el-empty  description="暂无数据"></el-empty>
                            </div>
                        </el-tab-pane>
                    </el-tabs>
                </div>
            </div>
            <div class="tips">
                以上查询服务由恒润集团提供
            </div>
            <div class="footer-submit">
                <el-button style="width: 490px; height: 50px" type="primary" @click="cancelIndex">返回首页</el-button>
            </div>
        </div>
    </div>

</template>

<script>
import { getCertificateList } from '@/api/serviceSharing'
export default {
    data() {
        return {
            activeName: '0',
            total: 0,
            params: {
                name: '',
                idCard: '',
            },
            list: [
                {
                    id: '1',
                    certificateName: 'XXXX证书',
                },
            ]
        };
    },
    mounted() {
        if (this.$route.query.name) {
            this.params = this.$route.query
            this.getList()
        }
    },
    methods: {
        async getList() {
            let res = await getCertificateList(this.params)
            if (res.code == 200) {
                this.list = { ...res.rows }
                this.total = res.total
            }
        },
        handleClick(tab, event) {
            console.log(tab, event);
            console.log(this.activeName)
        },
        cancelIndex() {
            this.$router.push('/index')
        }
    }
}
</script>

<style lang="scss" scoped>
.main {
    width: 100%;
    height: 100vh;
    background-color: #F2F2F2;
    padding: 20px;
    box-sizing: border-box;
}

.content {
    background-color: #fff;
    margin-top: 40px;
    padding: 40px;
    box-sizing: border-box;
    width: 100%;

    .content_title {
        display: flex;
        align-items: center;
        margin-bottom: 20px;

        .icon {
            width: 4px;
            height: 20px;
            background: #21c9b8;
        }

        .title {
            font-family: Source Han Sans CN;
            font-weight: 400;
            font-size: 18px;
            color: #030a1a;
            margin-left: 10px;
        }
    }

    .tab {
        .tab-total {
            font-family: Source Han Sans CN;
            font-weight: 400;
            font-size: 16px;
            color: #666666;
            margin-bottom: 20px;

            .tab-num {
                color: #21c9b8;
                padding: 0 10px;
            }
        }

        .tab-item {
            display: flex;
            justify-content: center;
            align-items: center;

            .tab-left {
                width: 31.66%;
                margin-right: 5%;

                .table-title {
                    width: 40%;
                }

                .table-content {
                    width: 60%;
                }
            }

            .tab-right {
                width: 63.33%;

                .table-title {
                    width: 20%;
                }

                .table-content {
                    width: 30%;
                }
            }

            .table {
                width: 100%;
                border: 1px solid #E6E6E6;
                border-collapse: collapse;
                text-align: center;

                .table-tr {
                    height: 40px;
                    border-bottom: 1px solid #E6E6E6;

                    .table-td {
                        border-right: 1px solid #E6E6E6;
                        padding: 0 10px;
                        box-sizing: border-box;
                    }
                }

                .table-title {
                    background: #F2F2F2;
                    font-family: Source Han Sans CN;
                    font-weight: 400;
                    font-size: 16px;
                    color: #333333;
                }

                .table-content {
                    font-family: Source Han Sans CN;
                    font-weight: 400;
                    font-size: 14px;
                    color: #666666;
                }
            }
        }
    }

    .tips {
        font-weight: 400;
        font-size: 16px;
        color: #030A1A;
        line-height: 70px;
        text-align: center;
        margin-top: 20px;
    }

    .footer-submit {
        margin-top: 20px;
        width: 100%;
        display: flex;
        justify-content: center;
    }
}
</style>
<template>
  <div class="intention-page">
    <div class="intention-page-header">
      <div class="banner">
        <img
          src="https://xp-tech.oss-cn-beijing.aliyuncs.com/cykc/20230210/1676034162343360.webp"
          alt="我有意向"
        />
      </div>
    </div>
    <div class="intention-page-title">我有意向</div>
    <div v-loading="loading" class="card-container intention-form">
      <div class="form-content">
        <el-form ref="form" :model="form" label-width="80px">
          <el-form-item label="意向类型">
            {{ formatTitle(form.resourceType) }}
          </el-form-item>
          <el-form-item label="意向名称">
            {{form.resourceTitle || "--"}}
          </el-form-item>
          <el-form-item label="意向描述">
            <el-input
              type="textarea"
              v-model="form.resourceDescribe"
              maxlength="500"
              rows="6"
              show-word-limit
              placeholder="请输入需求描述"
            ></el-input>
          </el-form-item>
          <el-form-item label="联系人">
            <el-input disabled v-model="form.contactPerson" placeholder="请输入联系人"></el-input>
          </el-form-item>
          <el-form-item label="联系方式">
            <el-input disabled v-model="form.contactPhone"></el-input>
          </el-form-item>
          <el-form-item class="footer-submit">
            <el-button type="primary" @click="onSubmit">提交</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>/**
 * businessId 业务ID
 * resourceType类型是下面:
 * 专家智库  resource_expet
 * 成果云服  resource_supply
 * 商机需求  resource_demand
 * resourceTitle: 资源名称
 *
 * **/
import {interactRecordAdd} from "@/api/zhm";

const TYPES = {
  "resource_expet": "专家智库",
  "resource_supply": "成果云服",
  "resource_demand": "商机需求",
};

export default {
  name: "addIntention",
  data() {
    const { user } = this.$store.state;
    return {
      loading: false,
      form: {
        businessId: undefined,
        resourceType: undefined,
        resourceTitle: undefined,
        resourceDescribe: undefined,
        contactPerson: user.name,
        contactPhone: user.tel,
      }
    }
  },
  created() {
    const { id, type, title } = this.$route.query;
    if (id) {
      this.form.businessId = id;
      this.form.resourceType = type;
      this.form.resourceTitle = title;
    }
  },
  methods: {
    formatTitle(key) {
      return TYPES[key] || '--';
    },
    onSubmit() {
      this.loading = true;
      interactRecordAdd(this.form).then((res) => {
        const { code, msg } = res;
        if (code === 200) {
          this.$message.success("提交成功");
          // TODO: 跳转到提交记录
          // this.$router.push({
          //   path: "/addIntention",
          //   params: {
          //     id: '对应当前资源的id',
          //     type: '专家智库: resource_expet 成果云服  resource_supply 商机需求  resource_demand',
          //     title: '资源名称'
          //   },
          // });
        } else {
          this.$message.error(msg || "提交失败");
        }
      }).finally(() => this.loading = false)
      console.log('submit!', this.form);
    },
  }
}
</script>

<style lang="scss" scoped>
@import "~@/assets/styles/mixin.scss";
.intention-page {
  background-color: #F4F5F9;
  padding-bottom: 80px;
  &-header {
    background-color: #FFFFFF;
    .banner {
      width: 100%;
      height: 540px;
      background-color: #f5f5f5;
      img {
        width: 100%;
        height: 540px;
        object-fit: fill;
      }
    }
    .body {
      padding: 60px 0;
    }
  }
  &-title {
    font-size: 40px;
    font-weight: 500;
    color: #333333;
    line-height: 40px;
    text-align: center;
    padding: 60px 0;
  }
  .intention-form {
    @include flexCenter;
    height: 664px;
    background-color: #FFFFFF;
    margin-bottom: 80px;
    .form-content {
      width: 750px;
      .footer-submit {
        text-align: center;
        margin-top: 40px;
        .el-button {
          width: 400px;
        }
      }
    }
  }
}
</style>

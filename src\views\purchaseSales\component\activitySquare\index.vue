<!--
 * @Author: jhy
 * @Date: 2023-01-30 11:29:06
 * @LastEditors: JHY
 * @LastEditTime: 2023-02-13 11:28:34
-->
<template>
  <div class="activity-container">
    <div class="activity-banner">
      <img
        src="../../../../assets/activitySquare/activitySquareBanner.png"
        alt=""
      />
    </div>
    <div v-loading="loading">
      <div class="activity-title-content">
        <div class="activity-title-box">
          <div class="activity-divider"></div>
          <div class="activity-title">链活动</div>
          <div class="activity-divider"></div>
        </div>
        <div class="activity-search-box">
          <el-form ref="form" class="activity-search-form" :model="form">
            <el-form-item>
              <el-input
                v-model="form.keywords"
                placeholder="请输入搜索内容"
                class="activity-search-input"
              >
                <el-button
                  slot="append"
                  class="activity-search-btn"
                  @click="onSearch"
                  >搜索</el-button
                >
              </el-input>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div class="activity-info-content">
        <div class="activity-search-type-box">
          <el-form ref="formInfo" :model="formInfo">
            <div class="activity-search-line">
              <el-form-item label="活动类型" class="activity-search-line-item">
                <el-radio-group
                  v-model="formInfo.activityType"
                  class="activity-search-radio"
                  @input="changeRadio"
                >
                  <el-radio-button label="">全部</el-radio-button>
                  <el-radio-button
                    v-for="(item, index) in activityTypeList"
                    :key="index"
                    :label="item.dictValue"
                    >{{ item.dictLabel }}</el-radio-button
                  >
                </el-radio-group>
              </el-form-item>
            </div>
          </el-form>
        </div>
        <div class="content">
          <div
            class="contentItem"
            v-for="item in data"
            :key="item.id"
            @click="goActivityDetail(item.id)"
          >
            <div>
              <img :src="item.activityPicture" alt="" />
            </div>
            <div class="contentTitle">{{ item.activityName }}</div>
          </div>
        </div>
        <!-- <div
          v-for="(item, index) in data"
          :key="index"
          class="activity-list-item"
          @click="goActivityDetail(item.id)"
        >
          <div class="list-item-content">
            <div class="list-item-img">
              <img
                v-if="item.activityPicture"
                alt=""
                :src="item.activityPicture"
              />
            </div>
            <div class="list-item-info">
              <div class="list-item-title">
                {{ item.activityName }}
              </div>
              <div class="list-item-text">
                {{ item.activityOverview }}
              </div>
              <div class="list-item-time">{{ item.createTimeStr }}</div>
            </div>
          </div>
        </div> -->
        <div class="activity-page-end">
          <el-button class="activity-page-btn" @click="goHome">首页</el-button>
          <el-pagination
            v-if="data && data.length > 0"
            background
            layout="prev, pager, next"
            class="activity-pagination"
            :page-size="pageSize"
            :current-page="pageNum"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          >
          </el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getActivityList } from "@/api/purchaseSales";
import { getDicts } from "@/api/system/dict/data";

export default {
  data() {
    return {
      loading: false,
      form: {
        keywords: "", //搜索内容
      },
      formInfo: {
        activityType: "", //活动类型
      },
      activityTypeList: [], //活动类型列表
      data: [],
      pageNum: 1,
      pageSize: 9,
      total: 0,
    };
  },
  created() {
    this.getDictsList("activity_type", "activityTypeList");
    this.search();
  },
  methods: {
    search() {
      this.loading = true;
      getActivityList({
        ...this.form,
        ...this.formInfo,
        pageNum: this.pageNum,
        pageSize: this.pageSize,
      })
        .then((res) => {
          this.loading = false;
          let { rows, total } = res || [];
          this.data = rows;
          this.total = total;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    // 字典
    getDictsList(code, propertyName) {
      getDicts(code).then((res) => {
        this[propertyName] = res.data || [];
      });
    },
    changeRadio() {
      this.onSearch();
    },
    handleSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.onSearch();
    },
    handleCurrentChange(pageNum) {
      this.pageNum = pageNum;
      this.search();
    },
    onSearch() {
      this.pageNum = 1;
      this.search();
    },
    // 跳转到最新活动页面
    goActivityDetail(id) {
      let routeData = this.$router.resolve({
        path: "/activityDetail",
        query: { id },
      });
      window.open(routeData.href, "_blank");
    },
    goHome() {
      this.$router.push({ path: "/index" });
    },
  },
};
</script>

<style lang="scss" scoped>
.activity-container {
  width: 100%;
  background: #f4f5f9;
  .activity-banner {
    width: 100%;
    height: 50vh;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .activity-title-content {
    width: 100%;
    background-color: #fff;
    padding-bottom: 18px;
    .activity-title-box {
      width: 336px;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 60px 0 40px;
      .activity-title {
        font-size: 40px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #333;
        line-height: 40px;
        padding: 0 40px;
      }
      .activity-divider {
        width: 48px;
        height: 4px;
        background: #21c9b8;
      }
    }
    .activity-search-box {
      .activity-search-form {
        text-align: center;
        .activity-search-input {
          width: 792px;
          height: 54px;
          .activity-search-btn {
            width: 100px;
          }
        }
      }
    }
  }
  .activity-info-content {
    width: 1200px;
    margin: 40px auto 0;
    .activity-search-type-box {
      background: #fff;
      margin-bottom: -7px;
      .activity-search-line {
        padding: 14px 24px 4px;
        .activity-search-line-item {
          margin-bottom: 0;
        }
        & + .activity-search-line {
          border-top: 1px solid #f5f5f5;
        }
      }
    }
    .activity-list-item {
      width: 100%;
      background: #fff;
      border-radius: 12px;
      margin-top: 24px;
      .list-item-content {
        display: flex;
        padding: 24px 32px;
        cursor: pointer;
        .list-item-img {
          width: 230px;
          height: 164px;
          img {
            width: 100%;
            height: 100%;
            border-radius: 5px;
          }
        }
        .list-item-info {
          padding-left: 24px;
          font-family: PingFangSC-Regular, PingFang SC;
          .list-item-title {
            width: 806px;
            height: 24px;
            text-overflow: ellipsis; /*让截断的文字显示为点点。还有一个值是clip意截断不显示点点*/
            white-space: nowrap; /*让文字不换行*/
            overflow: hidden; /*超出要隐藏*/
            font-size: 24px;
            font-weight: 500;
            color: #323233;
            line-height: 24px;
            margin: 8px 0 24px;
            word-wrap: break-word;
          }
          .list-item-text {
            width: 806px;
            height: 60px;
            overflow: hidden;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            text-overflow: ellipsis;
            font-size: 16px;
            color: #666;
            line-height: 30px;
            word-wrap: break-word;
          }
          .list-item-time {
            color: #999;
            line-height: 14px;
            margin-top: 24px;
          }
        }
        &:hover {
          .list-item-title {
            color: #21c9b8;
          }
        }
      }
    }
    .activity-page-end {
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0 auto;
      padding: 24px 0 60px;
      .activity-page-btn {
        width: 82px;
        height: 32px;
        background: #fff;
        border-radius: 4px;
        border: 1px solid #d9d9d9;
        font-family: PingFangSC-Regular, PingFang SC;
        color: #333;
        line-height: 10px;
      }
    }
  }
  .content {
    display: flex;
    // justify-content: space-between;
    flex-wrap: wrap;
    width: 100%;
    height: 100%;
    margin-top: 20px;
    padding-bottom: 30px;
    .contentItem {
      width: 32%;
      height: 340px;
      text-align: center;
      padding: 20px 30px;
      background: #fff;
      margin: 20px 0 0 23px;
      cursor: pointer;
      img {
        width: 100%;
        height: 230px;
      }
      .contentTitle {
        margin-top: 15px;
        font-size: 18px;
        font-family: Source Han Sans CN;
        font-weight: 500;
        color: #222222;
      }
    }
    .contentItem:nth-child(3n + 1) {
      margin-left: 0;
    }
  }
}
</style>

<style lang="scss">
.activity-container {
  .activity-search-input {
    .el-input__inner {
      height: 54px;
      background: #fff;
      border-radius: 27px 0 0 27px;
      border: 1px solid #d9d9d9;
      font-size: 16px;
      font-family: PingFangSC-Regular, PingFang SC;
      line-height: 24px;
      padding-left: 30px;
    }
    .el-input-group__append {
      border-radius: 0px 100px 100px 0px;
      background: #21c9b8;
      font-size: 16px;
      font-family: PingFangSC-Regular, PingFang SC;
      color: #fff;
      line-height: 24px;
    }
  }
  .activity-search-line {
    .el-form-item__label {
      width: 88px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #999;
      padding-right: 32px;
      text-align: left;
    }
    .activity-search-radio {
      width: 1050px;
      margin-top: 11px;
      .el-radio-button {
        padding-bottom: 20px;
        .el-radio-button__inner {
          border: none;
          padding: 0 32px 0 0;
          background: none;
          &:hover {
            color: #21c9b8;
          }
        }
        &.is-active {
          .el-radio-button__inner {
            color: #21c9b8;
            background: none;
          }
        }
        .el-radio-button__orig-radio:checked {
          & + .el-radio-button__inner {
            box-shadow: unset;
          }
        }
      }
    }
  }
  .activity-page-end {
    .activity-pagination {
      .btn-prev,
      .btn-next,
      .btn-quickprev {
        width: 32px;
        height: 32px;
        background: #fff;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        color: #333;
      }
      &.is-background {
        .el-pager {
          .number {
            width: 32px;
            height: 32px;
            border: 1px solid #d9d9d9;
            background: #fff;
            border-radius: 4px;
            line-height: 32px;
            &.active {
              background: #21c9b8;
              border: 1px solid #21c9b8;
            }
          }
        }
      }
    }
  }
}
</style>

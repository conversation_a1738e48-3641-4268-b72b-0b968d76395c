import {
  login,
  ssologin,
  loginCode,
  ssologinCode,
  logout,
  getInfo,
  refreshToken,
  accessToken,
} from "@/api/login";
import {
  getToken,
  setToken,
  setTicket,
  setExpiresIn,
  removeToken,
} from "@/utils/auth";

const user = {
  state: {
    token: getToken(),
    name: "",
    avatar: "",
    userId: "",
    bussinessNo: "",
    phonenumber: "",
    companyName: "",
    tel: "",
    avatar: "",
    roles: [],
    permissions: [],
  },

  mutations: {
    SET_TOKEN: (state, token) => {
      state.token = token;
    },
    SET_EXPIRES_IN: (state, time) => {
      state.expires_in = time;
    },
    SET_NAME: (state, name) => {
      state.name = name;
    },
    SET_COMPANYNAME: (state, name) => {
      state.companyName = name;
    },
    SET_TEL: (state, tel) => {
      state.tel = tel;
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar;
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles;
    },
    SET_USERID: (state, userId) => {
      state.userId = userId;
    },
    SET_BUSSINESSNO: (state, bussinessNo) => {
      state.bussinessNo = bussinessNo;
    },
    SET_PHONENUMBER: (state, phonenumber) => {
      state.phonenumber = phonenumber;
    },
    SET_PERMISSIONS: (state, permissions) => {
      state.permissions = permissions;
    },
    SET_USERINFO: (state, userInfo) => {
      state.userInfo = userInfo;
    },
  },

  actions: {
    // 登录
    Login({ commit }, userInfo) {
      const type = userInfo.type;
      const username = userInfo.username.trim();
      const smsCode = userInfo.smsCode;
      const password = userInfo.password;
      const code = userInfo.code;
      const uuid = userInfo.uuid;
      return new Promise((resolve, reject) => {
        let fetch =
          type === "account"
            ? login(username, password, code, uuid)
            : type === "code"
              ? loginCode(username, smsCode)
              : loginCode(username, smsCode, password);
        fetch
          .then((res) => {
            let data = res.data;
            setToken(data.access_token);
            commit("SET_TOKEN", data.access_token);
            setExpiresIn(data.expires_in);
            commit("SET_EXPIRES_IN", data.expires_in);
            getInfo()
              .then((res2) => {
                console.log(res2.member, "用户信息----------");
                commit("SET_USERID", res2.member.memberId);
                commit("SET_NAME", res2.member.memberRealName);
                commit("SET_PHONENUMBER", res2.member.memberPhone);
                commit("SET_COMPANYNAME", res2.member.companyName);
                commit("SET_AVATAR", res2.member.avatar);
                window.sessionStorage.setItem('userinfo', JSON.stringify(res2.member))
                // commit("SET_USERINFO", res2.member);
                resolve();
              })
              .catch((error) => {
                reject(error);
              });
            resolve();
          })
          .catch((error) => {
            reject(error);
          });
      });
    },

    // sso登录
    ssoLogin({ commit }, userInfo) {
      console.log(userInfo, "执行了吗-----------");
      const type = userInfo.type;
      const username = userInfo.username.trim();
      const smsCode = userInfo.smsCode;
      const password = userInfo.password;
      const code = userInfo.code;
      const uuid = userInfo.uuid;
      return new Promise((resolve, reject) => {
        let fetch =
          type === "account"
            ? ssologin(username, password, code, uuid)
            : type === "code"
              ? ssologinCode(username, smsCode)
              : ssologinCode(username, smsCode, password);
        fetch
          .then((res) => {
            let data = res.data;
            setToken(data.access_token);
            commit("SET_TOKEN", data.access_token);
            setExpiresIn(data.expires_in);
            commit("SET_EXPIRES_IN", data.expires_in);
            resolve(data);
          })
          .catch((error) => {
            reject(error);
          });
      });
    },

    // 获取用户信息
    GetInfo({ commit, state }) {
      return new Promise((resolve, reject) => {
        getInfo()
          .then((res) => {
            const user = res.member;
            const avatar =
              user.avatar == "" || user.avatar == null
                ? require("@/assets/images/profile.jpg")
                : user.avatar;
            // if (res.roles && res.roles.length > 0) {
            //   // 验证返回的roles是否是一个非空数组
            //   commit("SET_ROLES", res.roles);
            //   commit("SET_PERMISSIONS", res.permissions);
            // } else {
            //   commit("SET_ROLES", ["ROLE_DEFAULT"]);
            // }
            // commit("SET_NAME", user.realName);
            // commit("SET_USERID", user.userId);
            // commit("SET_BUSSINESSNO", user.bussinessNo);
            // commit("SET_PHONENUMBER", user.phonenumber);
            // commit("SET_COMPANYNAME", user.companyName);
            // commit("SET_TEL", user.phonenumber);
            commit("SET_AVATAR", avatar);
            commit("SET_USERID", res.member.memberId);
            // commit("SET_BUSSINESSNO", user.bussinessNo);
            commit("SET_PHONENUMBER", res.member.memberPhone);
            commit("SET_USERINFO", res.member);
            // commit("SET_COMPANYNAME", user.companyName);
            // commit("SET_TEL", user.phonenumber);
            // commit("SET_AVATAR", avatar);
            resolve(res);
          })
          .catch((error) => {
            reject(error);
          });
      });
    },

    // 刷新token
    RefreshToken({ commit, state }) {
      return new Promise((resolve, reject) => {
        refreshToken(state.token)
          .then((res) => {
            setExpiresIn(res.data);
            commit("SET_EXPIRES_IN", res.data);
            resolve();
          })
          .catch((error) => {
            reject(error);
          });
      });
    },

    // 退出系统
    LogOut({ commit, state }) {
      return new Promise((resolve, reject) => {
        logout(state.token)
          .then(() => {
            console.log();
            commit("SET_TOKEN", "");
            commit("SET_ROLES", []);
            commit("SET_PERMISSIONS", []);
            removeToken();
            resolve();
          })
          .catch((error) => {
            reject(error);
          });
      });
    },

    // 前端 登出
    FedLogOut({ commit }) {
      return new Promise((resolve) => {
        commit("SET_TOKEN", "");
        removeToken();
        resolve();
      });
    },

    // 根据回传票据获取登录token
    GetAccessToken({ commit }, ticket) {
      let params = {
        ticket,
      };
      console.log(params);
      return new Promise((resolve, reject) => {
        accessToken(params)
          .then((res) => {
            setToken(res.data.access_token);
            commit("SET_TOKEN", res.data.access_token);
            setTicket(ticket);
            resolve();
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
  },
};

export default user;

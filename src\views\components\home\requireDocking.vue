<template>
  <div
    class="card-container wow animate__animated animate__fadeInUp"
    data-wow-duration="1s"
  >
    <div class="enterpriseTitle">需求对接</div>
    <div class="content_top">
      <div
        class="typeLine"
        :style="{ left: projectTabs_activeLeft + 'px' }"
      ></div>
      <div class="content_top_leftRight" @click="getRequireType('1', $event)">
        <div :class="type == '1' ? 'typeStyleHover' : 'typeStyle'">需求</div>
        <!-- <div v-show="type == '1'" class="typeLine"></div> -->
      </div>
      <div class="content_top_center"></div>
      <div class="content_top_leftRight" @click="getRequireType('2', $event)">
        <div :class="type == '2' ? 'typeStyleHover' : 'typeStyle'">资源</div>
        <!-- <div v-show="type == '2'" class="typeLine"></div> -->
      </div>
      <div class="more" @click="goMore">查看更多 ></div>
    </div>
    <div v-loading="loading">
      <div class="content_bottom">
        <div
          class="content_bottom_item"
          v-for="(item, index) in requireData"
          :key="index"
          @click="goDetail(item.id)"
        >
          <div style="width: 100%; padding: 0 25px 0 24px">
            <div class="title ellipsis1">
              {{ item.title }}
            </div>
            <div class="desc ellipsis2">
              {{ item.description }}
            </div>
            <div class="button">
              <div v-show="item.typeName" class="buttonLeft">
                {{ item.typeName }}
              </div>
              <div class="buttonRight">→</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { demandData, supplyData } from "@/api/home";

export default {
  data() {
    return {
      type: "1",
      requireData: [],
      loading: false,
      projectTabs_activeLeft: 23,
    };
  },
  created() {
    this.getDemandList();
  },
  methods: {
    getDemandList() {
      this.loading = true;
      let params = {
        pageNum: 1,
        pageSize: 8,
      };
      demandData(params).then((res) => {
        if (res.code === 200) {
          this.requireData = res.rows;
          this.loading = false;
        }
      });
    },
    getSupplyList() {
      this.loading = true;
      let params = {
        pageNum: 1,
        pageSize: 8,
      };
      supplyData(params).then((res) => {
        if (res.code === 200) {
          console.log();
          this.requireData = res.rows;
          this.loading = false;
        }
      });
    },
    getRequireType(type, e) {
      this.projectTabs_activeLeft = e.target.offsetLeft;
      console.log(" getRequireType ~ e:", e.target.offsetLeft);
      this.type = type;
      if (this.type == 1) {
        this.getDemandList();
      } else {
        this.getSupplyList();
      }
    },
    goDetail(id) {
      if (this.type == 1) {
        this.$router.push("/demandDetail?id=" + id);
      } else {
        this.$router.push("/supplyDetail?id=" + id);
      }
    },
    goMore() {
      if (this.type == 1) {
        this.$router.push("/supplyDemandDocking?index=0");
      } else {
        this.$router.push("/supplyDemandDocking?index=1");
      }
    }
  },
};
</script>
<style lang="scss" scoped>
.enterpriseTitle {
  width: 100%;
  text-align: center;
  margin: 50px auto 42px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  font-size: 38px;
  color: #222222;
  // position: relative;
  // .allEnterprise {
  //   position: absolute;
  //   top: 50px;
  //   right: 0;
  //   font-size: 16px;
  //   font-family: Source Han Sans CN;
  //   font-weight: 500;
  //   color: #21C9B8;
  //   line-height: 26px;
  //   cursor: pointer;
  // }
}

.content_top {
  width: 100%;
  height: 54px;
  background: #ffffff;
  box-shadow: 0px 4px 20px 0px rgba(90, 90, 90, 0.1);
  border-radius: 4px;
  display: flex;
  padding: 0 25px 0 23px;
  position: relative;
  .content_top_leftRight {
    width: 40px;
    height: 100%;
    text-align: center;
    cursor: pointer;
    div {
      transition: all 0.3s ease-in;
    }
    .typeStyle {
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 18px;
      color: #222222;
      margin-top: 16px;
    }
    .typeStyleHover {
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 18px;
      color: #21c9b8;
      margin-top: 16px;
    }
  }
  .typeLine {
    width: 40px;
    height: 2px;
    background: #21c9b8;
    margin-top: 11px;
    position: absolute;
    bottom: 0;
    transition: all 0.25s ease-in;
  }
  .content_top_center {
    width: 2px;
    height: 18px;
    background: #e3e3e3;
    margin: 20px 20px 0;
  }
  .more {
    font-family: Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #787878;
    margin-left: auto;
    margin-top: 20px;
    cursor: pointer;
  }
}
.content_bottom {
  width: 100%;
  margin-top: 40px;
  margin-bottom: 62px;
  display: flex;
  flex-wrap: wrap;
  .content_bottom_item {
    width: 285px;
    height: 190px;
    box-shadow: 0px 4px 20px 0px rgba(90, 90, 90, 0.1);
    margin-left: 20px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    z-index: 1;
    transition: all 0.35s ease-in;

    &:before {
      content: "";
      z-index: -1;
      position: absolute;
      top: 100%;
      left: 100%;
      width: 46px;
      height: 46px;
      border-radius: 50%;
      background-color: #21c9b8;
      transform-origin: center;
      transform: translate3d(-50%, -50%, 0) scale3d(0, 0, 0);
      transition: transform 0.35s ease-in;
    }
    .title {
      // height: 18px;
      font-family: Source Han Sans CN;
      font-weight: 500;
      font-size: 18px;
      color: #222222;
      margin: 29px 0 18px 0;
    }
    .desc {
      width: 236px;
      height: 48px;
      font-family: Source Han Sans CN;
      transition: color 0.35s ease-in;
      font-weight: 400;
      font-size: 14px;
      color: #787878;
      line-height: 24px;
    }

    .button {
      margin-top: 28px;
      display: flex;
      align-items: center;
    }
    .buttonLeft {
      width: 76px;
      height: 26px;
      background: #e8f9f8;
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 14px;
      color: #21c9b8;
      text-align: center;
      line-height: 26px;
    }
    .buttonRight {
      width: 25px;
      color: #adacac;
      transition: color 0.3s ease-in;
      scale: 1.5;
      margin-left: auto;
    }
  }
  .content_bottom_item:nth-child(4n + 1) {
    margin-left: 0;
  }
  .content_bottom_item:nth-child(n + 5) {
    margin-top: 20px;
  }
  .content_bottom_item:hover {
    .desc {
      color: #ffffff;
    }
    .buttonRight {
      color: #ffffff !important;
    }
    &::before {
      transform: translate3d(-50%, -50%, 0) scale3d(15, 15, 15);
    }
    background-image: url("../../../assets/images/home/<USER>");
    background-size: 100% 100%;
    box-shadow: 0px 4px 20px 0px rgba(227, 248, 246, 1);
    z-index: 2;
    .buttonRight {
      width: 25px;
      color: #21c9b8;
      scale: 1.5;
      margin-left: auto;
    }
  }
}

.ellipsis1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  text-overflow: ellipsis;
  word-wrap: break-word;
}

.ellipsis2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  text-overflow: ellipsis;
  word-wrap: break-word;
}
</style>

<template>
  <div>
    <el-form ref="form" :rules="rules" :model="form" label-position="top">
      <el-form-item label="外协工序名称" prop="processName">
        <el-input v-model="form.processName" maxlength="50" show-word-limit placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="加工数量" prop="processingQuantity">
        <el-input type="number" min="0" v-model="form.processingQuantity" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="外协加工内容" prop="outsourcingContent">
        <el-input v-model="form.outsourcingContent" type="textarea" resize="none" :rows="8" maxlength="500"
          show-word-limit placeholder="请输入" />
      </el-form-item>
      <el-form-item label="要求完成时间" prop="requiredCompletionTime">
        <el-date-picker v-model="form.requiredCompletionTime" type="date" placeholder="选择日期" value-format="yyyy-MM-dd"
          style="width: 100%" />
      </el-form-item>
      <el-form-item label="工程令号" prop="projectNumber">
        <el-input v-model="form.projectNumber" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="备注" prop="remarks">
        <el-input v-model="form.remarks" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="公司名称" prop="companyName">
        <el-input disabled v-model="form.companyName" placeholder="请先绑定公司"></el-input>
      </el-form-item>
      <!-- <el-form-item label="上传附件" prop="enclosure">
        <FileUpload v-model="form.enclosure" />
      </el-form-item> -->
      <el-form-item label="联系人" prop="contactPerson">
        <el-input disabled v-model="form.contactPerson" placeholder="请先维护联系人"></el-input>
      </el-form-item>
      <el-form-item label="联系电话" prop="contactPhone">
        <el-input disabled v-model="form.contactPhone" placeholder="请先维护联系方式"></el-input>
      </el-form-item>
      <el-form-item class="footer-submit">
        <el-button type="primary" @click="onSubmit">发布</el-button>
        <el-button style="margin-left: 140px" @click.once="onCancel">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import { releaseProcess } from "@/api/release"

export default {
  data() {
    return {
      form: {
        processName: "",
        processingQuantity: "",
        outsourcingContent: "",
        requiredCompletionTime: "",
        projectNumber: "",
        remarks: "",
        companyName: "",
        contactPerson: "",
        contactPhone: "",
      },
      // 表单校验
      rules: {
        processName: [
          { required: true, message: "需求标题不能为空", trigger: "blur" },
        ],
        processingQuantity: [
          { required: true, message: "加工数量不能为空", trigger: "blur" },
        ],
        outsourcingContent: [
          { required: true, message: "外协加工内容不能为空", trigger: "blur" },
        ],
        requiredCompletionTime: [
          { required: true, message: "要求完成时间不能为空", trigger: "blur" },
        ],
        projectNumber: [
          { required: true, message: "工程令号不能为空", trigger: "blur" },
        ],
      },

    };
  },
  created() {
    let userinfo = JSON.parse(window.sessionStorage.getItem("userinfo"));
    if(userinfo && userinfo != 'null') {
      this.form.companyName = userinfo.memberCompanyName;
      this.form.contactPerson = userinfo.memberRealName;
      this.form.contactPhone = userinfo.memberPhone;
    }
  },
  methods: {
    onSubmit(status) {
      this.$refs["form"].validate((valid) => {
        if (valid) {

          releaseProcess(this.form).then(res => {
            if (res.code == 200) {
              this.$message.success("发布成功");
              this.onCancel()
            } else {
              this.$message.error('发布失败')
            }
          })
        }
      });
    },
    onCancel() {
      this.$router.go(-1);
    },
  },
};
</script>
<style lang="scss" scoped>
.footer-submit {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin-top: 60px;

  .el-button {
    width: 140px;
    height: 50px;
  }
}
</style>

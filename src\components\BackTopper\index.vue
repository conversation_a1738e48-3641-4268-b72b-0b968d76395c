<template>
  <transition name="el-fade-in">
    <div v-show="visible" class="left-bar">
      <div class="left-bar-item create" @click="goPublish">
        <svg
          class="icon"
          viewBox="0 0 16 15"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          xmlns:xlink="http://www.w3.org/1999/xlink"
        >
          <title>发布需求</title>
          <g
            id="Web"
            stroke="none"
            stroke-width="1"
            fill="none"
            fill-rule="evenodd"
          >
            <g
              id="首页-1备份-5"
              transform="translate(-1872.000000, -894.000000)"
              fill="#666666"
              fill-rule="nonzero"
            >
              <g id="编组-14" transform="translate(1840.000000, 792.000000)">
                <g id="发布供需0" transform="translate(32.000000, 102.000000)">
                  <path
                    d="M8.38940592,15 L2.05523449,15 C0.903672664,14.9764217 -0.0133456064,14.0529692 0.000147765112,12.9304939 L0.000147765112,2.07342565 C-0.0133756596,0.950280288 0.903032382,0.0256945182 2.05523449,0 L12.6805557,0 C13.8412914,0.0150609322 14.7709522,0.94213234 14.7598724,2.07342565 L14.7598724,6.2241965 C14.7597162,6.54886123 14.4896466,6.81202487 14.1565182,6.81202487 C13.8233899,6.81202487 13.5533202,6.54886123 13.5532638,6.2241965 L13.5532638,2.07342565 C13.5597503,1.84158899 13.4714289,1.61674452 13.3077462,1.44839883 C13.1440635,1.28005313 12.9184407,1.18200966 12.6805557,1.17585576 L2.05523449,1.17585576 C1.56938668,1.20118186 1.19287132,1.59943505 1.2066566,2.07342565 L1.2066566,12.9304939 C1.20016967,13.1612911 1.28806726,13.3851388 1.4509958,13.5527496 C1.61392435,13.7203603 1.83852445,13.8179907 2.07534297,13.8241442 L8.38940592,13.8241442 C8.72257412,13.8241443 8.99266031,14.0873685 8.99266031,14.4120721 C8.99266031,14.7367757 8.72257412,15 8.38940592,15 Z"
                    id="路径"
                  ></path>
                  <path
                    d="M11.260897,14.8510642 C10.9996538,14.8520458 10.7811726,14.6578533 10.758185,14.4042331 C10.6335124,12.8991377 10.6335124,12.8991377 10.7260114,12.7462765 L13.629676,8.04285341 C13.6968635,7.93237473 13.8071512,7.85317475 13.9353249,7.82336033 C14.0780496,7.78639325 14.2297299,7.80173375 14.3616247,7.86647504 L15.7531316,8.67781552 C15.8682548,8.74408854 15.9509475,8.85295812 15.9823683,8.9796185 L15.9823683,9.0031356 C16.017972,9.1354369 15.999215,9.27605555 15.9300862,9.39508754 L13.0304433,14.0985106 C12.9299009,14.2592109 12.9299009,14.2592109 11.445895,14.8157826 C11.3872384,14.839378 11.3243528,14.8513691 11.260897,14.8510642 L11.260897,14.8510642 Z M11.6630666,13.1264698 C11.6630666,13.2479749 11.6630666,13.4439509 11.7032836,13.6673635 L12.2462125,13.459629 L14.8200981,9.29709955 L14.2409738,8.95610138 L11.6630666,13.1264698 Z M10.6576425,5.09537497 L4.07814765,5.09537497 C3.74497943,5.09537497 3.47489323,4.83215069 3.47489323,4.50744709 C3.47489323,4.18274349 3.74497943,3.91951921 4.07814765,3.91951921 L10.6576425,3.91951921 C10.9908108,3.91951921 11.260897,4.18274349 11.260897,4.50744709 C11.260897,4.83215069 10.9908108,5.09537497 10.6576425,5.09537497 Z M9.10124615,7.96446302 L4.07814765,7.96446302 C3.74497943,7.96446302 3.47489323,7.70123874 3.47489323,7.37653514 C3.47489323,7.05183153 3.74497943,6.78860726 4.07814765,6.78860726 L9.10124615,6.78860726 C9.43441437,6.78860726 9.70450057,7.05183153 9.70450057,7.37653514 C9.70450057,7.70123874 9.43441437,7.96446302 9.10124615,7.96446302 Z M7.64137045,10.8296316 L4.07814765,10.8296316 C3.74497943,10.8296316 3.47489323,10.5664073 3.47489323,10.2417037 C3.47489323,9.91700008 3.74497943,9.65377581 4.07814765,9.65377581 L7.64137045,9.65377581 C7.97453867,9.65377581 8.24462487,9.91700008 8.24462487,10.2417037 C8.24462487,10.5664073 7.97453867,10.8296316 7.64137045,10.8296316 L7.64137045,10.8296316 Z"
                    id="形状"
                  ></path>
                </g>
              </g>
            </g>
          </g>
        </svg>
        <span class="text">需求定制</span>
        <!-- <div class="add-menu">
          <router-link to="/addSource?type=demand" class="add-menu-item">
            <svg
              class="add-menu-item-icon"
              viewBox="0 0 18 18"
              version="1.1"
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
            >
              <title>发布需求</title>
              <g
                id="Web"
                stroke="none"
                stroke-width="1"
                fill="none"
                fill-rule="evenodd"
              >
                <g
                  id="首页-1备份-5"
                  transform="translate(-1744.000000, -886.000000)"
                  fill="#666"
                  fill-rule="nonzero"
                >
                  <g
                    id="编组-14备份"
                    transform="translate(1732.000000, 872.000000)"
                  >
                    <g
                      id="thunderbolt"
                      transform="translate(12.000000, 14.000000)"
                    >
                      <rect
                        id="矩形"
                        fill="#000000"
                        opacity="0"
                        x="0"
                        y="0"
                        width="18"
                        height="18"
                      ></rect>
                      <path
                        d="M15.5625,6.01757813 L11.2597656,6.01757813 L15.1289063,1.12890625 C15.2089844,1.02539062 15.1367188,0.875 15.0058594,0.875 L7.515625,0.875 C7.4609375,0.875 7.40820313,0.904296875 7.38085938,0.953125 L2.3203125,9.69335938 C2.25976563,9.796875 2.33398438,9.92773438 2.45507813,9.92773438 L5.86132813,9.92773438 L4.11523438,16.9121094 C4.078125,17.0644531 4.26171875,17.171875 4.375,17.0625 L15.6699219,6.28515625 C15.7714844,6.18945313 15.703125,6.01757813 15.5625,6.01757813 Z M6.38671875,13.3066406 L7.56445312,8.59960938 L4.49023438,8.59960938 L8.19335938,2.20507812 L12.5800781,2.20507812 L8.51171875,7.34765625 L12.6328125,7.34765625 L6.38671875,13.3066406 Z"
                        id="形状"
                      ></path>
                    </g>
                  </g>
                </g>
              </g>
            </svg>
            <span class="add-menu-item-text">发布需求</span>
          </router-link>
          <router-link to="/addSource?type=supply" class="add-menu-item">
            <svg
              class="add-menu-item-icon"
              viewBox="0 0 18 18"
              version="1.1"
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
            >
              <title>发布资源</title>
              <g
                id="Web"
                stroke="none"
                stroke-width="1"
                fill="none"
                fill-rule="evenodd"
              >
                <g
                  id="首页-1备份-5"
                  transform="translate(-1744.000000, -928.000000)"
                  fill-rule="nonzero"
                >
                  <g
                    id="编组-14备份"
                    transform="translate(1732.000000, 872.000000)"
                  >
                    <g
                      id="experiment"
                      transform="translate(12.000000, 56.000000)"
                    >
                      <rect
                        id="矩形"
                        fill="#000000"
                        opacity="0"
                        x="0"
                        y="0"
                        width="18"
                        height="18"
                      ></rect>
                      <path
                        d="M9,8.21875 C9,8.65022246 9.34977754,9 9.78125,9 C10.2127225,9 10.5625,8.65022246 10.5625,8.21875 C10.5625,7.78727754 10.2127225,7.4375 9.78125,7.4375 C9.34977754,7.4375 9,7.78727754 9,8.21875 Z M16.1679688,15.1113281 L12.5996094,5.875 L12.5996094,2.4765625 L14,2.4765625 L14,1.1484375 L4,1.1484375 L4,2.4765625 L5.40039062,2.4765625 L5.40039062,5.875 L1.83203125,15.1113281 C1.77734375,15.2558594 1.74804688,15.4082031 1.74804688,15.5625 C1.74804688,16.2519531 2.30859375,16.8125 2.99804688,16.8125 L15.0019531,16.8125 C15.15625,16.8125 15.3085937,16.7832031 15.453125,16.7285156 C16.0976562,16.4804688 16.4179687,15.7558594 16.1679688,15.1113281 Z M6.72851562,6.12304688 L6.72851562,2.515625 L11.2714844,2.515625 L11.2714844,6.12304688 L13.046875,10.71875 C12.6425781,10.6152344 12.2246094,10.5625 11.7988281,10.5625 C10.6035156,10.5625 9.47070312,10.9824219 8.5703125,11.734375 C7.90820312,12.2851563 7.08007813,12.5917969 6.20117188,12.5917969 C5.5625,12.5917969 4.94921875,12.4296875 4.40820312,12.1289062 L6.72851562,6.12304688 Z M3.11132812,15.484375 L3.92578125,13.3789062 C4.62304688,13.7324219 5.3984375,13.921875 6.203125,13.921875 C7.3984375,13.921875 8.53125,13.5019531 9.43164062,12.75 C10.09375,12.1992187 10.921875,11.8925781 11.8007812,11.8925781 C12.484375,11.8925781 13.1367187,12.078125 13.7070312,12.421875 L14.8886719,15.484375 L3.11132812,15.484375 Z"
                        id="形状"
                        fill="#666666"
                      ></path>
                    </g>
                  </g>
                </g>
              </g>
            </svg>
            <span class="add-menu-item-text">发布资源</span>
          </router-link>
        </div> -->
      </div>
      <div @click.stop="handleClick" class="left-bar-item">
        <svg
          class="icon"
          viewBox="0 0 16 16"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          xmlns:xlink="http://www.w3.org/1999/xlink"
        >
          <title>返回顶部</title>
          <g
            id="Web"
            stroke="none"
            stroke-width="1"
            fill="none"
            fill-rule="evenodd"
          >
            <g
              id="首页-1备份-5"
              transform="translate(-1872.000000, -813.000000)"
              fill="#666666"
              fill-rule="nonzero"
            >
              <g id="编组-14" transform="translate(1840.000000, 792.000000)">
                <g id="返回顶部" transform="translate(32.000000, 21.000000)">
                  <polygon
                    id="路径"
                    points="0 0 16 0 16 1.23105499 4.72056094e-16 1.23105499"
                  ></polygon>
                  <polygon
                    id="路径"
                    points="8.87019131 2.694432 8.0000085 1.824032 7.12980871 2.694432 1.47296613 8.35257601 2.34314893 9.222976 7.38461408 4.180351 7.38461408 16 8.61538594 16 8.61538594 4.180351 13.6568511 9.222976 14.5270509 8.35257601"
                  ></polygon>
                </g>
              </g>
            </g>
          </g>
        </svg>
        <span class="text">返回顶部</span>
      </div>
      <!-- <div class="left-bar-item qrcode">
        <svg
          class="icon"
          width="14px"
          height="16px"
          viewBox="0 0 14 16"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          xmlns:xlink="http://www.w3.org/1999/xlink"
        >
          <title>产品入口1</title>
          <g
            id="Web"
            stroke="none"
            stroke-width="1"
            fill="none"
            fill-rule="evenodd"
          >
            <g
              id="首页-1备份-5"
              transform="translate(-1873.000000, -973.000000)"
              fill="#666666"
              fill-rule="nonzero"
            >
              <g id="编组-14" transform="translate(1840.000000, 792.000000)">
                <g id="产品入口1" transform="translate(33.125000, 181.187500)">
                  <path
                    d="M13.125,3.90625 L10.46875,3.90625 L10.46875,3.59375 C10.46875,1.609375 8.859375,0 6.875,0 C4.890625,0 3.28125,1.609375 3.28125,3.59375 L3.28125,3.90625 L0.625,3.90625 C0.279296875,3.90625 0,4.18554688 0,4.53125 L0,15 C0,15.3457031 0.279296875,15.625 0.625,15.625 L13.125,15.625 C13.4707031,15.625 13.75,15.3457031 13.75,15 L13.75,4.53125 C13.75,4.18554688 13.4707031,3.90625 13.125,3.90625 Z M4.6875,3.59375 C4.6875,2.38476562 5.66601562,1.40625 6.875,1.40625 C8.08398438,1.40625 9.0625,2.38476562 9.0625,3.59375 L9.0625,3.90625 L4.6875,3.90625 L4.6875,3.59375 Z M12.34375,14.21875 L1.40625,14.21875 L1.40625,5.3125 L3.28125,5.3125 L3.28125,7.03125 C3.28125,7.1171875 3.3515625,7.1875 3.4375,7.1875 L4.53125,7.1875 C4.6171875,7.1875 4.6875,7.1171875 4.6875,7.03125 L4.6875,5.3125 L9.0625,5.3125 L9.0625,7.03125 C9.0625,7.1171875 9.1328125,7.1875 9.21875,7.1875 L10.3125,7.1875 C10.3984375,7.1875 10.46875,7.1171875 10.46875,7.03125 L10.46875,5.3125 L12.34375,5.3125 L12.34375,14.21875 Z"
                    id="形状"
                  ></path>
                </g>
              </g>
            </g>
          </g>
        </svg>
        <span class="text">产品入口</span>
        <div class="qrcode-menu">
          <div class="qrcode-menu-content">
            <div class="qrcode-el">
              <el-image
                style="width: 80px; height: 80px"
                :src="require('@/assets/user/wait.png')"
                fit="fill"
              />
              <div class="qrcode-el-title">门户端</div>
              <div class="qrcode-el-desc">政策服务/申报/数据掌握</div>
            </div>
            <div class="qrcode-el">
              <el-image
                style="width: 80px; height: 80px"
                :src="require('@/assets/user/company_mini.png')"
                fit="fill"
              />
              <div class="qrcode-el-title">企业端-云端研发</div>
              <div class="qrcode-el-desc">研发/采购/销售/政策/服务</div>
            </div>
            <div class="qrcode-el">
              <el-image
                style="width: 80px; height: 80px"
                :src="require('@/assets/user/resource_mini.png')"
                fit="fill"
              />
              <div class="qrcode-el-title">资源端</div>
              <div class="qrcode-el-desc">供应商/服务商/专家</div>
            </div>
            <div class="qrcode-el">
              <el-image
                style="width: 80px; height: 80px"
                :src="require('@/assets/user/gov_mini.png')"
                fit="fill"
              />
              <div class="qrcode-el-title">政府端</div>
              <div class="qrcode-el-desc">政策服务/申报/数据掌握</div>
            </div>
          </div>
        </div>
      </div> -->
    </div>
  </transition>
</template>

<script>
import throttle from "throttle-debounce/throttle";

const cubic = (value) => Math.pow(value, 3);
const easeInOutCubic = (value) =>
  value < 0.5 ? cubic(value * 2) / 2 : 1 - cubic((1 - value) * 2) / 2;

export default {
  name: "ElBacktop",

  props: {
    visibilityHeight: {
      type: Number,
      default: 200,
    },
    target: [String],
  },

  data() {
    return {
      el: null,
      container: null,
      visible: true,
    };
  },

  mounted() {
    this.init();
    // this.throttledScrollHandler = throttle(300, this.onScroll);
    // this.container.addEventListener("scroll", this.throttledScrollHandler);
  },

  methods: {
    init() {
      this.container = document;
      this.el = document.documentElement;
      if (this.target) {
        this.el = document.querySelector(this.target);
        if (!this.el) {
          throw new Error(`target is not existed: ${this.target}`);
        }
        this.container = this.el;
      }
    },
    onScroll() {
      const scrollTop = this.el.scrollTop;
      this.visible = scrollTop >= this.visibilityHeight;
    },
    handleClick(e) {
      this.scrollToTop();
      this.$emit("click", e);
    },
    scrollToTop() {
      const el = this.el;
      const beginTime = Date.now();
      const beginValue = el.scrollTop;
      const rAF =
        window.requestAnimationFrame || ((func) => setTimeout(func, 16));
      const frameFunc = () => {
        const progress = (Date.now() - beginTime) / 500;
        if (progress < 1) {
          el.scrollTop = beginValue * (1 - easeInOutCubic(progress));
          rAF(frameFunc);
        } else {
          el.scrollTop = 0;
        }
      };
      rAF(frameFunc);
    },
    goPublish() {
      this.$router.push({
        path: "/release",
      });
    },
  },

  beforeDestroy() {
    this.container.removeEventListener("scroll", this.throttledScrollHandler);
  },
};
</script>
<style scoped lang="scss">
@import "~@/assets/styles/mixin.scss";
.left-bar {
  position: fixed;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 80px;
  background: #ffffff;
  box-shadow: 0 10px 50px 0 rgba(38, 74, 116, 0.2);
  z-index: 999;
  &-item {
    @include flexCenter;
    flex-direction: column;
    position: relative;
    width: 100%;
    height: 80px;
    cursor: pointer;
    background-color: #ffffff;
    .icon {
      width: 16px;
      height: 16px;
      object-fit: contain;
      margin-bottom: 8px;
    }
    .text {
      height: 14px;
      font-size: 14px;
      font-weight: 400;
      color: #666666;
      line-height: 14px;
    }
    .add-menu {
      display: none;
      position: absolute;
      right: 80px;
      top: 2.5px;
      width: 108px;
      box-shadow: 0 10px 50px 0 rgba(38, 74, 116, 0.2);
      z-index: 10;
      padding-right: 4px;
      &-item {
        @include flexCenter;
        flex-shrink: 0;
        width: 100%;
        height: 38px;
        background: #ffffff;
        &-icon {
          width: 18px;
          height: 18px;
          margin-right: 6px;
        }
        &-text {
          font-size: 14px;
          font-weight: 400;
          color: #666666;
          line-height: 14px;
        }
        &:hover {
          background-color: #21c9b8;
          .add-menu-item-icon {
            path {
              fill: #ffffff;
            }
          }
          .add-menu-item-text {
            color: #ffffff;
          }
        }
      }
    }
    .qrcode-menu {
      display: none;
      position: absolute;
      right: 80px;
      top: -160px;
      width: 160px;
      box-shadow: 0 10px 50px 0 rgba(38, 74, 116, 0.2);
      z-index: 10;
      padding-right: 4px;
      &-content {
        @include flexCenter;
        flex-direction: column;
        flex-shrink: 0;
        width: 100%;
        background: #ffffff;
        padding: 12px 5px;
        .qrcode-el {
          @include flexCenter;
          flex-direction: column;
          margin-bottom: 20px;
          &-title {
            font-size: 12px;
            font-weight: 500;
            color: #333333;
            line-height: 12px;
            padding: 6px 0 8px 0;
          }
          &-desc {
            font-size: 10px;
            font-weight: 400;
            color: #999999;
            line-height: 10px;
          }
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
    &.create {
      &:hover {
        .add-menu {
          display: block;
        }
      }
    }
    &.qrcode {
      &:hover {
        .qrcode-menu {
          display: block;
        }
      }
    }
    &:hover {
      .icon {
        path {
          fill: #ffffff;
        }
      }
      background-color: #21c9b8;
      .text {
        color: #ffffff;
      }
      .icon {
        fill: green;
        color: #ffffff;
      }
    }
  }
}
</style>

<template>
  <div class="resource-hall-detail-container">
    <!-- banner图 -->
    <div class="resource-hall-detail-banner">
      <img
        src="../../../../assets/resourceHall/resourceHallDetailBanner.png"
        alt=""
      />
    </div>
    <div class="resource-hall-detail-title-box">
      <div class="resource-hall-detail-divider"></div>
      <div class="resource-hall-detail-title">资源详情</div>
      <div class="resource-hall-detail-divider"></div>
    </div>
    <div v-loading="loading" class="resource-hall-detail-content">
      <div class="resource-hall-detail-box">
        <div class="resource-hall-detail-box-title">
          {{ data.supplyName }}
        </div>
        <div class="resource-hall-detail-headline">
          <div class="headline-content">
            <div class="headline-content-item">
              <div class="item-title">技术类别：</div>
              <div class="item-content">
                {{ data.technologyType }}
              </div>
            </div>
            <div class="headline-content-item">
              <div class="item-title">应用领域：</div>
              <div class="item-content">
                {{ data.applicationArea }}
              </div>
            </div>
            <div class="headline-content-item">
              <div class="item-title">产品阶段：</div>
              <div class="item-content">{{ data.productStageName }}</div>
            </div>
            <div class="headline-content-item">
              <div class="item-title">合作方式：</div>
              <div class="item-content">
                {{ data.cooperationModeName }}
              </div>
            </div>
            <div class="headline-content-item">
              <div class="item-title">供给方：</div>
              <div class="item-content">
                {{ data.companyName }}
              </div>
            </div>
            <div class="headline-content-item">
              <div class="item-title">联系人：</div>
              <div class="item-content">
                {{ data.contactsName }}
              </div>
            </div>
            <div class="headline-content-item">
              <div class="item-title">联系方式：</div>
              <div class="item-content">
                {{ data.contactsMobile }}
              </div>
            </div>
            <div class="headline-content-item">
              <div class="item-title">发布时间：</div>
              <div class="item-content">
                {{ data.createTime }}
              </div>
            </div>
            <div class="headline-content-btn">
              <el-button
                v-if="showBtn"
                class="headline-btn-style intention-btn"
                @click="goIntention"
                >我有意向</el-button
              >
              <el-button
                @click="goChat"
                class="headline-btn-style communication-btn"
                icon="el-icon-chat-dot-round"
                >在线沟通</el-button
              >
            </div>
          </div>
          <div class="headline-img">
            <img
              v-if="data.productPhoto && data.productPhoto.length > 0"
              :src="data.productPhoto[0].url"
              alt=""
            />
            <img
              v-else
              src="../../../../assets/resourceHall/resourceHallDetailBanner.png"
              alt=""
            />
          </div>
        </div>
        <div class="resource-hall-detail-description">
          <div class="description-title-box">
            <div class="description-divider"></div>
            <div class="description-title">资源描述</div>
          </div>
          <div class="description-content">
            <div v-html="data.summary" class="description-text ql-editor"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getSupplyDetail, getCheckSubmit } from "@/api/purchaseSales";
import { getInfo } from "@/api/login";
import { getCompanyInfoByLoginInfo } from "@/api/apathy";
import { mapGetters } from "vuex";
import CryptoJS from "crypto-js";
let secretKey = "9zVn0%bqmUYSGw2n";

export default {
  data() {
    return {
      loading: false,
      data: {},
      showBtn: true,
    };
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      this.loading = true;
      getSupplyDetail(this.$route.query.id)
        .then((res) => {
          let key = CryptoJS.enc.Utf8.parse(secretKey);
          let decrypt = CryptoJS.AES.decrypt(res, key, {
            mode: CryptoJS.mode.ECB,
            padding: CryptoJS.pad.Pkcs7,
          });
          res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));
          this.loading = false;
          this.data = res.data || {};
          this.data.productPhoto = JSON.parse(this.data.productPhoto);
          if (!this.token) {
            this.showBtn = true;
          } else {
            this.getInfo();
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
    // 判断此资源是不是自己发布的
    getInfo() {
      getInfo().then((res) => {
        if (this.data.createById === res.user.userId) {
          this.showBtn = false;
        } else {
          this.showBtn = true;
        }
      });
    },
    goChat() {
      // 判断是否登录
      if (!this.token) {
        this.$confirm("请先登录", "提示", {
          confirmButtonText: "去登录",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          this.$store.dispatch("LogOut").then(() => {
            location.href = "/login";
          });
        });
        return;
      }
      let routeData = this.$router.resolve({
        path: "/user/im",
        query: {
          userId: this.data.createImById,
        },
      });
      window.open(routeData.href, "_blank");
    },
    // 跳到我有意向页面
    goIntention() {
      // 判断是否登录
      if (!this.token) {
        this.$confirm("请先登录", "提示", {
          confirmButtonText: "去登录",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          this.$store.dispatch("LogOut").then(() => {
            location.href = "/login";
          });
        });
        return;
      }
      this.loading = true;
      // 是否加入企业
      getCompanyInfoByLoginInfo()
        .then((res) => {
          if (res.data) {
            // 是否对此资源提交过意向
            getCheckSubmit({
              id: this.$route.query.id,
              resourceType: "resource_supply",
            })
              .then((res) => {
                this.loading = false;
                // true 提交过  false未提交过
                if (res.data) {
                  this.$message({
                    type: "warning",
                    message: "已经提交过了哦！",
                  });
                } else {
                  let routeData = this.$router.resolve({
                    path: "/addIntention",
                    query: {
                      id: this.$route.query.id,
                      type: "resource_supply",
                      title: this.data.supplyName,
                    },
                  });
                  window.open(routeData.href, "_blank");
                }
              })
              .catch(() => {
                this.loading = false;
              });
          } else {
            this.loading = false;
            this.$message({
              type: "warning",
              message: "必须加入企业才可提交我有意向",
            });
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
  },
  computed: {
    ...mapGetters(["token"]),
  },
};
</script>

<style lang="scss" scoped>
.resource-hall-detail-container {
  width: 100%;
  background: #f4f5f9;
  .resource-hall-detail-banner {
    width: 100%;
    height: 25.93vh;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .resource-hall-detail-title-box {
    width: 336px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 60px 0 40px;
    .resource-hall-detail-title {
      font-size: 40px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #333;
      line-height: 40px;
      padding: 0 40px;
    }
    .resource-hall-detail-divider {
      width: 48px;
      height: 4px;
      background: #21c9b8;
    }
  }
  .resource-hall-detail-content {
    background: #f4f5f9;
    padding-bottom: 70px;
    .resource-hall-detail-box {
      width: 1200px;
      background: #fff;
      margin: 0 auto;
      padding: 60px 60px 192px;
      .resource-hall-detail-box-title {
        width: 100%;
        font-size: 32px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #333;
        line-height: 32px;
        word-wrap: break-word;
      }
      .resource-hall-detail-headline {
        display: flex;
        justify-content: space-between;
        margin-top: 40px;
        padding-bottom: 40px;
        border-bottom: 1px solid #e8e8e8;
        .headline-content {
          flex: 1;
          .headline-content-item {
            display: flex;
            font-family: PingFangSC-Regular, PingFang SC;
            line-height: 32px;
            .item-title {
              width: 80px;
              color: #666;
            }
            .item-content {
              flex: 1;
              max-width: 560px;
              color: #333;
              word-wrap: break-word;
            }
          }
          .headline-content-btn {
            padding-top: 112px;
            .headline-btn-style {
              width: 100px;
              height: 32px;
              border-radius: 4px;
              font-family: PingFangSC-Regular, PingFang SC;
              padding: 8px 11px;
            }
            .intention-btn {
              background: #21c9b8;
              color: #fff;
            }
            .communication-btn {
              border: 1px solid #21c9b8;
              color: #21c9b8;
            }
          }
        }
        .headline-img {
          width: 400px;
          height: 240px;
          margin-left: 20px;
          img {
            width: 100%;
            height: 100%;
          }
        }
      }
    }
    .resource-hall-detail-description {
      padding-top: 39px;
      .description-title-box {
        display: flex;
        align-items: center;
        padding-bottom: 40px;
        .description-divider {
          width: 4px;
          height: 20px;
          background: #21c9b8;
        }
        .description-title {
          font-size: 24px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #333;
          line-height: 24px;
          padding-left: 8px;
        }
      }
      .description-content {
        font-size: 16px;
        font-family: PingFangSC-Regular, PingFang SC;
        color: #333;
        line-height: 28px;
      }
    }
  }
}
</style>

<style lang="scss">
.resource-hall-detail-container {
  .description-content {
    .description-text {
      word-break: break-all;
      font-size: 16px;
      line-height: 28px;
      color: #333;
      font-family: PingFangSC-Regular, PingFang SC;
      img {
        max-width: 100%;
      }
    }
  }
}
</style>

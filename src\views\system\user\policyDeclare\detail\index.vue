<!--
 * @Author: zhc
 * @Date: 2023-02-03 11:06:49
 * @LastEditTime: 2023-02-17 16:19:40
 * @Description: 
 * @LastEditors: zhc
-->
<template>
  <div class="app-container">
    <div class="notice-record-detail">
      <el-row :gutter="20">
        <el-col :span="2.5" :xs="24">
          <user-menu />
        </el-col>
        <el-col :span="20" :xs="24">
          <div class="info-container">
            <div class="header">
              <div class="header-text">申报详情</div>
            </div>
            <div class="detail-page" v-if="isDetail">
              <div class="header-small">
                <div class="red-tag"></div>
                基本信息
              </div>

              <el-descriptions class="margin-top" :column="1" border>
                <el-descriptions-item>
                  <template slot="label"> 政策名称 </template>
                  {{ info.policyName }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label"> 联系人 </template>
                  {{ info.contractPerson }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label"> 联系电话</template>
                  {{ info.contractPhone }}
                </el-descriptions-item>

                <el-descriptions-item>
                  <template slot="label"> 资料上传 </template>
                  <a
                    class="file-class"
                    v-for="item in info.fileList"
                    v-bind:key="item.url"
                    @click="handleFilePreview(item.url)"
                  >
                    <el-image
                      style="width: 14px; height: 17px"
                      :src="require('@/assets/user/file_pdf.png')"
                    ></el-image>
                    {{ item.name }}
                    <div class="previwe-class">立即查看</div>
                  </a>
                </el-descriptions-item>
              </el-descriptions>
              <el-image
                class="status_approving"
                v-if="info.status == '2'"
                style="width: 120px; height: 102px"
                :src="require('@/assets/user/status_approving.png')"
              ></el-image>
              <div class="delete-btn">
                <el-button @click="goBack">返回</el-button>
                <el-button
                  v-if="info.status == '1'"
                  type="danger"
                  @click="changeMode"
                  >编辑</el-button
                >
              </div>
            </div>
            <div class="edit-page" v-else>
              <el-form
                ref="form"
                :model="form"
                :rules="rules"
                label-width="120px"
              >
                <el-form-item label="政策名称" prop="policyName">
                  <el-input
                    v-model="form.policyName"
                    disabled
                    placeholder="政策名称"
                  />
                </el-form-item>
                <el-row>
                  <el-col :span="24">
                    <el-form-item label="联系人" prop="contractPerson">
                      <el-input
                        v-model="form.contractPerson"
                        placeholder="请输入联系人"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row>
                  <el-col :span="24">
                    <el-form-item label="联系电话" prop="contractPhone">
                      <el-input
                        v-model="form.contractPhone"
                        placeholder="请选择联系电话"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row>
                  <el-col :span="24">
                    <el-form-item label="上传文件" prop="fileList">
                      <el-upload
                        :headers="headers"
                        :action="actionUrl"
                        accept=".pdf, .doc, .xls"
                        :file-list="form.fileList"
                        :on-remove="handleApplicationRemove"
                        :on-success="handleApplicationSuccess"
                        :limit="10"
                      >
                        <div>
                          <el-button
                            size="small"
                            type="primary"
                            icon="el-icon-upload2"
                            >上传文件</el-button
                          >
                          <span class="tip">仅限doc、pdf、xls格式</span>
                        </div>
                      </el-upload>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
              <div class="delete-btn">
                <el-button @click="changeMode">返回</el-button>
                <el-button type="error" @click="changeMode(1)"
                  >暂存草稿</el-button
                >
                <el-button type="danger" @click="submitForm(2)"
                  >提交审核</el-button
                >
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
let id = 0;

import UserMenu from "../../components/userMenu.vue";
import { getPolicyDetail, editPolicyApply } from "@/api/system/policy";
import { uploadUrl } from "@/api/oss";
import { getToken } from "@/utils/auth";

export default {
  name: "Notice",
  dicts: ["affiliated_unit", "capital_source", "affiliated_street"],
  components: { UserMenu },
  data() {
    return {
      isDetail: true,
      actionUrl: uploadUrl(),
      headers: { Authorization: "Bearer " + getToken() },
      info: {},
      form: {},
      accountLicenceList: [],
      // 表单校验
      rules: {
        policyName: [
          { required: true, message: "所属单位不能为空", trigger: "blur" },
        ],
        contractPerson: [
          { required: true, message: "联系人不能为空", trigger: "blur" },
        ],
        contractPhone: [
          { required: true, message: "联系电话不能为空", trigger: "blur" },
        ],
        fileList: [
          { required: true, message: "文件不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getDetail();
  },
  methods: {
    getDetail() {
      let userId = this.$route.query.id;
      getPolicyDetail(userId).then((response) => {
        this.info = response.data;
        this.total = response.total;
      });
    },
    goBack() {
      this.$router.go(-1);
    },

    changeMode() {
      if (this.isDetail) {
        this.isDetail = false;
        this.form = this.info;
      } else {
        this.isDetail = true;
        this.form = {};
      }
      this.getDetail();
    },
    handleFilePreview(file) {
      window.open(file);
    },
    submitForm(type) {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.form.status = type;
          editPolicyApply({ ...this.form }).then((response) => {
            this.$modal.msgSuccess("操作成功");
            this.changeMode();
          });
        }
      });
    },
    handleApplicationRemove(file, fileList) {
      this.form.application = "";
    },
    handleApplicationSuccess(res, file, fileList) {
      //此处写上传oss成功之后的逻辑
      if (res.code == 200) {
        this.form.application = res.data.url;
        this.form.applicationName = res.data.name;
      }
    },
    handleAccountRemove(file, fileList) {
      this.form.accountLicence = "";
    },
    handleAccountSuccess(res, file, fileList) {
      //此处写上传oss成功之后的逻辑
      if (res.code == 200) {
        this.form.accountLicence = res.data.url;
        this.form.accountLicenceName = res.data.name;
      }
    },
  },
};
</script>

<style lang="scss">
.app-container {
  background: #f4f5f9;
  .notice-record-detail {
    .info-container {
      width: 100%;
      padding-top: 12px;
      padding: 10px 30px;

      background-color: white;
      .header {
        margin-bottom: 30px;
        width: 100%;
        text-align: center;
        .el-button {
          height: 40px;
          border-color: transparent;
          padding: 10px 10px 10px 20px;
          font-size: 20px;
          color: #000;
        }
        .el-button:hover {
          background-color: white;
        }
        .header-text {
          font-size: 24px;
          font-weight: 500;
          color: #333333;
          line-height: 24px;
          line-height: 40px;
        }
      }
      .detail-page {
        position: relative;

        .header-small {
          text-align: center;
          display: flex;
          font-size: 16px;
          font-weight: 500;
          color: #333333;
          line-height: 16px;
          margin-bottom: 20px;

          .red-tag {
            margin-right: 12px;
            width: 3px;
            height: 16px;
            background: #21c9b8;
          }
        }
        .file-class {
          width: 733px;
          height: 40px;
          background: #f7f8fa;
          border-radius: 4px;
          padding: 0 20px;
          display: flex;
          font-size: 14px;
          font-weight: 400;
          color: #333333;
          line-height: 40px;
          position: relative;

          .el-image {
            margin: 12px 8px 0 0;
          }
          .previwe-class {
            right: 20px;
            position: absolute;
            margin: 8px 0 0 0;
            width: 72px;
            height: 24px;
            border-radius: 16px;
            text-align: center;
            border: 1px solid #2f76e0;
            font-size: 12px;
            font-weight: 400;
            color: #2f76e0;
            line-height: 24px;
          }
        }
        .status_approving {
          top: 0px;
          right: 20px;
          position: absolute;
        }
      }

      .edit-page {
        .el-input--medium .el-input__inner {
          width: 90%;
          height: 36px;
          line-height: 36px;
        }
        .el-button--primary {
          background: #fff;
          color: #333;
          border-color: #bfbfbf;
        }
        .el-button--danger {
          background: #fff;
          color: #21c9b8;
          border-color: #21c9b8;
        }
        .tip {
          padding-left: 10px;
          font-size: 12px;
          font-weight: 400;
          color: #8c8c8c;
          line-height: 18px;
        }
      }
      .el-descriptions--medium.is-bordered .el-descriptions-item__cell {
        padding: 10px;
        font-size: 14px;
        font-weight: 400;
        color: #333333;
      }

      .el-descriptions--medium.is-bordered .el-descriptions-item__label {
        padding: 15px;
        text-align: center;
        font-size: 14px;
        font-weight: 500;
        color: #333333;
        width: 200px;
      }
      .delete-btn {
        width: 100%;
        margin-top: 20px;
        text-align: center;
        .el-button {
          padding: 12px 55px;
          color: #333;
        }
        // .el-button:hover,
        // .el-button:focus {
        //   border-color: #d9d9d9;
        //   background-color: transparent;
        // }
        .el-button--danger {
          margin-left: 30px;
          color: #ffffff;
          background-color: #21c9b8;
          border-color: #21c9b8;
        }
        .el-button--error {
          margin-left: 30px;
          color: #21c9b8;
          background-color: #ffffff;
          border-color: #21c9b8;
        }
      }
    }
  }
}
</style>

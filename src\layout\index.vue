<template>
  <div :class="classObj" class="app-wrapper" :style="{'--current-color': theme}">
    <div class="main-container">
      <div class="fixed-header">
        <navbar />
      </div>
      <app-main />
      <footer-bar />
    </div>
    <back-topper />
  </div>
</template>

<script>
import RightPanel from '@/components/RightPanel'
import BackTopper from '@/components/BackTopper'
import { AppMain, Navbar, FooterBar, Settings, Sidebar, TagsView } from './components'
import ResizeMixin from './mixin/ResizeHandler'
import { mapState } from 'vuex'
import variables from '@/assets/styles/variables.scss'

export default {
  name: 'Layout',
  components: {
    AppMain,
    Navbar,
    FooterBar,
    RightPanel,
    BackTopper,
    Settings,
    Sidebar,
    TagsView
  },
  mixins: [ResizeMixin],
  computed: {
    ...mapState({
      theme: state => state.settings.theme,
      sideTheme: state => state.settings.sideTheme,
      sidebar: state => state.app.sidebar,
      device: state => state.app.device,
      needTagsView: state => state.settings.tagsView,
      fixedHeader: state => state.settings.fixedHeader
    }),
    classObj() {
      return {
        withoutAnimation: this.sidebar.withoutAnimation,
        mobile: this.device === 'mobile'
      }
    },
    variables() {
      return variables;
    }
  },
  methods: {
    handleClickOutside() {
      this.$store.dispatch('app/closeSideBar', { withoutAnimation: false })
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/assets/styles/mixin.scss";
  @import "~@/assets/styles/variables.scss";

  .app-wrapper {
    @include clearfix;
    position: relative;
    height: 100%;
    width: 100%;

    &.mobile.openSidebar {
      position: fixed;
      top: 0;
    }
  }

  .drawer-bg {
    background: #000;
    opacity: 0.3;
    width: 100%;
    top: 0;
    height: 100%;
    position: absolute;
    z-index: 999;
  }

  .fixed-header {
    position: fixed;
    top: 0;
    right: 0;
    z-index: 99;
    width: 100%;
  }

  .mobile .fixed-header {
    width: 100%;
  }

  .top-el {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: row;
    flex-shrink: 0;
    width: 40px;
    height: 40px;
    border-radius: 100%;
    background-color: #FFFFFF;
    i {
      font-size: 18px;
      font-weight: bold;
    }
  }
</style>

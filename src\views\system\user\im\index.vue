<!--
 * @Author: zhc
 * @Date: 2023-02-13 19:00:09
 * @LastEditTime: 2023-06-06 16:48:26
 * @Description:
 * @LastEditors: zhc
-->
<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="2.5">
        <user-menu />
      </el-col>
      <el-col :span="2.5">
        <div class="conversation-list">
          <conversation-list ref="conversationList" base-size="6.5px" />
        </div>
      </el-col>

      <el-col :span="17" :xl="17" :lg="15" :md="12">
        <div class="user-message-container">
          <div class="message-box">
            <message-list
              class="message-list"
              ref="messageList"
              base-size="6.5px"
            ></message-list>
          </div>
          <div class="editor-box">
            <message-editor base-size="6.5px" />
          </div>
          <!-- <div class="none-class">
            <el-image
              style="width: 160px; height: 160px"
              :src="require('@/assets/user/none.png')"
              fit="cover"
            ></el-image>
            <div class="text">暂无数据</div>
          </div> -->
        </div>
      </el-col>
    </el-row>
    <el-dialog
      :visible.sync="videoDialogVisible"
      :v-if="videoDialogVisible"
      width="60%"
      style="height: 650px"
      :before-close="handleVideoDialogClose"
    >
      <Videoplayer :mp4Url="videoUrl"></Videoplayer>
    </el-dialog>
    <el-dialog
      :visible.sync="imageDialogVisible"
      :v-if="imageDialogVisible"
      width="60%"
      style="height: 850px; text-align: center"
      :before-close="handleImageDialogClose"
    >
      <el-image
        :v-if="imageDialogVisible"
        :src="imageUrl"
        style="width: 600px; height: 100%"
      ></el-image>
    </el-dialog>
  </div>
</template>

<script>
import UserMenu from "../components/userMenu.vue";
import FileSaver from "file-saver";
import * as RongIMLib from "@rongcloud/imlib-next";
import {
  imkit,
  CoreEvent,
  DisabledMessageContextMenu,
  DisabledConversationontextMenu,
} from "@rongcloud/imkit";
import store from "@/store";
import { getUserIMToken } from "@/api/system/user";
import { ConversationType } from "@rongcloud/engine";
import Videoplayer from "./components/video";

export default {
  name: "IM",
  components: { UserMenu, Videoplayer },

  data() {
    return {
      user: {
        id: store.getters.userId,
      },
      editorVisible: false,
      videoDialogVisible: false,
      imageDialogVisible: false,
      videoUrl: "",
      lang: "",
      langArr: [
        {
          lang: "zh_CN",
          value: "中文",
        },
        {
          lang: "en",
          value: "英文",
        },
      ],
      conversationMenuList: [
        {
          value: DisabledConversationontextMenu.Top,
          name: "置顶",
        },
        {
          value: DisabledConversationontextMenu.Delete,
          name: "删除",
        },
        {
          value: DisabledConversationontextMenu.Notification,
          name: "免打扰",
        },
      ],
      disableMenuMessage: [],
      disableMenuConversation: [],
      messageMenuList: [
        {
          value: DisabledMessageContextMenu.Forward,
          name: "转发",
        },
        {
          value: DisabledMessageContextMenu.Delete,
          name: "删除",
        },
        {
          value: DisabledMessageContextMenu.Reference,
          name: "引用",
        },
        {
          value: DisabledMessageContextMenu.Recall,
          name: "撤回",
        },
        {
          value: DisabledMessageContextMenu.Copy,
          name: "复制",
        },
      ],
      imageUrl: "",
      imageUrl: "",
      showImage: false,
      switchConversationList: {},
      conversation: null,
      showMessageMenu: false,
      showConversationMenu: false,
    };
  },
  created() {
    ///获取用户token
    if (this.user.id) {
      getUserIMToken({ userId: this.user.id }).then((res) => {
        if (res.code === 200 && res.data.code === 200) {
          window.token = res.data.token;
          RongIMLib.connect(token).then((res) => {
            console.info("连接结果打印1：", res);
            window.imkit = imkit;
            this.lang = imkit.lang;
            // 加载会话列表
            imkit.emit(CoreEvent.CONVERSATION, true);
            if (this.$route.query.userId) {
              imkit.selectConversation({
                conversationType: ConversationType.PRIVATE, // 会话类型
                targetId: this.$route.query.userId,
              });
            }
          });
        }
      });
    }
  },
  mounted() {
    // defineCustomElements();
    const conversationList = this.$refs.conversationList;
    const messageList = this.$refs.messageList;
    //添加点击会话监听
    conversationList.addEventListener(
      "tapConversation",
      this.handleTapConversation //回调处理函数
    );
    console.log("conversationList", conversationList);
    //添加删除会话监听
    conversationList.addEventListener(
      "deleteConversation",
      this.handleDeleteConversation //回调处理函数
    );
    const disableMenu = [DisabledMessageContextMenu.Reference];
    messageList.disableMenu = disableMenu;
    //添加点击消息触发监听
    messageList.addEventListener("tapMessage", this.handleTapMessage);
  },
  beforeUnmount() {
    // 注意：需要 removeEventListener 防止多次绑定造成异常
    const conversationList = this.$refs.conversationList;

    conversationList.removeEventListener(
      "tapConversation",
      this.handleTapConversation
    );

    conversationList.removeEventListener(
      "deleteConversation",
      this.handleDeleteConversation
    );
  },
  methods: {
    handleVideoDialogClose() {
      this.videoDialogVisible = false;
    },
    handleImageDialogClose() {
      this.imageDialogVisible = false;
    },
    handleTapConversation() {
      //处理点击会话后的操作
      console.info("处理点击会话后的操作11111");
    },
    handleDeleteConversation() {
      //处理删除会话后的操作
      console.info("处理点击会话后的操作");
    },
    handleTapMessage(e) {
      const data = e.detail;
      // 处理点击查看大图或文件消息下载等功能
      console.log("点击消息触发监听:", data);
      if (data.type == "file") {
        let url = data.url.replace(
          "http://rongcloud-file.ronghub.com",
          "https://cy.ningmengdou.com/ryim"
        );
        FileSaver.saveAs(url, data.name);
      } else if (data.type == "sight") {
        this.videoUrl = data.url;
        this.videoDialogVisible = true;
      } else if (data.type == "image") {
        this.imageUrl = data.url;
        this.imageDialogVisible = true;
      }
    },
  },
};
</script>

<style lang="scss">
.app-container {
  background: #f4f5f9;

  .conversation-list {
    height: 700px;
    width: 300px;
  }
  .user-message-container {
    height: 700px;
    background: #fff;
    .none-class {
      text-align: center;
      padding: 20% 0;
      height: 700px;

      .text {
        font-size: 14px;
        font-weight: 400;
        color: #999999;
        line-height: 14px;
      }
    }
    .message-box {
      height: 400px;
      .message-list {
      }
      .no-data {
      }
    }

    .editor-box {
      height: 300px;
      .editor-tool-bar {
        height: 9em;
      }
      .editor-content-wrapper {
        padding: 1em 4em;
      }
    }
  }
}
</style>
<style lang="scss" scoped>
.app-container {
  .user-message-container {
    .message-box {
    }
  }
}
</style>

<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="2.5" :xs="24">
        <user-menu activeIndex="1" />
      </el-col>
      <el-col :span="20" :xs="24" style="width: calc(100% - 180px)">
        <div class="main-content">
          <el-row :gutter="30">
            <el-col :span="5" style="opacity: 0;">marginleft</el-col>
            <el-col :span="8">
              <el-form :model="userData" :rules="rules" ref="userData" label-width="80px">
                <el-form-item label="头像" prop="avatar">
                  <ImageUpload v-model="avatarList" :limit="1" />
                </el-form-item>
                <el-form-item label="真实姓名" prop="memberRealName">
                  <el-input v-model="userData.memberRealName" placeholder="请输入真实姓名" />
                </el-form-item>
                <el-form-item label="联系方式" prop="memberPhone">
                  <el-input v-model="userData.memberPhone" placeholder="请输入联系方式" />
                </el-form-item>
                <el-form-item label="微信号" prop="memberWechat">
                  <el-input v-model="userData.memberWechat" placeholder="请输入微信号" />
                </el-form-item>
                <el-form-item label="所在行业" prop="solutionTypeId">
                  <el-select v-model="userData.solutionTypeId" placeholder="请选择" class="form-select">
                    <el-option v-for="item in solutionTypeList" :key="item.solutionTypeId"
                      :label="item.solutionTypeName" :value="item.solutionTypeId">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="职位" prop="memberPost">
                  <el-select v-model="userData.memberPost" placeholder="请选择" class="form-select">
                    <el-option v-for="item in memberPostList" :key="item.dictValue" :label="item.dictLabel"
                      :value="item.dictValue">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="企业名称" prop="memberCompanyName">
                  <el-autocomplete v-model="userData.memberCompanyName" placeholder="请输入您公司的完整名称"
                    :fetch-suggestions="querySearchTianYanCha" @select="selectAutoDataTianYanCha"
                    style="width: 84%"></el-autocomplete>
                </el-form-item>
                <el-form-item label="企业地址" prop="memberCompanyArea">
                  <el-cascader clearable label="title" value="id" :options="areaList"
                    v-model="userData.memberCompanyArea" :props="props" placeholder="请选择地区"
                    class="form-select"></el-cascader>

                </el-form-item>
                <el-form-item label="企业规模" prop="companyScale">
                  <el-select v-model="userData.companyScale" placeholder="请选择" class="form-select">
                    <el-option v-for="item in companyScaleList" :key="item.dictValue" :label="item.dictLabel"
                      :value="item.dictValue">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-form>
              <div class="btn-box">
                <el-button class="btn" type="primary" @click="submitForm">保存</el-button>
              </div>
            </el-col>
            <el-col :span="4">
              <!-- <div class="card" v-if="!talentInfo">
                <div class="card-title">人才库</div>
                <div class="card-content">您暂未入驻!</div>
                <div class="card-content">入驻后可获取需求企业对接</div>
                <div class="btn-card">
                  <el-button class="btn" type="primary" @click="gotoTalentJoinNow('')">立即入驻</el-button>
                </div>
              </div>
              <div class="card" v-if="talentInfo">
                <div class="card-title">人才库</div>
                <div class="card-content success">您已经入驻!</div>
                <div class="card-content">点击下方按钮可预览，</div>
                <div class="card-content">如修改内容需重新审核。</div>
                <div class="btn-card">
                  <el-button class="btn" type="primary" @click="gotoTalentJoinNow(talentInfo)">查看信息</el-button>
                </div>
              </div> -->
            </el-col>
          </el-row>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import UserMenu from "../components/userMenu.vue";
import { industryList, getAreaData, userInfoSave } from "@/api/system/user";
import { searchCompany, getCompanyCodeByName } from "@/api/system/company";
import { listData } from "@/api/system/dict/data";
import { getInfo } from "@/api/login";

export default {
  name: "User",
  components: { UserMenu },
  data() {
    return {
      userData: {},
      settled: false,
      locationhost: '',
      showLogin: false,
      userinfo: {},
      token: '',
      userType: '',
      id: '',
      detail: {},
      setting: false,
      cooperationData: [],
      demandData: [],
      showSuccess: false,
      jbzldata: true,
      qyzhdata: false,
      imgLen: 0,
      showPop1: false,
      showPop2: false,
      areaList: [],
      props: {
        label: 'title',
        value: 'id',
        children: 'children',
        multiple: false
      },
      companyScaleList: [], // 企业规模
      memberPostList: [], // 职位
      solutionTypeList: [], // 行业
      rules: {
        memberPhone: [
          { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
        ],
      },
      talentInfo: {},
      avatarList: '',
    };
  },
  created() {
    // if (window.sessionStorage.getItem('userinfo') != 'null' && window.sessionStorage.getItem('userinfo') != 'undefined' && window.sessionStorage.getItem('userinfo')) {
    //   this.userinfo = JSON.parse(window.sessionStorage.getItem('userinfo'))
    //   let info = this.userinfo
    //   info.solutionTypeId = info.solutionTypeId ? info.solutionTypeId.toString() : ''
    //   info.memberCompanyArea = info.memberCompanyArea ? info.memberCompanyArea.split(',') : []
    //   this.userData = info
    //   this.avatarList = this.userData.avatar ? [this.userData.avatar] : []
    // } else {
    // }
    this.getInfo()
    this.getSolutionType()
    this.getMemberPost()
    this.getCity()
    this.getCompanyScale()
  },
  methods: {
    // 获取用户信息
    getInfo() {
      getInfo().then((res) => {
        if (res.code == 200) {
          this.userData = res.member || {};
          this.userData.solutionTypeId = this.userData.solutionTypeId ? this.userData.solutionTypeId.toString() : ''
          this.userData.memberCompanyArea = this.userData.memberCompanyArea ? this.userData.memberCompanyArea.split(',') : []
          this.avatarList = this.userData.avatar ? this.userData.avatar : ''
          this.talentInfo = res.talentInfo || {};
        }
      });
    },
    submitForm() {
      this.$refs['userData'].validate((valid) => {
        if (valid) {
          let params = {
            ...this.userData,
            memberCompanyArea: this.userData.memberCompanyArea ? this.userData.memberCompanyArea.join(',') : '',
            avatar: this.avatarList ? this.avatarList : ''
          }
          userInfoSave(params).then(res => {
            if (res.code && res.code == 200) {
              this.$message({
                showClose: true,
                message: '保存成功',
                type: 'success'
              });
              window.sessionStorage.setItem('userinfo', JSON.stringify(res.member))
              window.sessionStorage.setItem('userName', res.member.memberPhone);
              window.sessionStorage.setItem('userId', res.member.memberId);
              window.history.go(-1)
            } else {
              this.$message({
                showClose: true,
                message: '保存失败',
                type: 'error'
              });
            }
          });
        }
      });


    },
    // 行业
    getSolutionType() {
      industryList({}).then(res => {
        if (res.code == 200) {
          this.solutionTypeList = res.rows
        }
      })
    },
    // 职位
    getMemberPost() {
      let params = { dictType: "member_post" };
      listData(params).then((response) => {
        if (response.code == 200) {
          this.memberPostList = response.rows;
        }
      });
    },
    // 企业名称
    querySearchTianYanCha(queryString, cb) {
      if (queryString) {
        searchCompany({ keywords: queryString }).then(res => {
          let data = res.rows;
          let List = [];
          data.forEach(function (val, index) {
            List.push({
              id: index,
              value: val
            })
          })
          if (data.length > 0) {
            cb(List);
          } else {
            cb([{
              id: '',
              value: '暂无数据'
            }]);
          }
        });
      }
    },
    // 企业名称选择
    selectAutoDataTianYanCha(row) {
      getCompanyCodeByName({ keywords: row.value }).then(res => {
        if (res.code == 200) {
          let data = res.data;
          this.$set(this.userData, 'socialUnityCreditCode', data.taxNo)
        }
      });
    },
    // 企业地址
    getCity() {
      let cityList = window.sessionStorage.getItem('cityList');
      if (!cityList) {
        getAreaData().then(res => {
          if (res.code == 200) {
            let areaList = res.rows
            areaList.forEach(item => {
              item.children.forEach(item1 => {
                item1.children.forEach(item2 => {
                  delete item2.children
                })
              })
            })
            window.sessionStorage.setItem('cityList', JSON.stringify(areaList));
            this.areaList = areaList;
          }
        })
      } else {
        this.areaList = JSON.parse(JSON.parse(JSON.stringify(cityList)))
      }
      // console.log( this.areaList)
    },
    // 职位
    getCompanyScale() {
      let params = { dictType: "company_scale" };
      listData(params).then((response) => {
        if (response.code == 200) {
          this.companyScaleList = response.rows;
        }
      });
    },
    gotoTalentJoinNow(info) {
      if (info) {
        this.$router.push({ path: '/user/talentDetail', query: { id: info.id } })
      } else {
        this.$router.push({ path: '/user/talentJoinNow' })
      }
    }
  },
};
</script>
<style lang="scss" scoped>
.app-container {
  background: linear-gradient(to right, rgb(225, 247, 240), rgb(244, 252, 250));
  height: 100vh;
}

.main-content {
  background-color: #fff;
  padding: 20px;
  padding-bottom: 100px;

  .btn-box {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 40px;

    .btn {
      width: 200px;
      height: 50px;
    }
  }

  .card {
    margin-top: 170px;
    padding: 10px;
    box-sizing: border-box;
    width: 280px;
    height: 240px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: url("../../../../assets/userCenter/card_bg.png") no-repeat;
    background-size: 100% 100%;


    .card-title {
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 10px;
      color: #030A1A;
    }

    .card-content {
      font-size: 14px;
      color: #666666;
      line-height: 30px;
    }

    .success {
      color: #21C9B8;
    }

    .btn-card {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 0px;

      .btn {
        width: 200px;
        height: 50px;
      }
    }
  }

}
</style>

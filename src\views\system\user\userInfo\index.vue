<!--
 * @Author: zhc
 * @Date: 2023-02-03 11:06:49
 * @LastEditTime: 2023-03-24 09:06:27
 * @Description: 
 * @LastEditors: zhc
-->
<template>
  <div class="user-info-page">
    <div class="app-container">
      <el-row :gutter="20">
        <el-col :span="2.5" :xs="24">
          <user-menu activeIndex="1" />
        </el-col>
        <el-col :span="20" :xs="24">
          <div class="user-info-container">
            <div class="header-small">
              <div class="red-tag"></div>
              基本信息
            </div>
            <div class="user-info-card">
              <el-button
                class="edit-button"
                icon="el-icon-edit"
                @click="openEditDialog"
                >点击修改</el-button
              >
              <el-image
                class="user-info-avatar"
                :src="user.avatar"
                :fit="fit"
              ></el-image>

              <div class="user-name">{{ user.realName || "--" }}</div>
              <div class="phone-class">
                <el-image
                  style="vertical-align: top"
                  :src="require('@/assets/user/phone.png')"
                ></el-image>
                <span class="phone-number">{{ user.phonenumber }}</span>
              </div>
              <div class="info-box">
                <el-form ref="form" :model="user" label-width="80px">
                  <el-form-item label="职务:">
                    {{ user.identityType }}
                  </el-form-item>
                  <el-form-item label="邮箱:">
                    <span>{{ user.email }}</span>
                  </el-form-item>
                  <el-form-item label="公司:">
                    {{ user.companyName }}
                  </el-form-item>
                  <!-- <el-form-item label="身份认证:">
                    <div class="tag-group">
                      <a
                        class="label-container orange"
                        v-if="this.companyStatus == '1'"
                        @click="jumpToApprove"
                      >
                        <el-image
                          style="width: 12px; height: 12px"
                          :src="
                            require('@/assets/user/authentication_orange.png')
                          "
                        ></el-image>
                        <span>企业认证</span>
                      </a>
                      <a
                        class="label-container red"
                        v-if="personalStatus == '1'"
                        @click="jumpToApprove"
                      >
                        <el-image
                          class="red"
                          style="width: 12px; height: 12px"
                          :src="require('@/assets/user/authentication_red.png')"
                        ></el-image>
                        <span>名片认证</span>
                      </a>
                      <a
                        v-if="
                          this.personalStatus == '0' &&
                          this.companyStatus == '0'
                        "
                        class="label-container"
                        @click="jumpToApprove"
                      >
                        <el-image
                          style="width: 12px; height: 12px"
                          :src="require('@/assets/user/authentication.png')"
                        ></el-image>
                        <span>未认证</span>
                      </a>
                    </div>
                  </el-form-item> -->
                </el-form>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>

      <!-- 添加或修改部门对话框 -->
      <el-dialog
        title="个人资料修改"
        :visible.sync="editDialog"
        width="750px"
        append-to-body
      >
        <el-form ref="form" :model="form" :rules="rules" label-width="80px">
          <el-row>
            <el-col :span="6">
              <el-form-item label-width="0px">
                <el-upload
                  class="avatar-uploader"
                  :headers="headers"
                  :action="actionUrl"
                  accept=".png, .jpg "
                  :show-file-list="false"
                  :on-success="handleUploadSuccess"
                  :limit="1"
                >
                  <el-image
                    style="width: 100px; height: 100px; border-radius: 50%"
                    :fit="fit"
                    :src="form.avatar"
                  ></el-image>
                  <el-button size="small" type="primary">点击上传</el-button>
                </el-upload>
              </el-form-item>
            </el-col>
            <el-col :span="18">
              <el-row>
                <el-col :span="24">
                  <el-form-item label="姓名" prop="realName">
                    <el-input
                      v-model="form.realName"
                      placeholder="请输入姓名"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="联系方式" prop="phonenumber">
                    <el-input
                      v-model="form.phonenumber"
                      placeholder="请输入联系电话"
                      disabled
                      maxlength="11"
                    />
                    <a
                      class="action-class"
                      style="margin-left: 6px"
                      :underline="false"
                      @click="openPhoneDialog"
                      >变更</a
                    >
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="微信号" prop="weixin">
                    <el-input
                      v-model="form.weixin"
                      placeholder="请输入微信号"
                      maxlength="50"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="邮箱" prop="email">
                    <el-input
                      v-model="form.email"
                      placeholder="请输入邮箱"
                      maxlength="50"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="我的公司" prop="companyName">
                    <el-row>
                      <el-input
                        v-model="form.companyName"
                        placeholder="请选择公司"
                        disabled
                        maxlength="50"
                      />
                      <a
                        class="action-class"
                        style="margin-left: 6px"
                        :underline="false"
                        @click="openComapnyDialog"
                        >变更</a
                      >
                    </el-row>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="职务" prop="identityType">
                    <el-row>
                      <el-input
                        v-model="form.identityType"
                        placeholder="请选择职务"
                        disabled
                        maxlength="50"
                      />
                      <a
                        class="action-class"
                        style="margin-left: 6px"
                        :underline="false"
                        @click="openPositionDialog"
                        >变更</a
                      >
                    </el-row>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="cancelEditDialog">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </el-dialog>
      <!-- 手机号更改绑定 -->
      <el-dialog
        title="修改手机号"
        :visible.sync="phoneDialog"
        width="450px"
        append-to-body
      >
        <el-form
          ref="transForm"
          :model="transForm"
          :rules="transRules"
          class="trans-form"
        >
          <div class="header">请输入验证码，确定用户身份</div>
          <el-form-item prop="userName" label="手机号：">
            {{ transForm.username }}
          </el-form-item>
          <el-form-item prop="smsCode" label="验证码：">
            <el-input
              v-model="transForm.smsCode"
              auto-complete="off"
              placeholder="请输入短信验证码"
              :maxlength="6"
              style="width: 80%"
            >
              <template slot="suffix">
                <a @click="getSmsCode" class="active-style" v-if="!showCode"
                  >获取验证码</a
                >
                <span class="disabled-style" v-else
                  >{{ countMiao }}秒</span
                ></template
              >
            </el-input>
            <div class="login-code"></div>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="cancelPhoneDialog">取 消</el-button>
          <el-button type="primary" @click="changePhone">确 定</el-button>
        </div>
      </el-dialog>
      <!-- 确认手机号更改绑定 -->
      <el-dialog
        title="修改手机号"
        :visible.sync="phoneConfirmDialog"
        width="450px"
        append-to-body
      >
        <el-form
          ref="transForm"
          :model="transForm"
          :rules="transRules"
          class="trans-form"
        >
          <div class="header">请输入新用户手机号，获取验证码，完成变更</div>
          <el-form-item prop="userName" label="手机号：">
            <el-input
              v-model="transForm.confirmName"
              placeholder="请输入手机号"
              :maxlength="11"
              style="width: 80%"
            />
          </el-form-item>
          <el-form-item prop="smsCode" label="验证码：">
            <el-input
              v-model="transForm.smsConfirmCode"
              auto-complete="off"
              placeholder="请输入短信验证码"
              :maxlength="6"
              style="width: 80%"
            >
              <template slot="suffix">
                <a
                  @click="getConfirmSmsCode"
                  class="active-style"
                  v-if="!showConfirmCode"
                  >获取验证码</a
                >
                <span class="disabled-style" v-else
                  >{{ countConfirmMiao }}秒</span
                ></template
              >
            </el-input>
            <div class="login-code"></div>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="cancelConfirmDialog">取 消</el-button>
          <el-button type="primary" @click="changeConfirmPhone"
            >确 定</el-button
          >
        </div>
      </el-dialog>
      <el-dialog
        title="关联企业"
        :visible.sync="companyDialog"
        width="550px"
        append-to-body
      >
        <el-form ref="form" :model="form" :rules="rules" label-width="80px">
          <el-form-item label="企业名称">
            <el-select
              v-model="companyId"
              filterable
              remote
              reserve-keyword
              placeholder="请输入关键词"
              :remote-method="getCompanyList"
              :loading="companyLoading"
              @change="companyChanged"
              style="width: 100%"
            >
              <el-option
                v-for="item in companyOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="cancelComapnyDialog">取 消</el-button>
          <el-button type="primary" @click="transferCompany">确 定</el-button>
        </div>
      </el-dialog>
      <el-dialog
        title="修改职务"
        :visible.sync="positionDialog"
        width="800px"
        append-to-body
      >
        <el-row>
          <el-col :span="5">
            <div class="left-container">
              <div
                v-for="(firstData, index) in positionFirstData"
                v-bind:key="firstData.postName"
                :class="
                  index === activeFirstIndex
                    ? 'position-header selected-style'
                    : 'position-header'
                "
              >
                <a @click="selectFisrt(index, firstData)">
                  {{ firstData.postName || "" }}
                </a>
              </div>
            </div>
          </el-col>
          <el-col :span="19">
            <div class="right-container">
              <div
                v-for="childData in positionChildData"
                v-bind:key="childData.postId"
              >
                <div>
                  <div class="second-header">
                    {{ childData.postName || "--" }}
                  </div>
                  <div class="position-container">
                    <a
                      v-for="pos in childData.children"
                      v-bind:key="pos.postId"
                      :class="
                        selectPositionData.postId == pos.postId
                          ? 'position-tag selected'
                          : 'position-tag'
                      "
                      @click="selectPosition(pos)"
                    >
                      {{ pos.postName || "--" }}
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
        <div slot="footer" class="dialog-footer">
          <el-button @click="cancelPositionDialog">取 消</el-button>
          <el-button type="primary" @click="changePosition">确 定</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import store from "@/store";
import UserMenu from "../components/userMenu.vue";
import userAvatar from "../profile/userAvatar";
import userInfo from "../profile/userInfo";
import { uploadUrl } from "@/api/oss";
import { getToken } from "@/utils/auth";
import {
  getUserInfo,
  updateUserInfo,
  checkAuthStatus,
  getCompanyListByName,
  transferCompany,
  getPositionData,
  oldPhone,
  newPhone,
} from "@/api/system/user";
import { getCommonCode } from "@/api/login";

export default {
  name: "UserInfo",
  components: { userAvatar, userInfo, UserMenu },
  data() {
    return {
      fit: "cover",
      // 是否显示弹出层
      editDialog: false,
      companyDialog: false,
      positionDialog: false,
      actionUrl: uploadUrl(),
      headers: { Authorization: "Bearer " + getToken() },
      userId: store.getters.userId,
      user: {
        name: store.getters.name,
        avatar: store.getters.avatar,
        id: store.getters.userId,
      },
      form: {},
      rules: {},
      companyOptions: [],
      positionOptions: [],
      companyLoading: false,
      companyId: "",
      companyName: "",
      companyCode: "",
      personalStatus: "",
      companyStatus: "",
      positionFirstData: [],
      activeFirstIndex: 0,
      positionChildData: [],
      selectPositionData: {},
      identityType: "",
      phoneDialog: false,
      transForm: {
        username: "",
        smsCode: "",
        confirmName: "",
        smsConfirmCode: "",
      },
      transRules: {},
      countMiao: 60,
      showCode: false,
      showConfirmCode: false,
      countConfirmMiao: 60,
      phoneConfirmDialog: false,
    };
  },
  created() {
    this.getUser();
    this.getPositionData();
    this.isRelevanceCompany();
  },
  methods: {
    openPhoneDialog() {
      this.transForm.username = this.user.phonenumber;
      this.phoneDialog = true;
    },
    isRelevanceCompany() {
      let flag = this.$route.query.relevanceCompany;
      if (flag == "1") {
        this.openComapnyDialog();
      }
    },
    cancelEditDialog() {
      this.editDialog = false;
      this.form = {};
    },
    openEditDialog() {
      this.form = { ...this.user };
      this.editDialog = true;
    },
    cancelComapnyDialog() {
      this.companyDialog = false;
      this.companyCode = "";
      this.companyId = "";
    },
    openComapnyDialog() {
      this.form = { ...this.user };
      this.companyDialog = true;
    },
    cancelPositionDialog() {
      this.selectPositionData = {};
      this.identityType = "";
      this.positionDialog = false;
    },
    openPositionDialog() {
      this.selectPositionData = {};
      this.identityType = "";
      this.positionDialog = true;
    },
    submitCompanyForm() {},
    getUser() {
      getUserInfo(this.userId).then((response) => {
        this.user = response.data;
        // this.roleGroup = response.roleGroup;
        // this.postGroup = response.postGroup;
      });
      // checkAuthStatus().then((response) => {
      //   this.personalStatus = response.data.personalStatus;
      //   this.companyStatus = response.data.companyStatus;
      // });
    },
    getPositionData() {
      getPositionData().then((response) => {
        response.data.forEach((item) => {
          this.positionFirstData.push(item);
        });
        this.positionChildData = response.data[0].children;
      });
    },
    selectFisrt(index, data) {
      this.activeFirstIndex = index;
      this.positionChildData = data.children;
    },
    selectPosition(position) {
      this.selectPositionData = position;
      this.identityType = position.postName;
    },
    companyChanged(res) {
      this.companyOptions.forEach((item) => {
        if (item.id == res) {
          this.companyCode = item.creditCode;
          this.companyId = item.id;
          this.companyName = item.name;
        }
      });
    },
    changePosition() {
      if (this.identityType) {
        this.form.identityType = this.identityType;
        this.cancelPositionDialog();
      }
    },
    getCompanyList(query) {
      if (query !== "") {
        getCompanyListByName(query).then((response) => {
          this.companyOptions = response.data;
        });
      }
    },
    transferCompany() {
      transferCompany({
        tianyanId: this.companyId,
        businessNo: this.companyCode,
      }).then((res) => {
        if (res.code == 200) {
          this.cancelComapnyDialog();
          this.form.companyName = this.companyName;
          this.form.bussinessNo = this.companyCode;
          let flag = this.$route.query.relevanceCompany;
          if (flag == "1") {
            this.$modal.msgSuccess("操作成功");
            store.dispatch("GetInfo");
            this.getUser();
          }
        }
      });
    },
    submitForm() {
      this.form.userName = undefined;
      updateUserInfo(this.form).then((res) => {
        if (res.code == 200) {
          this.$modal.msgSuccess("操作成功");
          this.cancelEditDialog();
          store.dispatch("GetInfo");
          this.getUser();
        }
      });
    },
    handleUploadSuccess(res, file, fileList) {
      //此处写上传oss成功之后的逻辑
      if (res.code == 200) {
        this.form.avatar = res.data.url;
      }
    },
    reset() {},
    jumpToApprove() {
      this.$router.push("/user/approveSetting");
    },
    // 获取验证码
    getSmsCode() {
      if (!this.transForm.username) {
        this.$message({
          message: "请输入手机号",
          type: "warning",
        });
        return;
      }
      getCommonCode({ telphone: this.transForm.username }).then(() => {
        this.countTime();
      });
    },
    getConfirmSmsCode() {
      if (!this.transForm.confirmName) {
        this.$message({
          message: "请输入手机号",
          type: "warning",
        });
        return;
      }
      getCommonCode({ telphone: this.transForm.confirmName }).then(() => {
        this.countConfirmTime();
      });
    },
    // 验证码倒计时
    countTime() {
      this.countMiao = 60;
      this.showCode = true;
      var times = setInterval(() => {
        this.countMiao--; //递减
        if (this.countMiao < 0) {
          // <=0 变成获取按钮
          this.showCode = false;
          clearInterval(times);
        }
      }, 1000);
    },
    // 验证码倒计时
    countConfirmTime() {
      this.countConfirmMiao = 60;
      this.showConfirmCode = true;
      var times = setInterval(() => {
        this.countConfirmMiao--; //递减
        if (this.countConfirmMiao < 0) {
          // <=0 变成获取按钮
          this.showConfirmCode = false;
          clearInterval(times);
        }
      }, 1000);
    },
    cancelPhoneDialog() {
      this.phoneDialog = false;
    },
    changePhone() {
      if (!this.transForm.smsCode) {
        this.$message.warning("请输入验证码！");
      } else {
        let data = {
          username: this.transForm.username,
          smsCode: this.transForm.smsCode,
          userType: "01",
        };
        oldPhone(data).then((res) => {
          if (res.code === 200) {
            this.phoneDialog = false;
            this.phoneConfirmDialog = true;
          }
        });
      }
    },
    cancelConfirmDialog() {
      this.phoneConfirmDialog = false;
    },
    changeConfirmPhone() {
      if (!this.transForm.confirmName) {
        this.$message.warning("请输入手机号！");
      } else if (!this.transForm.smsConfirmCode) {
        this.$message.warning("请输入验证码！");
      } else {
        let data = {
          username: this.transForm.confirmName,
          smsCode: this.transForm.smsConfirmCode,
          userType: "01",
        };
        newPhone(data).then((res) => {
          if (res.code === 200) {
            this.phoneConfirmDialog = false;
            this.editDialog = false;
            this.$message.success("操作成功！");
            this.getUser();
          }
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.el-input {
  height: 36px;
  width: 380px;
  line-height: 36px;
}
.action-class {
  padding-left: 16px;
  color: #21c9b8;
}
.el-button--primary {
  color: #21c9b8;
  background-color: #ffffff;
  border-color: #21c9b8;
}
.trans-form {
  border-radius: 6px;
  background: #ffffff;
  width: 400px;
  padding: 25px 5px 5px 25px;
  .header {
    font-size: 18px;
    font-weight: 500;
    color: #121620;
    line-height: 18px;
    margin-bottom: 12px;
  }
  .el-input {
    height: 38px;
    input {
      height: 38px;
    }
  }
  .el-select {
    display: inline-block;
    position: relative;
    width: 100%;
  }
  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 2px;
  }
  .el-form-item--medium .el-form-item__content {
    display: flex;
    line-height: 36px;
  }
  .el-form-item {
    margin-bottom: 12px;
  }
  .el-input__suffix-inner {
    .active-style {
      color: #21c9b8;
      font-size: 14px;
    }
    .disabled-style {
      color: #999;
      font-size: 14px;
    }
  }
}
</style>
<style lang="scss">
.app-container {
  background: #f4f5f9;
  .user-info-container {
    background: #fff;
    height: calc(100vh - 150px);
    padding: 20px;
    position: relative;

    .edit-button {
      position: absolute;
      right: 10px;
      top: 90px;
      margin-left: 30px;
      color: #21c9b8;
      background-color: #ffffff;
      border-color: transparent;
    }
    .user-info-avatar {
      width: 120px;
      height: 120px;
      border-radius: 50%;
    }
    .header-small {
      text-align: center;
      display: flex;
      font-size: 20px;
      font-weight: 500;
      color: #333333;
      line-height: 20px;

      .red-tag {
        margin-right: 12px;
        width: 3px;
        height: 22px;
        background: #21c9b8;
      }
    }
    .user-info-card {
      margin-top: 80px;
      // margin-right: 80px;
      text-align: center;
      .user-name {
        margin-top: 10px;
        font-size: 22px;
        font-weight: 500;
        color: #333333;
        line-height: 24px;
      }
      .phone-class {
        width: 100%;
        height: 30px;
        margin-top: 12px;

        .el-image {
          width: 16px;
          height: 16px;
        }
        .phone-number {
          margin-left: 12px;
          font-size: 16px;
          font-weight: 400;
          color: #333333;
          line-height: 16px;
        }
      }
      .info-box {
        display: flex;
        justify-content: center;
        // margin: 24px auto 0 auto;
        .el-form-item {
          margin-bottom: 4px;
          text-align: start;
        }
        .tag-group {
          display: flex;
          .label-container {
            padding: 4px 12px;
            margin-top: 6px;
            margin-right: 6px;
            height: 24px;
            background: #f0f1f4;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            color: #8a8c94;
            line-height: 12px;
            .el-image {
              margin: 2px 4px 0 0;
            }
          }
          .orange {
            background: rgba(255, 191, 69, 0.2);
            color: #ff8a27;
          }
          .red {
            background: rgba(197, 37, 33, 0.1);
            color: #21c9b8;
          }
        }
      }
    }
    .position-header {
      color: red;
    }
  }
}

.left-container {
  height: 500px;
  margin-right: 10px;
  overflow-y: auto;
  text-align: center;
  .position-header {
    height: 50px;
    line-height: 50px;
    font-weight: 500;

    overflow-y: auto;
  }
  .selected-style {
    background: #21c9b8;
    color: #fff;
  }
}

.right-container {
  height: 500px;
  overflow-y: auto;

  .second-header {
    height: 65px;
    line-height: 65px;

    font-size: 18px;
    font-weight: 500;
  }
  .position-container {
    width: 70%;
    display: flex;
    flex-wrap: wrap;
    .position-tag {
      padding: 0 20px;
      font-size: 15px;
      line-height: 36px;
      color: #000;
    }
    .selected {
      background: #21c9b8 !important;
      color: #fff !important;
    }
    a:hover {
      cursor: pointer;
      color: #21c9b8;
      text-decoration: none;
    }
  }
}
</style>

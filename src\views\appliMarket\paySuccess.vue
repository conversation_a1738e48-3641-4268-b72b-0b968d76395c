<template>
  <div class="activity-container">
    <div class="order">
      <div v-show="payType == 1">
        <div class="step" style="padding-top: 80px">
          <img src="../../assets/images/success.png" />
        </div>
        <div class="success_tishi">
          您已成功支付<strong>￥{{ totalAmount }}</strong>
        </div>
      </div>
      <div v-show="payType == 2">
        <div class="step" style="padding-top: 80px">
          <img src="../../assets/images/success.png" />
        </div>
        <div style="text-align: center">
          <div style="font-size: 18px">支付信息提交成功，请等待确认后授权</div>
          <p>订单编号: 2023080368762797</p>
          <p>待确认支付金额: ¥9999.99</p>
          <p></p>
        </div>
      </div>
      <div class="success_tishi">
        您可以在个人中心
        <span @click="goSubscriptions" style="color: #21c9b8; cursor: pointer"
          >"我的订阅"</span
        >
        查看订单详情
        <!-- 查看订单，请到<a href="person/main.html">个人中心</a>我的订阅订单查询 -->
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      totalAmount: "",
      payType: 1,
    };
  },
  created() {
    this.totalAmount = this.$route.query.price;
  },
  methods: {
    goSubscriptions() {
      this.$router.push({
        path: "/user/mySubscriptions",
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.order {
  width: 1000px;
  margin-top: 0;
  margin-right: auto;
  margin-bottom: 0;
  margin-left: auto;
  padding-bottom: 50px;
  min-height: 500px;
  .step {
    text-align: center;
    padding-top: 20px;
    width: 100%;
    // padding-bottom: 20px;
  }
  .success_tishi {
    text-align: center;
    margin: 10px 0;
  }

  .success_tishi strong {
    color: #f00;
    font-size: 16px;
  }

  .success_tishi a {
    color: #de791b;
  }
}
</style>

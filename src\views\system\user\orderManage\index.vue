<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="2.5" :xs="24">
        <user-menu />
      </el-col>
      <el-col :span="20" :xs="24">
        <div class="content">
          <div class="content_type">
            <div class="title">订单管理</div>
          </div>
          <div class="tabStyle">
            <!-- <div
              v-for="item in orderTypeList"
              :key="item.value"
              class="buttonStyle"
              :class="flag == item.value ? 'buttonHover' : ''"
              @click="getFlag(item.value)"
            >
              {{ item.label }}
            </div> -->
            <!-- <div
              class="buttonStyle"
              :class="flag == 2 ? 'buttonHover' : ''"
              @click="getFlag(2)"
            >
              待付款
            </div>
            <div
              class="buttonStyle"
              :class="flag == 3 ? 'buttonHover' : ''"
              @click="getFlag(3)"
            >
              待发货
            </div>
            <div
              class="buttonStyle"
              :class="flag == 4 ? 'buttonHover' : ''"
              @click="getFlag(4)"
            >
              待收货
            </div>
            <div
              class="buttonStyle"
              :class="flag == 5 ? 'buttonHover' : ''"
              @click="getFlag(5)"
            >
              已完成
            </div> -->
          </div>
          <div style="margin-top: 20px">
            <el-form ref="form" :model="form" label-width="100px">
              <el-row>
                <el-col :span="8">
                  <el-form-item label="订单编号" prop="nickName">
                    <el-input v-model="form.id" placeholder="请输入" />
                  </el-form-item>
                </el-col>
                <!-- <el-col :span="6">
                  <el-form-item label="手机号" prop="userName">
                    <el-input v-model="form.phone" placeholder="请输入" />
                  </el-form-item>
                </el-col> -->
                <el-col :span="8">
                  <el-form-item label="应用名称" prop="userName">
                    <el-input v-model="form.appName" placeholder="请输入" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="订货编码" prop="userName">
                    <el-input v-model="form.orderCode" placeholder="请输入" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="订单状态" prop="userName">
                    <el-select
                      v-model="form.orderStatus"
                      placeholder="请选择"
                      clearable
                      style="width: 100%"
                    >
                      <el-option
                        v-for="dict in orderStatusList"
                        :key="dict.dictValue"
                        :label="dict.dictLabel"
                        :value="dict.dictValue"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="订单生成时间" prop="userName">
                    <el-date-picker
                      v-model="createTime"
                      type="daterange"
                      range-separator="至"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      value-format="yyyy-MM-dd"
                    >
                    </el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item>
                    <el-button
                      type="primary"
                      icon="el-icon-search"
                      size="mini"
                      @click="handleQuery"
                      >查询</el-button
                    >
                    <el-button
                      icon="el-icon-refresh"
                      size="mini"
                      @click="resetQuery"
                      >重置</el-button
                    >
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>
          <div class="tableStyle" v-loading="loading">
            <div class="everyItem" v-for="item in subscribeList" :key="item.id">
              <div class="orderNumTime">
                <div>订单编号: {{ item.id }}</div>
                <div style="margin-left: 5%">
                  下单时间: {{ item.createTime }}
                </div>
                <div style="margin-left: 5%">下单人: {{ item.nickName }}</div>
                <div style="margin-left: 5%">企业名称: {{ item.supply }}</div>
              </div>
              <div class="driver"></div>
              <div class="item_content">
                <div class="item_img">
                  <img :src="item.appLogo" alt="" />
                </div>
                <div class="item_desc">
                  <div class="title">{{ item.remark }}</div>
                  <!-- <div style="font-size: 14px; margin-top: 10px">
                    <span style="color: #999999">规格:</span>
                    <span style="margin-left: 5px">{{ item.spec }}</span>
                  </div> -->
                  <div style="font-size: 14px; margin-top: 10px">
                    <span style="color: #999999">可用时长:</span>
                    <span style="margin-left: 5px">{{
                      item.validTime == "1" ? "一年" : "永久"
                    }}</span>
                  </div>
                  <div style="font-size: 14px; margin-top: 10px">
                    <span style="color: #999999">可用人数:</span>
                    <span style="margin-left: 5px">{{ item.userNumber }}</span>
                  </div>
                </div>
                <div class="item_amounts">
                  <div style="color: #999999; font-size: 14px">订单金额</div>
                  <div style="margin-top: 10px; font-weight: 400">
                    ￥{{ item.price }}
                  </div>
                </div>
                <div class="driverVertical"></div>
                <div>
                  <div>{{ getStatus(item.orderStatus) }}</div>
                  <!-- <div
                    style="margin-top: 10px; color: #21C9B8; cursor: pointer"
                    @click="gotoDetail(item.id)"
                  >
                    订单详情
                  </div> -->
                </div>
                <div style="margin: 0 7%">
                  <div
                    style="color: #21c9b8; cursor: pointer"
                    @click="gotoDetail(item.id)"
                  >
                    订单详情
                  </div>
                  <!-- 待发货 -->
                  <div v-if="item.orderStatus == 2">
                    <div
                      @click="goShip(item.id)"
                      style="margin-top: 10px; color: #21c9b8; cursor: pointer"
                    >
                      去发货
                    </div>
                  </div>
                  <!-- 已发货 -->
                  <div v-if="item.orderStatus !== 2 && item.orderStatus !== 9">
                    <div
                      @click="invoicing(item.id)"
                      style="margin-top: 10px; color: #21c9b8; cursor: pointer"
                    >
                      {{ item.makeinvoice == 0 ? "开具发票" : "重新开票" }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div style="text-align: center; margin-top: 45px">
              <el-pagination
                v-show="total > 0"
                background
                layout="prev, pager, next"
                :page-size="5"
                :current-page.sync="queryParams.pageNum"
                @current-change="handleCurrentChange"
                :total="total"
              >
              </el-pagination>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
    <el-dialog
      title="开票信息"
      :visible.sync="invoiceVisible"
      width="750px"
      append-to-body
    >
      <el-form
        ref="ruleForm"
        :model="ruleForm"
        :rules="rules"
        label-width="80px"
      >
        <el-form-item label="发票类型:">
          {{ invoiceData.invoiceType == 1 ? "专票" : "普票" }}
        </el-form-item>
        <el-form-item label="公司名称:">
          {{ invoiceData.companyName }}
        </el-form-item>
        <el-form-item label="税号:">
          {{ invoiceData.dutyParagraph }}
        </el-form-item>
        <el-form-item label="公司地址:">
          {{ invoiceData.address }}
        </el-form-item>
        <el-form-item label="公司电话:">
          {{ invoiceData.phone }}
        </el-form-item>
        <el-form-item label="开户银行:">
          {{ invoiceData.openAccount }}
        </el-form-item>
        <el-form-item label="银行账号:">
          {{ invoiceData.bankAccount }}
        </el-form-item>
        <el-form-item label="邮箱地址:">
          {{ invoiceData.email }}
        </el-form-item>
        <el-form-item label="上传发票" prop="companyCardList">
          <el-upload
            :headers="headers"
            :action="actionUrl"
            accept=".pdf,.jpg,.png"
            :file-list="ruleForm.companyCardList"
            :before-upload="handleBeforeUpload"
            :on-remove="handleApplicationRemove"
            :on-success="handleApplicationSuccess"
            :on-exceed="handleExceedLicence"
            :on-preview="handlePreview"
            :limit="1"
          >
            <div>
              <el-button size="small" type="primary" icon="el-icon-upload2"
                >上传文件</el-button
              >
              <span style="margin-left: 10px">可上传pdf,jpg,png格式</span>
            </div>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelDialog">取 消</el-button>
        <el-button type="primary" @click="submitForm">发送发票</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  orderList,
  modifyStatus,
  invoiceList,
  sendInvoice,
} from "@/api/system/user";
import UserMenu from "../components/userMenu.vue";
import { uploadUrl } from "@/api/oss";
import { getToken } from "@/utils/auth";

export default {
  name: "Operlog",
  dicts: ["sys_oper_type", "sys_common_status"],
  components: { UserMenu },
  data() {
    var validIsEmptyArr = (s, value, callback) => {
      if (!Array.isArray(value) || value.length === 0) {
        callback(new Error("请上传文件"));
        return;
      }
      callback();
    };
    return {
      headers: { Authorization: "Bearer " + getToken() },
      actionUrl: uploadUrl(),
      form: {
        id: "",
        phone: "",
        appName: "",
        orderCode: "",
        orderStatus: "",
      },
      createTime: [],
      subscribeList: [],
      queryParams: {
        pageNum: 1,
      },
      total: 0,
      flag: 1,
      typeList: [
        {
          dictValue: 1,
          dictLabel: "工业应用",
        },
        {
          dictValue: 2,
          dictLabel: "工业模型",
        },
      ],
      orderStatusList: [
        {
          dictValue: 1,
          dictLabel: "待支付",
        },
        {
          dictValue: 2,
          dictLabel: "待发货",
        },
        {
          dictValue: 3,
          dictLabel: "支付失败",
        },
        {
          dictValue: 4,
          dictLabel: "已发货",
        },
        {
          dictValue: 5,
          dictLabel: "已成交",
        },
        {
          dictValue: 6,
          dictLabel: "待续费",
        },
        {
          dictValue: 7,
          dictLabel: "已关闭",
        },
        {
          dictValue: 8,
          dictLabel: "支付中",
        },
        {
          dictValue: 9,
          dictLabel: "已取消",
        },
      ],
      orderTypeList: [
        {
          value: "0",
          label: "全部",
        },
        {
          value: "1",
          label: "待付款",
        },
        {
          value: "2",
          label: "待发货",
        },
        {
          value: "3",
          label: "待收货",
        },
        {
          value: "4",
          label: "已完成",
        },
      ],
      flag: "0",
      loading: false,
      invoiceVisible: false,
      invoiceData: {},
      ruleForm: {
        companyCardList: [],
      },
      rules: {
        companyCardList: [
          { required: true, validator: validIsEmptyArr, trigger: "change" },
        ],
      },
      currentId: null,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.loading = true;
      let params = {
        createBy: this.$store.state.user.userId,
        pageNum: this.queryParams.pageNum,
        pageSize: 5,
        ...this.form,
        startTime: this.createTime.length > 0 ? this.createTime[0] : "",
        endTime: this.createTime.length > 0 ? this.createTime[1] : "",
      };
      orderList(params).then((response) => {
        this.subscribeList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    handleCurrentChange(pageNum) {
      this.queryParams.pageNum = pageNum;
      this.getList();
    },
    handleQuery() {
      this.getList();
    },
    resetQuery() {
      this.queryParams.pageNum = 1;
      this.form = {
        id: "",
        phone: "",
        appName: "",
        orderCode: "",
        orderStatus: "",
      };
      this.createTime = [];
      this.getList();
    },
    getFlag(value) {
      this.flag = value;
    },
    gotoDetail(id) {
      this.$router.push({
        path: "/user/orderManageDetail",
        query: {
          id,
        },
      });
    },
    getStatus(status) {
      let orderStatus;
      this.orderStatusList.forEach((item) => {
        if (item.dictValue == status) {
          orderStatus = item.dictLabel;
        }
      });
      return orderStatus;
    },
    goShip(id) {
      this.$confirm("货品发货后无法撤销，确认发货吗？", "发货确认", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let data = {
            id,
            orderStatus: 4,
          };
          modifyStatus(data).then((res) => {
            if (res.code === 200) {
              this.$message.success("操作成功!");
              this.getList();
            }
          });
        })
        .catch(() => {});
    },
    invoicing(id) {
      this.currentId = id;
      invoiceList().then((res) => {
        if (res.code === 200) {
          this.invoiceData = res.data;
          this.invoiceVisible = true;
          this.ruleForm.companyCardList = [];
        }
      });
    },
    cancelDialog() {
      this.invoiceVisible = false;
    },
    submitForm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let data = {
            companyCardList: this.ruleForm.companyCardList,
            orderId: this.currentId,
          };
          sendInvoice(data).then((res) => {
            if (res.code === 200) {
              this.invoiceVisible = false;
              this.$message.success("操作成功!");
              this.getList();
            }
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    handleApplicationRemove(file, fileList) {
      this.ruleForm.companyCardList = [];
    },
    handleApplicationSuccess(res, file, fileList) {
      if (res.code == 200) {
        this.ruleForm.companyCardList.push({
          name: res.data.name,
          url: res.data.url,
          type: res.data.type,
          suffix: res.data.suffix,
        });
      }
    },
    handleExceedLicence(files, fileList) {
      let num = files.length + fileList.length;
      if (num >= 1) {
        this.$message.error("上传数量超过上限");
        return false;
      }
    },
    handlePreview(file) {
      window.open(file.url);
    },
    // 文件上传之前的钩子
    handleBeforeUpload(file) {
      let { name, type, size } = file;
      // let typeList = this.accept
      //   .split(",")
      //   .map((item) => item.trim().toLowerCase().substr(1));
      // let dotIndex = name.lastIndexOf(".");
      // // 文件类型校验
      // if (dotIndex === -1) {
      //   this.$message.error("请上传正确格式的文件");
      //   return false;
      // } else {
      //   let suffix = name.substring(dotIndex + 1);
      //   if (typeList.indexOf(suffix.toLowerCase()) === -1) {
      //     this.$message.error("请上传正确格式的文件");
      //     return false;
      //   }
      // }
      // 文件上传大小限制
      if (size > 1048576 * 5) {
        this.$message.error("文件大小不能超过5M！");
        return false;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.app-container {
  background: #f4f5f9;
}
.content {
  width: 100%;
  padding: 40px;
  background: #ffffff;
  // height: 800px;
  // background: rgb(242, 248, 255);
  .content_type {
    display: flex;
    width: 100%;
    margin-bottom: 30px;
    .title {
      width: 100px;
      padding-left: 20px;
      height: 30px;
      line-height: 30px;
      // border-left: 4px solid #21C9B8;
      font-weight: 600;
      font-size: 18px;
    }
    // .right_content {
    //   width: calc(100% - 100px);
    //   text-align: right;
    // }
  }
  .tabStyle {
    display: flex;
    // background: rgb(243, 248, 254);
    .buttonStyle {
      width: 100px;
      padding: 10px;
      color: #21c9b8;
      text-align: center;
      cursor: pointer;
      border: 1px solid #21c9b8;
    }
    .buttonStyle:nth-child(n + 2) {
      border-left: none;
    }
    .buttonHover {
      background: #21c9b8;
      color: #ffffff;
    }
  }
  .tableStyle {
    .everyItem {
      width: 100%;
      height: 200px;
      box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.06);
      margin-top: 20px;
      padding: 20px;
      // background: #ffffff;
      .orderNumTime {
        display: flex;
      }
      .driver {
        width: 100%;
        height: 1px;
        background: #ccc;
        margin: 15px 0;
      }
      .item_content {
        width: 100%;
        // height: 100%;
        display: flex;
        align-items: center;
        .item_img {
          width: 14%;
          height: 110px;
          img {
            width: 100%;
            height: 100%;
          }
        }
        .item_desc {
          margin-left: 20px;
          width: 25%;
          .title {
            font-size: 16px;
            font-family: Source Han Sans CN;
            font-weight: 500;
            color: #333333;
          }
        }
        .item_amounts {
          width: 10%;
          text-align: right;
        }
        .driverVertical {
          width: 1px;
          height: 110px;
          background: #ccc;
          margin: 0 8%;
        }
      }
    }
  }
}
</style>

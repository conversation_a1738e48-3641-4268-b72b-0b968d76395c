<template>
  <div class="intention-page">
    <div class="intention-page-header">
      <div class="banner">
        <img
          src="https://xp-tech.oss-cn-beijing.aliyuncs.com/cykc/20230210/1676034162343360.webp"
          alt="在线申报"
        />
      </div>
    </div>
    <div class="intention-page-title">在线申报</div>
    <div v-loading="loading" class="card-container intention-form">
      <div class="form-content">
        <el-form ref="form" :rules="rules" :model="form" label-width="80px">
          <el-form-item label="政策名称">
            {{ form.title }}
          </el-form-item>
          <el-form-item label="联系人" prop="contractPerson">
            <el-input v-model="form.contractPerson" placeholder="请输入联系人"></el-input>
          </el-form-item>
          <el-form-item label="联系方式" prop="contractPhone">
            <el-input disabled v-model="form.contractPhone"></el-input>
          </el-form-item>
          <el-form-item label="上传文件" prop="fileList">
            <FileUpload v-model="form.fileList" />
          </el-form-item>
          <el-form-item class="footer-submit">
            <el-button @click="onSubmit(1)" type="primary" plain>暂存草稿</el-button>
            <el-button type="primary" @click="onSubmit(2)">提交审核</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>

import {editPolicyApply} from "@/api/system/demand";

export default {
  name: "addPolicy",
  data() {
    const { user } = this.$store.state;
    return {
      loading: false,
      form: {
        title: undefined,
        policyId: undefined,
        fileList: [],
        contractPerson: user.name,
        contractPhone: user.tel,
        status: undefined,
      },
      rules: {
        contractPerson: [
          { required: true, message: "请输入联系人", trigger: "blur" },
        ],
        contractPhone: [
          { required: true, message: "请先维护联系方式", trigger: "blur" },
        ],
        fileList: [
          { required: true, message: "请上传文件", trigger: "blur" },
        ],
      },
    }
  },
  created() {
    const { id, title } = this.$route.params;
    if (id) {
      this.form.title = title;
      this.form.policyId = id;
    }
  },
  methods: {
    onSubmit(status) {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.loading = true;
          editPolicyApply({
            ...this.form,
            status,
          }).then((res) => {
            const { code, msg } = res;
            if (code === 200) {
              this.$message.success("提交成功");
              this.$router.back();
            } else {
              this.$message.error(msg || "提交失败");
            }
          }).finally(() => this.loading = false)
        }
      })

    },
  }
}
</script>

<style lang="scss" scoped>
@import "~@/assets/styles/mixin.scss";
.intention-page {
  background-color: #F4F5F9;
  padding-bottom: 80px;
  &-header {
    background-color: #FFFFFF;
    .banner {
      width: 100%;
      height: 540px;
      background-color: #f5f5f5;
      img {
        width: 100%;
        height: 540px;
        object-fit: fill;
      }
    }
    .body {
      padding: 60px 0;
    }
  }
  &-title {
    font-size: 40px;
    font-weight: 500;
    color: #333333;
    line-height: 40px;
    text-align: center;
    padding: 60px 0;
  }
  .intention-form {
    @include flexCenter;
    height: 664px;
    background-color: #FFFFFF;
    margin-bottom: 80px;
    .form-content {
      width: 750px;
      .footer-submit {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        margin-top: 40px;
        .el-button {
          width: 160px;
        }
      }
    }
  }
}
</style>

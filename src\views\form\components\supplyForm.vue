<template>
  <div class="supply-form">
    <el-form ref="form" :rules="rules" :model="form" label-position="top">
      <el-form-item label="资源类型" prop="supplyType">
        <el-checkbox-group
          v-model="form.supplyType"
          placeholder="请选择"
          clearable
        >
          <el-checkbox
            v-for="dict in dict.type.supply_type"
            :key="dict.value"
            :label="dict.value"
            :value="dict.value"
            >{{ dict.label }}</el-checkbox
          >
        </el-checkbox-group>
      </el-form-item>
      <el-form-item prop="supplyName" label="资源标题">
        <el-input
          v-model="form.supplyName"
          maxlength="50"
          show-word-limit
          placeholder="请输入"
        ></el-input>
      </el-form-item>
      <el-form-item prop="summary" label="资源描述">
        <el-input
          type="textarea"
          v-model="form.summary"
          maxlength="500"
          rows="6"
          show-word-limit
          placeholder="请输入资源描述"
        ></el-input>
        <div class="extra-content">
          <div class="extra-content-header">
            <el-button
              :loading="keywordLoading"
              @click="handleKeywordList"
              size="small"
              type="primary"
              >生成关键词</el-button
            >
            <span class="tip">生成关键词有利于实现精准匹配哦！</span>
          </div>
          <div v-if="form.keywords.length > 0" class="extra-content-body">
            <el-tag
              :key="`${tag}_${index}`"
              v-for="(tag, index) in form.keywords"
              closable
              size="small"
              disable-transitions
              @close="handleClose(tag)"
            >
              {{ tag }}
            </el-tag>
          </div>
        </div>
      </el-form-item>
      <el-form-item prop="technologyType" label="技术类别">
        <el-select
          v-model="form.technologyType"
          filterable
          allow-create
          multiple
          style="width: 100%"
          placeholder="请选择"
        >
          <el-option
            v-for="item in dict.type.technology_type"
            :key="item.value"
            :label="item.label"
            :value="item.label"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="applicationArea" label="应用领域">
        <el-select
          v-model="form.applicationArea"
          filterable
          allow-create
          multiple
          style="width: 100%"
          placeholder="请选择"
        >
          <el-option
            v-for="item in dict.type.application_area"
            :key="item.value"
            :label="item.label"
            :value="item.label"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="资源图片" prop="productPhoto">
        <ImageUpload v-model="form.productPhoto" />
      </el-form-item>
      <el-form-item label="合作方式" prop="cooperationMode">
        <el-select
          v-model="form.cooperationMode"
          placeholder="请选择"
          style="width: 100%"
          clearable
        >
          <el-option
            v-for="dict in dict.type.cooperation_mode"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="产品阶段" prop="productStage">
        <el-select
          v-model="form.productStage"
          placeholder="请选择"
          style="width: 100%"
          clearable
        >
          <el-option
            v-for="dict in dict.type.product_stage"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="上传附件" prop="enclosure">
        <FileUpload v-model="form.enclosure" />
      </el-form-item>
      <el-form-item label="公司名称" prop="companyName">
        <el-input
          disabled
          v-model="form.companyName"
          placeholder="请先绑定公司"
        ></el-input>
      </el-form-item>
      <el-form-item label="联系人" prop="contactsName">
        <el-input
          disabled
          v-model="form.contactsName"
          placeholder="请先维护联系人"
        ></el-input>
      </el-form-item>
      <el-form-item label="联系方式" prop="contactsMobile">
        <el-input
          disabled
          v-model="form.contactsMobile"
          placeholder="请先维护联系方式"
        ></el-input>
      </el-form-item>
      <el-form-item class="footer-submit">
        <el-button @click.once="onCancel">取消</el-button>
        <el-button @click="onSubmit('0')" type="primary" plain
          >暂存草稿</el-button
        >
        <el-button type="primary" @click="onSubmit('2')">发布</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { keywordList, supplyAdd } from "@/api/zhm";
import cache from "@/plugins/cache";

export default {
  name: "supplyForm",
  dicts: [
    "supply_type",
    "technology_type",
    "cooperation_mode",
    "product_stage",
    "application_area",
  ],
  data() {
    const { user } = this.$store.state;
    return {
      loading: false,
      keywordLoading: false,
      form: {
        // 供给类型
        supplyType: [],
        // 需求标题
        supplyName: undefined,
        // 描述
        summary: undefined,
        // 关键词
        keywords: [],
        // 技术类别
        technologyType: [],
        // 应用领域
        applicationArea: [],
        // 产品图片
        productPhoto: [],
        // 附件
        enclosure: [],
        // 产品阶段
        productStage: undefined,
        // 合作方式
        cooperationMode: undefined,
        // 公司名称
        companyName: user.companyName || "柠檬豆",
        // 联系人
        contactsName: user.name,
        // 联系电话
        contactsMobile: user.tel,
        publisherName: user.name,
        publisherMobile: user.tel,
        businessNo: user.bussinessNo,
        auditStatus: "2",
        displayStatus: "1",
      },
      rules: {
        supplyType: [
          { required: true, message: "请选择供给类型", trigger: "blur" },
        ],
        supplyName: [
          { required: true, message: "请输入供给标题", trigger: "blur" },
        ],
        summary: [
          { required: true, message: "请输入供给描述", trigger: "blur" },
        ],
        technologyType: [
          { required: true, message: "请选择技术类别", trigger: "blur" },
        ],
        applicationArea: [
          { required: true, message: "请选择应用领域", trigger: "blur" },
        ],
        productPhoto: [
          { required: true, message: "请选择供给图片", trigger: "blur" },
        ],
        companyName: [
          { required: true, message: "请维护公司名称", trigger: "blur" },
        ],
        contactsName: [
          { required: true, message: "请维护联系人", trigger: "blur" },
        ],
        contactsMobile: [
          { required: true, message: "请维护联系电话", trigger: "blur" },
        ],
      },
    };
  },
  methods: {
    init() {
      const data = cache.local.getJSON("supply_data");
      if (data) {
        this.form = data;
      }
    },
    onCancel() {
      this.$router.back();
    },
    onSave() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          cache.local.setJSON("supply_data", this.form);
          this.$message.success("暂存成功");
        }
      });
    },
    onSubmit(status) {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.loading = true;
          const {
            supplyType,
            keywords,
            applicationArea,
            productPhoto,
            enclosure,
            technologyType,
            ...rest
          } = this.form;
          const data = {
            ...rest,
            auditStatus: status,
          };
          if (supplyType.length > 0) {
            data["supplyType"] = supplyType.join();
          }
          if (keywords.length > 0) {
            data["keywords"] = keywords.join();
          }
          if (technologyType.length > 0) {
            data["technologyType"] = technologyType.join();
          }
          if (applicationArea.length > 0) {
            data["applicationArea"] = applicationArea.join();
          }
          if (productPhoto.length > 0) {
            data["productPhoto"] = JSON.stringify(productPhoto);
          }
          if (enclosure.length > 0) {
            data["enclosure"] = JSON.stringify(enclosure);
          }
          supplyAdd(data)
            .then((res) => {
              const { code, msg } = res;
              if (code === 200) {
                cache.local.remove("supply_data");
                this.$message.success("发布成功");
                this.$router.back();
              } else {
                this.$message.error(msg || "发布失败");
              }
            })
            .finally(() => (this.loading = false));
        }
      });
    },
    handleKeywordList() {
      const { summary } = this.form;
      if (summary) {
        this.keywordLoading = true;
        keywordList(summary)
          .then((res) => {
            const { code, data, msg } = res;
            if (code === 200) {
              this.form.keywords = data;
            } else {
              this.$message.error(msg);
            }
          })
          .finally(() => (this.keywordLoading = false));
      } else {
        this.$message.warning("请输入需求描述");
      }
    },

    handleClose(tag) {
      this.form.keywords = this.form.keywords.filter((item) => item !== tag);
    },
  },
};
</script>

<style lang="scss" scoped>
.supply-form {
  width: 676px;
  .label-item {
    display: flex;
    flex-direction: row;
    align-items: center;
    .title {
      font-size: 18px;
      font-weight: 500;
      color: #333333;
      line-height: 18px;
    }
    .extra {
      font-size: 14px;
      font-weight: 400;
      color: #999999;
      line-height: 14px;
    }
  }
  .extra-content {
    padding: 12px 0;
    &-header {
      display: flex;
      flex-direction: row;
      align-items: center;
      .tip {
        font-size: 14px;
        font-weight: 400;
        color: #999999;
        line-height: 14px;
        margin-left: 12px;
      }
    }
    &-body {
      padding-top: 6px;
      .el-tag {
        margin-right: 12px;
        &:last-child {
          margin-right: 0;
        }
      }
    }
  }
  ::v-deep.el-form-item__label {
    font-size: 18px;
    font-weight: 500;
    color: #333333;
    line-height: 18px;
    margin-bottom: 12px;
    padding: 0;
  }
  .el-checkbox {
    font-size: 14px;
    font-weight: 400;
    color: #262626;
    line-height: 18px;
    margin-right: 28px;
  }
  .footer-submit {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    margin-top: 40px;
    .el-button {
      width: 160px;
    }
  }
}
</style>

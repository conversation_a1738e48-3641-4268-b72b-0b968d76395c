import request from "@/utils/request";

// 制造共享-设备共享-列表
export function deviceListData(params) {
  return request({
    url: "/system/deviceInfo/list",
    method: "get",
    params,
  });
}

// 制造共享-设备共享-用户个人列表
export function deviceUserListData(params) {
  return request({
    url: "/system/deviceInfo/user/list",
    method: "get",
    params,
  });
}

// 制造共享-设备共享-详情
export function deviceDetailData(id) {
  return request({
    url: `/system/deviceInfo/${id}`,
    method: "get",
  });
}

// 删除设备信息
export function delDeviceInfo(id) {
  return request({
    url: "/system/deviceInfo/" + id,
    method: "delete",
  });
}

// 新增设备信息
export function addDeviceInfo(data) {
  return request({
    url: "/system/deviceInfo",
    method: "post",
    data: data,
  });
}

// 修改设备信息
export function updateDeviceInfo(data) {
  return request({
    url: "/system/deviceInfo",
    method: "put",
    data: data,
  });
}

// 制造共享-车间共享-列表
export function workListData(params) {
  return request({
    url: "/system/workInfo/list",
    method: "get",
    params,
  });
}

// 制造共享-车间共享-用户个人列表
export function workUserListData(params) {
  return request({
    url: "/system/workInfo/user/list",
    method: "get",
    params,
  });
}

// 制造共享-车间共享-详情
export function workDetailData(id) {
  return request({
    url: `/system/workInfo/${id}`,
    method: "get",
  });
}

// 新增车间信息
export function addWorkInfo(data) {
  return request({
    url: "/system/workInfo",
    method: "post",
    data: data,
  });
}

// 修改车间信息
export function updateWorkInfo(data) {
  return request({
    url: "/system/workInfo",
    method: "put",
    data: data,
  });
}

// 删除车间信息
export function delWorkInfo(id) {
  return request({
    url: "/system/workInfo/" + id,
    method: "delete",
  });
}

// 制造共享-订单共享-列表
export function orderListData(params) {
  return request({
    url: "/system/materialInfo/listWithOrder",
    // url: "/system/manufactureOrder/list",
    method: "get",
    params,
  });
}
// 制造共享-订单共享-列表
export function manufactureOrderListData(params) {
  return request({
    // url: "/system/materialInfo/listWithOrder",
    url: "/system/manufactureOrder/list",
    method: "get",
    params,
  });
}

// 制造共享-订单共享-详情
export function orderDetailData(id) {
  return request({
    // url: `/system/manufactureOrder/${id}`,
    url: `/system/manufactureOrder/withMaterials/${id}`,
    method: "get",
  });
}

// 制造共享-外协工序-列表
export function processListData(params) {
  return request({
    url: "/system/outsourcingRequirement/list",
    method: "get",
    params,
  });
}

// 制造共享-入驻工厂-列表
export function enteringFactoryListData(params) {
  return request({
    url: "/system/settledFactory/list",
    method: "get",
    params,
  });
}

// 制造共享-入驻工厂-新增
export function enteringFactoryAdd(data) {
  return request({
    url: "/system/settledFactory/add",
    method: "post",
    data,
  });
}

// 制造共享-入驻工厂-详情
export function enteringFactoryDetail(id) {
  return request({
    url: `/system/settledFactory/detail/${id}`,
    method: "get",
  });
}
// 查询产品信息列表
export function listSysProduct(query) {
  return request({
    url: '/system/SysProduct/list',
    method: 'get',
    params: query
  })
}
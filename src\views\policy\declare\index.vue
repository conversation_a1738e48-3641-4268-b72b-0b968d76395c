<template>
  <div class="policy-declare-container">
    <!-- banner图 -->
    <div class="policy-declarel-banner">
      <img src="../../../assets/policyDeclare/policyDeclareBanner.png" alt="" />
    </div>
    <div v-loading="loading">
      <div class="policy-declarel-title-content">
        <div class="policy-declarel-title-box">
          <div class="policy-declarel-divider"></div>
          <div class="policy-declarel-title">政策申报</div>
          <div class="policy-declarel-divider"></div>
        </div>
        <div class="policy-declarel-search-box">
          <el-form ref="form" class="policy-declarel-search-form" :model="form">
            <el-form-item>
              <el-input
                v-model="form.text"
                placeholder="请输入搜索内容"
                class="policy-declarel-search-input"
              >
                <el-button
                  slot="append"
                  class="policy-declarel-search-btn"
                  @click="onSearch"
                  >搜索</el-button
                >
              </el-input>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div class="policy-declarel-card">
        <div class="policy-declarel-info-content">
          <div class="policy-declarel-search-type-box">
            <el-form ref="formInfo" :model="formInfo">
              <div class="policy-declarel-search-line">
                <el-form-item
                  label="发布单位"
                  class="policy-declarel-search-line-item"
                  :class="{ advanced: !advancedReleaseId }"
                >
                  <el-radio-group
                    v-model="formInfo.releaseId"
                    class="more-radio"
                    @input="changeRadio"
                  >
                    <el-radio-button label="">全部</el-radio-button>
                    <el-radio-button
                      v-for="(item, index) in releaseIdList"
                      :key="index"
                      :label="item.id"
                      >{{ item.name }}</el-radio-button
                    >
                  </el-radio-group>
                </el-form-item>
                <el-button
                  class="policy-declarel-search-line-btn"
                  @click="toggleReleaseId"
                  >{{ advancedReleaseId ? "收起" : "更多"
                  }}<i class="el-icon-arrow-down"></i>
                </el-button>
              </div>
              <div class="policy-declarel-search-line">
                <el-form-item
                  label="政策类型"
                  class="policy-declarel-search-line-item"
                  :class="{ advanced: !advancedType }"
                >
                  <el-radio-group
                    v-model="formInfo.type"
                    class="more-radio"
                    @input="changeRadio"
                  >
                    <el-radio-button label="">全部</el-radio-button>
                    <el-radio-button
                      v-for="(item, index) in typeList"
                      :key="index"
                      :label="item.dictValue"
                      >{{ item.dictLabel }}</el-radio-button
                    >
                  </el-radio-group>
                </el-form-item>
                <el-button
                  class="policy-declarel-search-line-btn"
                  @click="toggleType"
                  >{{ advancedType ? "收起" : "更多"
                  }}<i class="el-icon-arrow-down"></i>
                </el-button>
              </div>
              <div class="policy-declarel-search-line">
                <el-form-item
                  label="政策状态"
                  class="policy-declarel-search-line-item"
                >
                  <el-radio-group
                    v-model="formInfo.policyStatus"
                    class="more-radio"
                    @input="changeRadio"
                  >
                    <el-radio-button label="">全部</el-radio-button>
                    <el-radio-button
                      v-for="(item, index) in policyStatusList"
                      :key="index"
                      :label="item.value"
                      >{{ item.label }}</el-radio-button
                    >
                  </el-radio-group>
                </el-form-item>
              </div>
            </el-form>
          </div>
          <div
            v-for="(item, index) in data"
            :key="index"
            class="policy-declarel-list-item"
            @click="goPolicyDeclarelDetail(item)"
          >
            <div class="list-item-content">
              <div class="list-item-headline">
                <div class="item-title">{{ item.releaseUnitName }}</div>
                <div v-if="item.releaseDistrict" class="item-address-tag">
                  <img
                    src="../../../assets/policyDeclare/policyAddressIcon.png"
                    alt=""
                    class="item-address-img"
                  />
                  <div class="item-address-text">
                    {{ item.releaseDistrict }}
                  </div>
                </div>
              </div>
              <div class="list-item-title">
                {{ item.title }}
              </div>
              <div class="list-item-box">
                <div class="list-item-time">
                  <div
                    v-if="item.policyStatus === 2"
                    class="list-item-time-end"
                  >
                    申报结束
                  </div>
                  <div v-else class="list-item-time-red">
                    距申报截止还有
                    <div class="red-num">{{ item.dayCount }}</div>
                    天
                  </div>
                </div>
                <div class="list-item-money">
                  <div class="list-item-money-title">最高奖励</div>
                  <span class="list-item-money-num">{{ item.maxReward }}</span>
                </div>
              </div>
              <div class="list-item-status">
                <!-- 1进行中  2已截止 -->
                <img
                  v-if="item.policyStatus === 1"
                  src="../../../assets/policyDeclare/carryOnIcon.png"
                  alt=""
                />
                <img
                  v-else
                  src="../../../assets/policyDeclare/cutoffIcon.png"
                  alt=""
                />
              </div>
            </div>
          </div>
          <div class="policy-declarel-page-end">
            <el-button class="policy-declarel-page-btn" @click="goHome"
              >首页</el-button
            >
            <el-pagination
              v-if="data && data.length > 0"
              background
              layout="prev, pager, next"
              class="policy-declarel-pagination"
              :page-size="pageSize"
              :current-page="pageNum"
              :total="total"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            >
            </el-pagination>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getPolicyDeclareList, getListByName } from "@/api/policyDeclare";
import { getDicts } from "@/api/system/dict/data";
import { POLICY_STATUS } from "@/const/status";

export default {
  data() {
    return {
      loading: false,
      form: {
        text: "", //搜索内容
      },
      formInfo: {
        releaseId: "", //发布单位
        type: "", //政策类型
        policyStatus: "", //政策状态
        labelCodeList: [], //政策画像code集合
      },
      releaseIdList: [], //发布单位下拉列表
      typeList: [], //政策类型下拉列表
      policyStatusList: POLICY_STATUS,
      advancedReleaseId: false,
      advancedType: false,
      data: [],
      pageNum: 1,
      pageSize: 10,
      total: 0,
    };
  },
  created() {
    const { code } = this.$route.params || {};
    console.log(this.$route);
    if (code) {
      this.formInfo.labelCodeList = code;
    }
    this.getDictsList("policy_type", "typeList");
    this.getListByName();
    this.search();
  },
  methods: {
    search() {
      this.loading = true;
      getPolicyDeclareList({
        text: this.form.text,
        type: this.formInfo.type,
        releaseId: this.formInfo.releaseId,
        policyStatus: this.formInfo.policyStatus,
        labelCodeList: this.formInfo.labelCodeList,
        pageNum: this.pageNum,
        pageSize: this.pageSize,
      })
        .then((res) => {
          this.loading = false;
          let { rows, total } = res || [];
          this.data = rows;
          this.total = total;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    // 查询发布单位
    getListByName() {
      getListByName().then((res) => {
        this.releaseIdList = res.data || [];
      });
    },
    // 字典
    getDictsList(code, propertyName) {
      getDicts(code).then((res) => {
        this[propertyName] = res.data || [];
      });
    },
    // 显示更多发布单位
    toggleReleaseId() {
      this.advancedReleaseId = !this.advancedReleaseId;
    },
    // 显示更多政策类型
    toggleType() {
      this.advancedType = !this.advancedType;
    },
    changeRadio() {
      console.log(typeof this.formInfo.releaseId, "0000");
      this.onSearch();
    },
    handleSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.onSearch();
    },
    handleCurrentChange(pageNum) {
      this.pageNum = pageNum;
      this.search();
    },
    onSearch() {
      this.pageNum = 1;
      this.search();
    },
    // 跳转到政策详情页面
    goPolicyDeclarelDetail(item) {
      let routeData = this.$router.resolve({
        path: "/policyDeclareDetail",
        query: { id: item.id },
      });
      window.open(routeData.href, "_blank");
    },
    // 跳转到首页
    goHome() {
      this.$router.push({ path: "/index" });
    },
  },
  watch: {},
  computed: {},
};
</script>
<style lang="scss" scoped>
.policy-declare-container {
  width: 100%;
  .policy-declarel-banner {
    width: 100%;
    height: 50vh;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .policy-declarel-title-content {
    width: 100%;
    padding-bottom: 18px;
    .policy-declarel-title-box {
      width: 336px;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 60px 0 40px;
      .policy-declarel-title {
        font-size: 40px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #333;
        line-height: 40px;
        padding: 0 40px;
      }
      .policy-declarel-divider {
        width: 48px;
        height: 4px;
        background: #21c9b8;
      }
    }
    .policy-declarel-search-box {
      .policy-declarel-search-form {
        text-align: center;
        .policy-declarel-search-input {
          width: 792px;
          height: 54px;
          .policy-declarel-search-btn {
            width: 100px;
          }
        }
      }
    }
  }
  .policy-declarel-card {
    background: #f4f5f9;
    padding-top: 40px;
    .policy-declarel-info-content {
      width: 1200px;
      margin: 0 auto;
      .policy-declarel-search-type-box {
        background: #fff;
        margin-bottom: 17px;
        .policy-declarel-search-line {
          display: flex;
          justify-content: space-between;
          padding: 14px 24px 4px;
          .policy-declarel-search-line-item {
            flex: 1;
            margin-bottom: 0;
            display: flex;
            &.advanced {
              overflow: hidden;
              height: 45px;
            }
            .more-radio {
              margin-top: 11px;
              flex: 1;
            }
          }
          .policy-declarel-search-line-btn {
            display: inline-block;
            width: 64px;
            height: 24px;
            background: #fff;
            border-radius: 2px;
            border: 1px solid #d9d9d9;
            font-size: 12px;
            font-family: PingFangSC-Regular, PingFang SC;
            color: #333;
            display: flex;
            align-items: center;
            padding: 0 16px;
            margin-top: 5px;
            &:hover {
              border: 1px solid #21c9b8;
              color: #21c9b8;
            }
          }
          & + .policy-declarel-search-line {
            border-top: 1px solid #f5f5f5;
          }
        }
      }
      .policy-declarel-list-item {
        position: relative;
        width: 100%;
        background: #fff;
        border-radius: 12px;
        .list-item-content {
          padding: 27px 24px 24px 24px;
          cursor: pointer;
          .list-item-headline {
            display: flex;
            align-items: center;
            .item-title {
              max-width: 570px;
              font-size: 18px;
              font-family: PingFangSC-Regular, PingFang SC;
              color: #999;
              line-height: 18px;
              word-break: break-all;
            }
            .item-address-tag {
              display: flex;
              align-items: center;
              border-radius: 6px;
              border: 2px solid #ff8516;
              font-size: 15px;
              line-height: 15px;
              text-align: center;
              margin-left: 12px;
              color: #ff8516;
              .item-address-img {
                width: 19px;
                height: 18px;
                margin-right: 1px;
              }
              .item-address-text {
                max-width: 570px;
                word-break: break-all;
                padding: 3px 5px 2px 0;
              }
            }
          }
          .list-item-title {
            font-size: 24px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #323233;
            line-height: 36px;
            word-break: break-all;
            padding-top: 18px;
          }
          .list-item-box {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 20px;
            .list-item-time {
              background: #f5f5f5;
              border-radius: 6px;
              font-size: 18px;
              font-family: PingFangSC-Regular, PingFang SC;
              color: #999;
              line-height: 18px;
              .list-item-time-end {
                padding: 6px 12px;
              }
              .list-item-time-red {
                display: flex;
                align-items: center;
                padding: 6px 15px 6px 12px;
                .red-num {
                  max-width: 270px;
                  font-size: 24px;
                  font-family: PingFangSC-Medium, PingFang SC;
                  font-weight: 500;
                  color: #cf4140;
                  line-height: 24px;
                  word-wrap: break-word;
                }
              }
            }
            .list-item-money {
              display: flex;
              align-items: flex-end;
              max-width: 570px;
              font-family: PingFangSC-Regular, PingFang SC;
              .list-item-money-title {
                font-size: 18px;
                color: #999;
                line-height: 18px;
                margin-right: 6px;
              }
              .list-item-money-num {
                max-width: 270px;
                font-size: 36px;
                font-weight: 500;
                color: #cf4140;
                line-height: 36px;
                word-wrap: break-word;
              }
            }
          }
        }
        & + .policy-declarel-list-item {
          margin-top: 24px;
        }
        &:hover {
          .list-item-title {
            color: #21c9b8;
          }
        }
        .list-item-status {
          position: absolute;
          top: 0;
          right: 0;
          img {
            width: 92px;
            height: 71px;
          }
        }
      }
      .policy-declarel-page-end {
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 0 auto;
        padding: 24px 0 60px;
        .policy-declarel-page-btn {
          width: 82px;
          height: 32px;
          background: #fff;
          border-radius: 4px;
          border: 1px solid #d9d9d9;
          font-family: PingFangSC-Regular, PingFang SC;
          color: #333;
          line-height: 10px;
        }
      }
    }
  }
}
</style>

<style lang="scss">
.policy-declare-container {
  .policy-declarel-search-input {
    .el-input__inner {
      height: 54px;
      background: #fff;
      border-radius: 27px 0 0 27px;
      border: 1px solid #d9d9d9;
      font-size: 16px;
      font-family: PingFangSC-Regular, PingFang SC;
      line-height: 24px;
      padding-left: 30px;
    }
    .el-input-group__append {
      border-radius: 0px 100px 100px 0px;
      background: #21c9b8;
      font-size: 16px;
      font-family: PingFangSC-Regular, PingFang SC;
      color: #fff;
      line-height: 24px;
    }
  }
  .el-form-item__label {
    width: 88px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #999;
    padding-right: 32px;
    text-align: left;
  }
  .policy-declarel-search-line {
    .el-form-item__content {
      width: 970px;
    }
  }
  .el-radio-button {
    padding-bottom: 20px;
    .el-radio-button__inner {
      border: none;
      padding: 0 32px 0 0;
      background: none;
      &:hover {
        color: #21c9b8;
      }
    }
    &.is-active {
      .el-radio-button__inner {
        color: #21c9b8;
        background: none;
      }
    }
    .el-radio-button__orig-radio:checked {
      & + .el-radio-button__inner {
        box-shadow: unset;
      }
    }
  }
  .policy-declarel-page-end {
    .policy-declarel-pagination {
      .btn-prev,
      .btn-next,
      .btn-quickprev {
        width: 32px;
        height: 32px;
        background: #fff;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        color: #333;
      }
      &.is-background {
        .el-pager {
          .number {
            width: 32px;
            height: 32px;
            border: 1px solid #d9d9d9;
            background: #fff;
            border-radius: 4px;
            line-height: 32px;
            &.active {
              background: #21c9b8;
              border: 1px solid #21c9b8;
            }
          }
        }
      }
    }
  }
}
</style>

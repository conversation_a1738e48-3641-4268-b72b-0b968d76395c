<template>
  <div class="demand-supply-page">
    <div class="demand-supply-page-header">
      <div class="banner">
        <img
          src="https://xp-tech.oss-cn-beijing.aliyuncs.com/cykc/20230211/1676086235866518.webp"
          alt="发布需求/发布资源"
        />
      </div>
    </div>
    <div class="demand-supply-page-title">
      <div class="toggle">
        <div
          @click="onChangeTab('demand')"
          :class="{ active: tab === 'demand' }"
          class="toggle-item"
        >
          发布需求
        </div>
        <div
          @click="onChangeTab('supply')"
          :class="{ active: tab === 'supply' }"
          class="toggle-item"
        >
          发布资源
        </div>
      </div>
    </div>
    <div class="card-container demand-supply-form">
      <div :class="{ active: tab === 'demand' }" class="tab-card">
        <demand-form />
      </div>
      <div :class="{ active: tab === 'supply' }" class="tab-card">
        <supply-form />
      </div>
    </div>
  </div>
</template>

<script>
import DemandForm from "./components/demandForm.vue";
import SupplyForm from "./components/supplyForm.vue";
export default {
  name: "addDemandAndSupply",
  components: {
    DemandForm,
    SupplyForm,
  },
  data() {
    return {
      tab: "demand",
    };
  },
  created() {
    this.onInit();
  },
  methods: {
    onInit() {
      const type = this.$route.query.type;
      if (type) {
        this.onChangeTab(type);
      }
    },
    onChangeTab(key) {
      if (this.tab !== key) {
        this.tab = key;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "~@/assets/styles/mixin.scss";
.demand-supply-page {
  background-color: #f4f5f9;
  padding-bottom: 80px;
  &-header {
    background-color: #ffffff;
    .banner {
      width: 100%;
      height: 540px;
      background-color: #f5f5f5;
      img {
        width: 100%;
        height: 540px;
        object-fit: fill;
      }
    }
    .body {
      padding: 60px 0;
    }
  }
  &-title {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 60px 0 40px 0;
    .toggle {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      flex-shrink: 0;
      width: 320px;
      height: 60px;
      background: #ffffff;
      box-shadow: 0 4px 16px 0 rgba(38, 74, 116, 0.1);
      border-radius: 30px;
      cursor: pointer;
      &-item {
        @include flexCenter;
        width: 160px;
        height: 60px;
        border-radius: 30px;
        font-size: 20px;
        font-weight: 500;
        color: #21c9b8;
        line-height: 20px;
        transition: color, background-color 0.45ms ease;
        &.active {
          background-color: #21c9b8;
          color: #ffffff;
        }
      }
    }
  }
  .demand-supply-form {
    @include flexCenter;
    background-color: #ffffff;
    padding: 80px 0;
    margin-bottom: 80px;
    .tab-card {
      display: none;
      min-height: 1080px;
      transition: display 0.45ms ease;
      &.active {
        display: block;
      }
    }
  }
}
</style>

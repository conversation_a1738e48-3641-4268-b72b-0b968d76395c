<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="2.5" :xs="24">
        <user-menu />
      </el-col>
      <el-col :span="20" :xs="24">
        <div class="container">
          <div class="content">
            <!-- 步骤条 -->
            <div class="stepsStyle">
              <el-steps :active="active">
                <el-step title="创建应用" description="去创建"></el-step>
                <el-step title="配置开发管理" description="地址配置"></el-step>
                <!-- <el-step title="应用测试" description="查看调试文档"></el-step> -->
                <el-step title="上架应用" description="去上架"></el-step>
              </el-steps>
            </div>
            <div class="currentContent">
              <div v-show="active == 0">
                <div class="title">基本信息</div>
                <el-form
                  :model="ruleForm"
                  :rules="rules"
                  ref="appliForm"
                  label-width="100px"
                  class="demo-ruleForm"
                >
                  <el-form-item label="应用名称" prop="appName">
                    <el-input v-model="ruleForm.appName"></el-input>
                  </el-form-item>
                  <el-form-item label="应用类型" prop="appCategory">
                    <el-select
                      v-model="ruleForm.appCategory"
                      placeholder="请选择应用类型"
                    >
                      <el-option
                        v-for="(item, index) in appliTypeData"
                        :key="index"
                        :label="item.dictLabel"
                        :value="item.dictLabel"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="应用服务端" prop="applicaServer">
                    <el-radio-group v-model="ruleForm.applicaServer">
                      <el-radio label="0">APP端</el-radio>
                      <el-radio label="1">web端</el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item label="交付方式" prop="delivery">
                    <el-radio-group v-model="ruleForm.delivery">
                      <el-radio label="0">Saas服务</el-radio>
                      <el-radio label="1">本地部署</el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item label="应用简介" prop="briefInto">
                    <el-input
                      v-model="ruleForm.briefInto"
                      type="textarea"
                      :rows="3"
                      resize="none"
                    ></el-input>
                  </el-form-item>
                  <el-form-item label="应用详情" prop="content">
                    <editor v-model="ruleForm.content" :min-height="192" />
                  </el-form-item>
                  <el-form-item label="应用封面" prop="appLogo">
                    <el-upload
                      :limit="1"
                      list-type="picture-card"
                      :headers="headers"
                      :action="uploadUrl"
                      :file-list="personalCardList"
                      :accept="accept"
                      :before-upload="handleBeforeUpload"
                      :on-preview="handlePersonalCardPreview"
                      :on-remove="handleRemove"
                      :on-success="handlePersonalCardSuccess"
                      :on-exceed="handelExceed"
                    >
                      <i class="el-icon-plus"></i>
                    </el-upload>
                  </el-form-item>
                  <el-form-item label="应用提供" prop="supply">
                    <el-input v-model="ruleForm.supply"></el-input>
                  </el-form-item>
                  <el-form-item label="联系人" prop="linkman">
                    <el-input v-model="ruleForm.linkman"></el-input>
                  </el-form-item>
                  <el-form-item label="联系方式" prop="phone">
                    <el-input
                      v-model="ruleForm.phone"
                      @input="
                        ruleForm.phone = ruleForm.phone.replace(/[^0-9.]/g, '')
                      "
                      maxlength="11"
                    ></el-input>
                  </el-form-item>
                </el-form>
              </div>
              <div v-show="active == 1">
                <div class="title">开发管理配置</div>
                <el-form
                  :model="ruleForm"
                  :rules="rules"
                  ref="appliForm1"
                  label-width="160px"
                  class="demo-ruleForm"
                >
                  <el-form-item label="服务器出口IP">
                    <el-input
                      v-model="ruleForm.serverIp"
                      @input="
                        ruleForm.serverIp = ruleForm.serverIp.replace(
                          /[^0-9.]/g,
                          ''
                        )
                      "
                    ></el-input>
                  </el-form-item>
                  <el-form-item label="网页端(Web)应用地址" prop="webUrl">
                    <el-input v-model="ruleForm.webUrl"></el-input>
                  </el-form-item>
                  <el-form-item
                    label="网页端(Web)体验地址"
                    prop="webexperienceUrl"
                  >
                    <el-input v-model="ruleForm.webexperienceUrl"></el-input>
                  </el-form-item>
                  <el-form-item
                    label="健康检查服务端地址"
                    prop="serverexamineUrl"
                  >
                    <el-input v-model="ruleForm.serverexamineUrl"></el-input>
                  </el-form-item>
                  <el-form-item label="开发联系人" prop="developmentPeople">
                    <el-input v-model="ruleForm.developmentPeople"></el-input>
                  </el-form-item>
                  <!-- <el-form-item label="获取测试token">
                    <div>点此获取</div>
                  </el-form-item> -->
                </el-form>
              </div>
              <div v-show="active == 2">
                <div class="title">商品规格信息</div>
                <div>
                  <el-form
                    :model="ruleForm"
                    :rules="rules"
                    ref="appliForm2"
                    label-width="180px"
                    class="demo-ruleForm"
                  >
                    <el-form-item label="规格" prop="spec">
                      <el-input v-model="ruleForm.spec"></el-input>
                    </el-form-item>
                    <el-form-item label="使用用户数" prop="userNumber">
                      <el-input
                        v-model="ruleForm.userNumber"
                        @input="
                          ruleForm.userNumber = ruleForm.userNumber.replace(
                            /[^0-9.]/g,
                            ''
                          )
                        "
                      ></el-input>
                    </el-form-item>
                    <el-form-item label="有效时间" prop="validTime">
                      <el-date-picker
                        v-model="ruleForm.validTime"
                        type="datetime"
                        placeholder="请选择有效时间"
                      >
                      </el-date-picker>
                    </el-form-item>
                    <div style="margin-top: 20px">
                      <div class="title">商品价格信息</div>
                      <div style="margin-top: 20px">
                        <el-form-item label="订货编码" prop="orderCode">
                          <el-input
                            v-model="ruleForm.orderCode"
                            @input="
                              ruleForm.orderCode = ruleForm.orderCode.replace(
                                /[^\w\.\/]/g,
                                ''
                              )
                            "
                          ></el-input>
                        </el-form-item>
                        <el-form-item label="商品原价（元）" prop="price">
                          <el-input
                            v-model="ruleForm.price"
                            @input="
                              ruleForm.price = ruleForm.price.replace(
                                /[^0-9.]/g,
                                ''
                              )
                            "
                          ></el-input>
                        </el-form-item>
                        <el-form-item
                          label="商品促销价（元）"
                          prop="promotionPrice"
                        >
                          <el-input
                            v-model="ruleForm.promotionPrice"
                            @input="
                              ruleForm.promotionPrice =
                                ruleForm.promotionPrice.replace(/[^0-9.]/g, '')
                            "
                          ></el-input>
                        </el-form-item>
                        <el-form-item label="商品分佣比例（%）">
                          <el-input
                            v-model="ruleForm.commissionRatio"
                            @input="
                              ruleForm.commissionRatio =
                                ruleForm.commissionRatio.replace(/[^0-9.]/g, '')
                            "
                          ></el-input>
                        </el-form-item>
                        <!-- <div class="buttonStyle" @click="addPrice">新增价格</div>
                          <div style="margin-top: 20px">
                            <el-table
                              :data="tableData"
                              style="width: 100%; mni-height: 200px"
                            >
                              <el-table-column prop="date" label="规格">
                              </el-table-column>
                              <el-table-column prop="name" label="订货编码">
                              </el-table-column>
                              <el-table-column prop="name" label="商品原价（元）">
                              </el-table-column>
                              <el-table-column prop="name" label="商品促销价（元）">
                              </el-table-column>
                              <el-table-column prop="name" label="商品分佣比例（%）">
                              </el-table-column>
                              <el-table-column label="操作">
                                <template>
                                  <div>删除</div>
                                </template>
                              </el-table-column>
                            </el-table>
                          </div> -->
                      </div>
                    </div>
                    <div style="margin-top: 20px">
                      <div class="title">商品参数介绍</div>
                      <div style="margin-top: 20px">
                        <el-form-item
                          label="服务咨询电话"
                          prop="consultingTelephone"
                        >
                          <el-input
                            v-model="ruleForm.consultingTelephone"
                            @input="
                              ruleForm.consultingTelephone =
                                ruleForm.consultingTelephone.replace(
                                  /[^0-9.]/g,
                                  ''
                                )
                            "
                          ></el-input>
                        </el-form-item>
                        <el-form-item
                          label="产品运营联系人手机号"
                          prop="operationTelephone"
                        >
                          <el-input
                            v-model="ruleForm.operationTelephone"
                            @input="
                              ruleForm.operationTelephone =
                                ruleForm.operationTelephone.replace(
                                  /[^0-9.]/g,
                                  ''
                                )
                            "
                          ></el-input>
                        </el-form-item>
                        <!-- <el-table
                      :data="shopData"
                      style="width: 100%; mni-height: 200px"
                    >
                      <el-table-column label="名称">
                        <template slot-scope="scoped">
                          <div v-if="scoped.row.type == 1">
                            {{ scoped.row.name }}
                          </div>
                          <div v-if="scoped.row.type == 2">
                            <el-input
                              v-model="scoped.row.name"
                              placeholder="请输入"
                            ></el-input>
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column label="内容">
                        <template slot-scope="scoped">
                          <el-input
                            v-model="scoped.row.content"
                            placeholder="请输入"
                          ></el-input>
                        </template>
                      </el-table-column>
                      <el-table-column label="操作">
                        <template slot-scope="scoped">
                          <div v-if="scoped.row.type == 1">-</div>
                          <div
                            style="color: #21C9B8; cursor: pointer"
                            v-if="scoped.row.type == 2"
                            @click="delShopData(scoped.row.id)"
                          >
                            删除
                          </div>
                        </template>
                      </el-table-column>
                    </el-table>
                    <div
                      style="color: #21C9B8; margin-top: 20px; cursor: pointer"
                      @click="addShopData"
                    >
                      新增参数
                    </div> -->
                      </div>
                    </div>
                  </el-form>
                  <!-- <div class="buttonStyle" @click="addSpecification">
                    新增规格
                  </div>
                  <div style="margin-top: 20px">
                    <el-table
                      :data="tableData"
                      style="width: 100%; mni-height: 200px"
                    >
                      <el-table-column prop="date" label="规格" width="180">
                      </el-table-column>
                      <el-table-column prop="name" label="规格信息" width="180">
                      </el-table-column>
                      <el-table-column label="操作">
                        <template>
                          <div>删除</div>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div> -->
                </div>
              </div>
            </div>
            <div class="submitStyle">
              <div v-show="active !== 0" class="buttonStyle" @click="prevStep">
                上一步
              </div>
              <div v-show="active !== 2" class="buttonStyle" @click="nextStep">
                下一步
              </div>
              <div
                v-show="active === 2"
                class="buttonStyle"
                @click="submitData"
              >
                提交
              </div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
    <el-dialog
      title="新增规格"
      :visible.sync="dialogVisible"
      width="30%"
      :before-close="handleClose"
    >
      <div>
        <el-form
          :model="ruleForm"
          :rules="rules"
          ref="ruleForm"
          label-width="80px"
          class="demo-ruleForm"
        >
          <el-form-item label="规格" prop="name">
            <el-input v-model="ruleForm.name"></el-input>
          </el-form-item>
          <el-form-item label="规格信息" prop="name">
            <el-input v-model="ruleForm.name"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogVisible = false"
          >确 定</el-button
        >
      </span>
    </el-dialog>
    <el-dialog
      title="新增价格"
      :visible.sync="dialogpriceVisible"
      width="30%"
      :before-close="handlepriceClose"
    >
      <div>
        <el-form
          :model="ruleForm"
          :rules="rules"
          ref="ruleForm"
          label-width="150px"
          class="demo-ruleForm"
        >
          <el-form-item label="规格" prop="name">
            <el-input v-model="ruleForm.name"></el-input>
          </el-form-item>
          <el-form-item label="订货编码" prop="name">
            <el-input v-model="ruleForm.name"></el-input>
          </el-form-item>
          <el-form-item label="商品原价（元）" prop="name">
            <el-input v-model="ruleForm.name"></el-input>
          </el-form-item>
          <el-form-item label="商品促销价（元）" prop="name">
            <el-input v-model="ruleForm.name"></el-input>
          </el-form-item>
          <el-form-item label="商品分佣比例（%）" prop="name">
            <el-input v-model="ruleForm.name"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogpriceVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogpriceVisible = false"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
// import { list, delOperlog, cleanOperlog } from "@/api/system/operlog";
import UserMenu from "../components/userMenu.vue";
import { getToken } from "@/utils/auth";
import { appliAdd, appliEdit, appliDetail } from "@/api/appliMarket";
import store from "@/store";

export default {
  name: "Operlog",
  dicts: ["sys_oper_type", "sys_common_status"],
  components: { UserMenu },
  data() {
    return {
      active: 0,
      ruleForm: {
        appName: "",
        appCategory: "",
        applicaServer: "",
        delivery: "",
        briefInto: "",
        content: "",
        appLogo: "",
        supply: "",
        linkman: "",
        phone: "",
        serverIp: "",
        webUrl: "",
        webexperienceUrl: "",
        serverexamineUrl: "",
        developmentPeople: "",
        spec: "",
        userNumber: "",
        validTime: "",
        orderCode: "",
        price: "",
        promotionPrice: "",
        commissionRatio: "",
      },
      rules: {
        appName: [
          { required: true, message: "请输入应用名称", trigger: "blur" },
        ],
        appCategory: [
          { required: true, message: "请选择应用类型", trigger: "change" },
        ],
        applicaServer: [
          { required: true, message: "请选择应用服务端", trigger: "change" },
        ],
        delivery: [
          { required: true, message: "请选择交付方式", trigger: "change" },
        ],
        briefInto: [
          { required: true, message: "请输入应用简介", trigger: "blur" },
        ],
        content: [
          { required: true, message: "请输入应用详情", trigger: "blur" },
        ],
        appLogo: [
          { required: true, message: "请上传应用封面图片", trigger: "change" },
        ],
        supply: [
          { required: true, message: "请输入应用提供", trigger: "blur" },
        ],
        linkman: [{ required: true, message: "请输入联系人", trigger: "blur" }],
        phone: [{ required: true, message: "请输入联系方式", trigger: "blur" }],
        webUrl: [
          {
            required: true,
            message: "请输入网页端(Web)应用地址",
            trigger: "blur",
          },
        ],
        webexperienceUrl: [
          {
            required: true,
            message: "请输入网页端(Web)体验地址",
            trigger: "blur",
          },
        ],
        serverexamineUrl: [
          {
            required: true,
            message: "请输入健康检查服务端地址",
            trigger: "blur",
          },
        ],
        developmentPeople: [
          {
            required: true,
            message: "请输入开发联系人",
            trigger: "blur",
          },
        ],

        spec: [
          {
            required: true,
            message: "请输入规格",
            trigger: "blur",
          },
        ],
        userNumber: [
          {
            required: true,
            message: "请输入使用用户数",
            trigger: "blur",
          },
        ],
        validTime: [
          {
            required: true,
            message: "请选择有效时间",
            trigger: "change",
          },
        ],
        orderCode: [
          {
            required: true,
            message: "请输入订货编码",
            trigger: "blur",
          },
        ],
        price: [
          {
            required: true,
            message: "请输入商品原价",
            trigger: "blur",
          },
        ],
        promotionPrice: [
          {
            required: true,
            message: "请输入商品促销价",
            trigger: "blur",
          },
        ],
        consultingTelephone: [
          {
            required: true,
            message: "请输入服务咨询电话",
            trigger: "blur",
          },
        ],
        operationTelephone: [
          {
            required: true,
            message: "请输入产品运营联系人手机号",
            trigger: "blur",
          },
        ],
        // date1: [
        //   {
        //     type: "date",
        //     required: true,
        //     message: "请选择日期",
        //     trigger: "change",
        //   },
        // ],
        // date2: [
        //   {
        //     type: "date",
        //     required: true,
        //     message: "请选择时间",
        //     trigger: "change",
        //   },
        // ],
      },
      personalCardList: [],
      tableData: [
        {
          date: "2016-05-02",
          name: "王小虎",
          address: "上海市普陀区金沙江路 1518 弄",
        },
        {
          date: "2016-05-04",
          name: "王小虎",
          address: "上海市普陀区金沙江路 1517 弄",
        },
        {
          date: "2016-05-01",
          name: "王小虎",
          address: "上海市普陀区金沙江路 1519 弄",
        },
        {
          date: "2016-05-03",
          name: "王小虎",
          address: "上海市普陀区金沙江路 1516 弄",
        },
      ],
      shopData: [
        {
          id: 1,
          name: "服务咨询电话",
          content: "",
          type: 1,
        },
        {
          id: 2,
          name: "产品运营联系人手机号",
          content: "",
          type: 1,
        },
      ],
      dialogVisible: false,
      dialogpriceVisible: false,
      uploadUrl: process.env.VUE_APP_BASE_API + "/file/upload", //上传地址
      accept: ".jpg, .jpeg, .png, .bmp",
      headers: { Authorization: "Bearer " + getToken() },
      appliTypeData: [
        {
          dictValue: "1",
          dictLabel: "研发设计",
        },
        {
          dictValue: "2",
          dictLabel: "生产制造",
        },
        {
          dictValue: "3",
          dictLabel: "运营管理",
        },
        {
          dictValue: "4",
          dictLabel: "质量管控",
        },
        {
          dictValue: "5",
          dictLabel: "仓储物流",
        },
        {
          dictValue: "6",
          dictLabel: "安全生产",
        },
        {
          dictValue: "7",
          dictLabel: "节能减排",
        },
        {
          dictValue: "8",
          dictLabel: "运维服务",
        },
      ],
    };
  },
  created() {
    this.getDetail();
  },
  methods: {
    getDetail() {
      let id = this.$route.query.id;
      if (id) {
        let params = {
          id,
          userId: store.getters.userId,
        };
        appliDetail(params).then((res) => {
          if (res.code === 200) {
            this.ruleForm = res.data;
            this.personalCardList = [{ url: this.ruleForm.appLogo }];
          }
        });
      }
    },
    // 操作日志类型字典翻译
    typeFormat(row, column) {
      return this.selectDictLabel(
        this.dict.type.sys_oper_type,
        row.businessType
      );
    },
    /** 详细按钮操作 */
    handleView(row) {
      this.open = true;
      this.form = row;
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const operIds = row.operId || this.ids;
      this.$modal
        .confirm('是否确认删除日志编号为"' + operIds + '"的数据项？')
        .then(function () {
          return delOperlog(operIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    handleCurrentChange(pageNum) {
      this.queryParams.pageNum = pageNum;
      this.getList();
    },
    prevStep() {
      this.active--;
    },
    nextStep() {
      if (this.active == 0) {
        this.$refs.appliForm.validate((valid) => {
          if (valid) {
            console.log(this.active, "执行了吗1----------");
            this.active++;
            // this.$nextTick(() => {
            //   this.$refs.appliForm1.clearValidate(); // 只清除清除验证
            // });
          } else {
            console.log("error submit!!");
            return false;
          }
        });
      } else if (this.active == 1) {
        this.$refs.appliForm1.validate((valid) => {
          if (valid) {
            console.log(this.active, "执行了吗2----------");
            this.active++;
          } else {
            console.log("error submit!!");
            return false;
          }
        });
      }
    },
    addSpecification() {
      this.dialogVisible = true;
    },
    handleClose() {
      this.dialogVisible = false;
    },
    addPrice() {
      this.dialogpriceVisible = true;
    },
    handlepriceClose() {
      this.dialogpriceVisible = false;
    },
    // 产品照片上传之前的钩子
    handleBeforeUpload(file) {
      let { name, type, size } = file;
      let typeList = this.accept
        .split(",")
        .map((item) => item.trim().toLowerCase().substr(1));
      let dotIndex = name.lastIndexOf(".");
      // 文件类型校验
      if (dotIndex === -1) {
        this.$message.error("请上传正确格式的文件");
        return false;
      } else {
        let suffix = name.substring(dotIndex + 1);
        if (typeList.indexOf(suffix.toLowerCase()) === -1) {
          this.$message.error("请上传正确格式的文件");
          return false;
        }
      }
      // 文件上传大小限制
      if (size > 1048576 * 20) {
        this.$message.error("文件大小不能超过20M！");
        return false;
      }
    },
    // 点击产品照片
    handlePersonalCardPreview(file) {
      this.imageUrl = file.url;
      this.imgVisible = true;
    },
    // 删除产品照片
    handleRemove(file, fileList) {
      this.personalCardList = [];
      this.ruleForm.appLogo = "";
    },
    handlePersonalCardSuccess(res, file, fileList) {
      if (!this.personalCardList) {
        this.personalCardList = [];
        this.ruleForm.appLogo = "";
      }
      //此处写上传oss成功之后的逻辑
      if (res.code == 200) {
        this.personalCardList.push(res.data);
        this.ruleForm.appLogo = res.data.url;
      }
    },
    handelExceed(file, fileList) {
      this.$message.warning("超出图片个数限制");
    },
    addShopData() {
      let id = this.shopData.length + 1;
      let data = {
        id,
        name: "",
        content: "",
        type: 2,
      };
      this.shopData.push(data);
    },
    delShopData(id) {
      this.shopData.forEach((item, index) => {
        if (item.id === id) {
          this.shopData.splice(index, 1);
        }
      });
    },
    submitData() {
      this.$refs.appliForm2.validate((valid) => {
        if (valid) {
          if (this.$route.query.id) {
            appliEdit(this.ruleForm).then((res) => {
              if (res.code === 200) {
                this.$message.success("操作成功!");
                this.$router.push({
                  path: "/user/application",
                });
              }
            });
          } else {
            appliAdd(this.ruleForm).then((res) => {
              if (res.code === 200) {
                this.$message.success("操作成功!");
                this.$router.push({
                  path: "/user/application",
                });
              }
            });
          }
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.app-container {
  background: #f4f5f9;
}
.container {
  width: 100%;
  background: #ffffff;
  padding: 40px;
}
.content {
  width: 100%;
  // background: #ffffff;
  .stepsStyle {
    padding: 0 13%;
  }
  .currentContent {
    margin-top: 30px;
    padding-left: 5%;
    padding-right: 40%;
  }
  .title {
    font-size: 22px;
    font-weight: 600;
    margin-bottom: 30px;
    margin-left: 20px;
  }
  .submitStyle {
    width: 100%;
    display: flex;
    justify-content: right;
  }
  .buttonStyle {
    width: 100px;
    padding: 10px;
    background: #21c9b8;
    color: #ffffff;
    text-align: center;
    cursor: pointer;
    border-radius: 4px;
    margin-right: 20px;
  }
}
</style>

/*
 * @Author: zhc
 * @Date: 2023-02-12 10:15:37
 * @LastEditTime: 2023-02-17 17:23:54
 * @Description:
 * @LastEditors: zhc
 */
import request from "@/utils/request";

// 供给列表查询
export function getApplyList(params) {
  return request({
    url: "/system/supply/secret/list",
    method: "get",
    params: params,
  });
}
// 供给详情
export function getApplyDetail(id) {
  return request({
    url: `/system/supply/secret/` + id,
    method: "get",
  });
}

// 请离、管理员转移按钮权限判断
export function checkManagerAuth() {
  return request({
    url: "/system/company/apply/checkManagerAuth",
    method: "get",
  });
}

// 验证手机验证码
export function checkSmsCode(params) {
  return request({
    url: "/system/company/apply/checkSmsCode",
    method: "get",
    params: params,
  });
}
// 新增需求
export function createApply(params) {
  return request({
    url: "/system/supply",
    method: "post",
    data: params,
  });
}
export function editApply(params) {
  return request({
    url: "/system/supply",
    method: "put",
    data: params,
  });
}
// 拒绝（1.发送系统消息）
export function applyRefuse(userId) {
  return request({
    url: "/system/company/apply/refuse",
    method: "post",
    data: { userId: userId },
  });
}
// 门户PC-保存申报信息-草稿-提审
export function editPolicyApply(params) {
  return request({
    url: "/system/policyApply/submit",
    method: "post",
    data: params,
  });
}
// 请离
export function askResignation(params) {
  return request({
    url: "/system/company/apply/askResignation",
    method: "post",
    data: params,
  });
}

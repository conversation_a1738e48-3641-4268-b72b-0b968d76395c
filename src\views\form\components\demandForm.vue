<template>
  <div class="demand-form">
    <el-form ref="form" :rules="rules" :model="form" label-position="top">
      <el-form-item prop="demandTitle" label="需求标题">
        <el-input
          v-model="form.demandTitle"
          maxlength="50"
          show-word-limit
          placeholder="请输入标签"
        ></el-input>
      </el-form-item>
      <el-form-item prop="demandType">
        <div class="label-item" slot="label">
          <span>需求类型</span>
          <span class="extra">（可按需求产品+应用行业+应用领域进行描述）</span>
        </div>
        <el-checkbox-group
          v-model="form.demandType"
          placeholder="请选择"
          clearable
        >
          <el-checkbox
            v-for="dict in dict.type.demand_type"
            :key="dict.value"
            :label="dict.value"
            :value="dict.value"
            >{{ dict.label }}</el-checkbox
          >
        </el-checkbox-group>
      </el-form-item>
      <el-form-item prop="summary" label="需求描述">
        <el-input
          type="textarea"
          v-model="form.summary"
          maxlength="500"
          rows="6"
          show-word-limit
          placeholder="请输入需求描述"
        ></el-input>
        <div class="extra-content">
          <div class="extra-content-header">
            <el-button @click="handleKeywordList" size="small" type="primary"
              >生成关键词</el-button
            >
            <span class="tip">生成关键词有利于实现精准匹配哦！</span>
          </div>
          <div v-if="form.keywords.length > 0" class="extra-content-body">
            <el-tag
              :key="`${tag}_${index}`"
              v-for="(tag, index) in form.keywords"
              closable
              size="small"
              disable-transitions
              @close="handleClose(tag)"
            >
              {{ tag }}
            </el-tag>
          </div>
        </div>
      </el-form-item>
      <el-form-item prop="applicationArea" label="应用领域">
        <el-select
          v-model="form.applicationArea"
          filterable
          multiple
          allow-create
          style="width: 100%"
          placeholder="请选择"
        >
          <el-option
            v-for="item in dict.type.application_area"
            :key="item.value"
            :label="item.label"
            :value="item.label"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="产品照片" prop="scenePicture">
        <ImageUpload v-model="form.scenePicture" />
      </el-form-item>
      <el-form-item label="展示限制" prop="displayRestrictions">
        <el-select
          v-model="form.displayRestrictions"
          placeholder="请选择"
          style="width: 100%"
          clearable
        >
          <el-option
            v-for="dict in dict.type.display_restrictions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="公司名称" prop="companyName">
        <el-input
          disabled
          v-model="form.companyName"
          placeholder="请先绑定公司"
        ></el-input>
      </el-form-item>
      <el-form-item label="联系人" prop="contactsName">
        <el-input
          disabled
          v-model="form.contactsName"
          placeholder="请先维护联系人"
        ></el-input>
      </el-form-item>
      <el-form-item label="联系方式" prop="contactsMobile">
        <el-input
          disabled
          v-model="form.contactsMobile"
          placeholder="请先维护联系方式"
        ></el-input>
      </el-form-item>
      <el-form-item class="footer-submit">
        <el-button @click.once="onCancel">取消</el-button>
        <el-button @click="onSubmit('0')" type="primary" plain
          >暂存草稿</el-button
        >
        <el-button type="primary" @click="onSubmit('1')">发布</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { demandAdd, keywordList } from "@/api/zhm";
import cache from "@/plugins/cache";

export default {
  name: "demandForm",
  dicts: ["demand_type", "display_restrictions", "application_area"],
  data() {
    const { user } = this.$store.state;
    return {
      loading: false,
      form: {
        // 需求标题
        demandTitle: undefined,
        // 需求类型
        demandType: [],
        // 需求描述
        summary: undefined,
        // 关键词
        keywords: [],
        // 应用领域
        applicationArea: [],
        // 场景图片
        scenePicture: [],
        // 展示限制
        displayRestrictions: undefined,
        // 公司名称
        companyName: user.companyName,
        // 联系人
        contactsName: user.name,
        // 联系电话
        contactsMobile: user.tel,
        auditStatus: "1",
        displayStatus: "1",
        publisherName: user.name,
        publisherMobile: user.tel,
        businessNo: user.bussinessNo,
      },
      rules: {
        demandTitle: [
          { required: true, message: "请输入需求标题", trigger: "blur" },
        ],
        demandType: [
          { required: true, message: "请选择需求类型", trigger: "blur" },
        ],
        summary: [
          { required: true, message: "请输入需求描述", trigger: "blur" },
        ],
        applicationArea: [
          { required: true, message: "请选择应用领域", trigger: "blur" },
        ],
        displayRestrictions: [
          { required: true, message: "请选择展示限制", trigger: "blur" },
        ],
        companyName: [
          { required: true, message: "请维护公司名称", trigger: "blur" },
        ],
        contactsName: [
          { required: true, message: "请维护联系人", trigger: "blur" },
        ],
        contactsMobile: [
          { required: true, message: "请维护联系电话", trigger: "blur" },
        ],
      },
    };
  },
  methods: {
    init() {
      const data = cache.local.getJSON("demand_data");
      if (data) {
        this.form = data;
      }
    },
    onCancel() {
      this.$router.back();
    },
    onSave() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          cache.local.setJSON("demand_data", this.form);
          this.$message.success("暂存成功");
        }
      });
    },
    onSubmit(status) {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.loading = true;
          const {
            demandType,
            keywords,
            applicationArea,
            scenePicture,
            ...rest
          } = this.form;
          const data = {
            ...rest,
            auditStatus: status,
          };
          if (demandType.length > 0) {
            data["demandType"] = demandType.join();
          }
          if (keywords.length > 0) {
            data["keywords"] = keywords.join();
          }
          if (applicationArea.length > 0) {
            data["applicationArea"] = applicationArea.join();
          }
          if (scenePicture.length > 0) {
            data["scenePicture"] = JSON.stringify(scenePicture);
          }

          demandAdd(data)
            .then((res) => {
              const { code, msg } = res;
              if (code === 200) {
                cache.local.remove("demand_data");
                this.$message.success("发布成功");
                this.$router.back();
              } else {
                this.$message.error(msg || "发布失败");
              }
            })
            .finally(() => (this.loading = false));
        }
      });
    },
    handleKeywordList() {
      const { summary } = this.form;
      if (summary) {
        keywordList(summary).then((res) => {
          const { code, data, msg } = res;
          if (code === 200) {
            this.form.keywords = data;
          } else {
            this.$message.error(msg);
          }
        });
      } else {
        this.$message.warning("请输入需求描述");
      }
    },

    handleClose(tag) {
      console.log("tag", tag);
      this.form.keywords = this.form.keywords.filter((item) => item !== tag);
    },
  },
};
</script>

<style lang="scss" scoped>
.demand-form {
  width: 676px;
  .label-item {
    display: flex;
    flex-direction: row;
    align-items: center;
    .title {
      font-size: 18px;
      font-weight: 500;
      color: #333333;
      line-height: 18px;
    }
    .extra {
      font-size: 14px;
      font-weight: 400;
      color: #999999;
      line-height: 14px;
    }
  }
  .extra-content {
    padding: 12px 0;
    &-header {
      display: flex;
      flex-direction: row;
      align-items: center;
      .tip {
        font-size: 14px;
        font-weight: 400;
        color: #999999;
        line-height: 14px;
        margin-left: 12px;
      }
    }
    &-body {
      padding-top: 6px;
      .el-tag {
        margin-right: 12px;
        &:last-child {
          margin-right: 0;
        }
      }
    }
  }
  ::v-deep.el-form-item__label {
    font-size: 18px;
    font-weight: 500;
    color: #333333;
    line-height: 18px;
    margin-bottom: 12px;
    padding: 0;
  }
  .el-checkbox {
    font-size: 14px;
    font-weight: 400;
    color: #262626;
    line-height: 18px;
    margin-right: 28px;
  }
  .footer-submit {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    margin-top: 40px;
    .el-button {
      width: 160px;
    }
  }
}
</style>

import Cookies from "js-cookie";

const TokenKey = "Web-Token";

const ExpiresInKey = "Admin-Expires-In";

const TicketKey = "ticket";

export function getToken() {
  return Cookies.get(TokenKey);
}

export function setToken(token) {
  return Cookies.set(Token<PERSON>ey, token);
}

export function removeToken() {
  return Cookies.remove(TokenKey);
}

export function setTicket(ticket) {
  return Cookies.set(TicketKey, ticket);
}

export function getTicket(ticket) {
  return Cookies.get(TicketKey, ticket);
}

export function removeTicket() {
  return Cookies.remove(TicketKey);
}

export function getExpiresIn() {
  return Cookies.get(ExpiresInKey) || -1;
}

export function setExpiresIn(time) {
  return Cookies.set(ExpiresInKey, time);
}

export function removeExpiresIn() {
  return Cookies.remove(ExpiresInKey);
}

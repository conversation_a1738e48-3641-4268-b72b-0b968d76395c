<template>
  <div>
    <deviceSharing v-if="currentIndex == 0"></deviceSharing>
    <workshopSharing v-if="currentIndex == 1"></workshopSharing>
    <orderSharing v-if="currentIndex == 2"></orderSharing>
  </div>
</template>
<script>
import deviceSharing from "./components/deviceSharing";
import workshopSharing from "./components/workshopSharing.vue";
import orderSharing from "./components/orderSharing.vue";
export default {
  name: "manufacturingSharing",
  components: { deviceSharing, workshopSharing, orderSharing },
  data() {
    return {
      currentIndex: 0,
    };
  },
  watch: {
    "$route.query.index": {
      handler(val) {
        this.currentIndex = val || 0;
      },
      deep: true,
    },
  },

  methods: {},
  created() {
    this.currentIndex = this.$route.query.index || 0;
  },
};
</script>
<style></style>

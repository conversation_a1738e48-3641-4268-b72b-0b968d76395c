<template>
  <div class="content">
    <div class="content_banner">
      <div style="height: 37px">动态资讯</div>
      <div style="height: 33px; margin-top: 31px;font-size: 18px;">洞察行业趋势，指引前行方向</div>
      <div class="activity-title-content">
        <div class="activity-search-box">
          <el-form ref="form" class="activity-search-form">
            <el-form-item>
              <el-input v-model="keywords" placeholder="请输入搜索内容" class="activity-search-input">
                <el-button slot="append" class="activity-search-btn" @click="onSearch">搜索</el-button>
              </el-input>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div class="hot_search">
        <span>热门搜索：</span>
        <span class="hot_search_item" @click="searchHot('物联网')">物联网</span>
        <span class="hot_search_item" @click="searchHot('数字化')">数字化</span>
        <span class="hot_search_item" @click="searchHot('智能制造')">智能制造</span>
        <span class="hot_search_item" @click="searchHot('活动')">活动</span>
        <span class="hot_search_item" @click="searchHot('入选')">入选</span>
        <span class="hot_search_item" @click="searchHot('方案')">方案</span>
        <span class="hot_search_item" @click="searchHot('荣誉')">荣誉</span>
      </div>
    </div>
    <div class="card-container">
      <div class="list-top">
        <div class="tip" style="background: transparent;height: auto;">
          <div style="padding-top:50px ">
            <span :class="{ active: type == '' }" @click="handleType('')">全部</span>
            <span v-for="(item, index) in options" :class="{ active: item.dictValue == type }"
              @click="handleType(item)">{{
                item.dictLabel }}</span>
            <span style="float: right;margin-top: 6.5px !important;" class="sort">
            </span>
          </div>
        </div>
      </div>
    </div>

    <div class="background" style="position:relative;">
      <div class="list" v-if="list.length > 0">
        <div class="info-box" v-for="item in list" @click="goto(item)">
          <div style="width: 129px;height: 129px;">
            <img :src="item.newsInformationImg ? item.newsInformationImg : gjImgdefault"
              style="width: 100%;height: 100%;" />
          </div>
          <div style="width: 414px;">
            <h3 class="omit">{{ item.newsInformationName || '' }}</h3>
            <p class="omit">{{ item.newsInformationIntroduction }}</p>
            <span>{{ item.newsInformationDate }}</span>
          </div>
        </div>

      </div>
      <div v-else class="list">
        <el-empty description="暂无数据"></el-empty>
      </div>
      <div class="pagination">
        <el-pagination background layout="prev, pager, next" :total="total"
          @current-change="currentChange"></el-pagination>
      </div>
    </div>

  </div>
</template>
<script>
import { listData } from "@/api/system/dict/data";
import { getNewsInformationList } from "@/api/aboutUs";
import "@/assets/styles/index.css";
import "@/assets/styles/common.css";

export default {
  name: "demandHall",
  data() {
    return {
      loading: false,
      pageNum: 1,
      pageSize: 10,
      total: 0,
      keywords: "",
      form: {},
      type: "",
      supplyType: "",
      supplyList: [
        {
          title: "普拉多 2025款 2.4T 旗舰VX版 5座",
          url: require("../../../assets/demand/xqimgdefault.png"),
          appliArea: "",
          requireType: "其它",
          desc: "中大型SUV，油电混合，最大功率(kW)243，最大扭矩(N·m)630，变速箱8挡手自一体，长*宽*高(mm)4935*1980*1935，车身结构5门5座SUV， 最高车速(km/h)170",
          publishTime: "2025-02-28 09:49:21",
        },
        {
          title: "普拉多 2025款 2.4T 旗舰VX版 5座",
          url: require("../../../assets/demand/xqimgdefault.png"),
          appliArea: "",
          requireType: "其它",
          desc: "中大型SUV，油电混合，最大功率(kW)243，最大扭矩(N·m)630，变速箱8挡手自一体，长*宽*高(mm)4935*1980*1935，车身结构5门5座SUV， 最高车速(km/h)170",
          publishTime: "2025-02-28 09:49:21",
        },
        {
          title: "普拉多 2025款 2.4T 旗舰VX版 5座",
          url: require("../../../assets/demand/xqimgdefault.png"),
          appliArea: "",
          requireType: "其它",
          desc: "中大型SUV，油电混合，最大功率(kW)243，最大扭矩(N·m)630，变速箱8挡手自一体，长*宽*高(mm)4935*1980*1935，车身结构5门5座SUV， 最高车速(km/h)170",
          publishTime: "2025-02-28 09:49:21",
        },
        {
          title: "普拉多 2025款 2.4T 旗舰VX版 5座",
          url: require("../../../assets/demand/xqimgdefault.png"),
          appliArea: "",
          requireType: "其它",
          desc: "中大型SUV，油电混合，最大功率(kW)243，最大扭矩(N·m)630，变速箱8挡手自一体，长*宽*高(mm)4935*1980*1935，车身结构5门5座SUV， 最高车速(km/h)170",
          publishTime: "2025-02-28 09:49:21",
        },
        {
          title: "普拉多 2025款 2.4T 旗舰VX版 5座",
          url: require("../../../assets/demand/xqimgdefault.png"),
          appliArea: "",
          requireType: "其它",
          desc: "中大型SUV，油电混合，最大功率(kW)243，最大扭矩(N·m)630，变速箱8挡手自一体，长*宽*高(mm)4935*1980*1935，车身结构5门5座SUV， 最高车速(km/h)170",
          publishTime: "2025-02-28 09:49:21",
        },
        {
          title: "普拉多 2025款 2.4T 旗舰VX版 5座",
          url: require("../../../assets/demand/xqimgdefault.png"),
          appliArea: "",
          requireType: "其它",
          desc: "中大型SUV，油电混合，最大功率(kW)243，最大扭矩(N·m)630，变速箱8挡手自一体，长*宽*高(mm)4935*1980*1935，车身结构5门5座SUV， 最高车速(km/h)170",
          publishTime: "2025-02-28 09:49:21",
        },
        {
          title: "普拉多 2025款 2.4T 旗舰VX版 5座",
          url: require("../../../assets/demand/xqimgdefault.png"),
          appliArea: "",
          requireType: "其它",
          desc: "中大型SUV，油电混合，最大功率(kW)243，最大扭矩(N·m)630，变速箱8挡手自一体，长*宽*高(mm)4935*1980*1935，车身结构5门5座SUV， 最高车速(km/h)170",
          publishTime: "2025-02-28 09:49:21",
        },
        {
          title: "普拉多 2025款 2.4T 旗舰VX版 5座",
          url: require("../../../assets/demand/xqimgdefault.png"),
          appliArea: "",
          requireType: "其它",
          desc: "中大型SUV，油电混合，最大功率(kW)243，最大扭矩(N·m)630，变速箱8挡手自一体，长*宽*高(mm)4935*1980*1935，车身结构5门5座SUV， 最高车速(km/h)170",
          publishTime: "2025-02-28 09:49:21",
        },
        {
          title: "普拉多 2025款 2.4T 旗舰VX版 5座",
          url: require("../../../assets/demand/xqimgdefault.png"),
          appliArea: "",
          requireType: "其它",
          desc: "中大型SUV，油电混合，最大功率(kW)243，最大扭矩(N·m)630，变速箱8挡手自一体，长*宽*高(mm)4935*1980*1935，车身结构5门5座SUV， 最高车速(km/h)170",
          publishTime: "2025-02-28 09:49:21",
        },
        {
          title: "普拉多 2025款 2.4T 旗舰VX版 5座",
          url: require("../../../assets/demand/xqimgdefault.png"),
          appliArea: "",
          requireType: "其它",
          desc: "中大型SUV，油电混合，最大功率(kW)243，最大扭矩(N·m)630，变速箱8挡手自一体，长*宽*高(mm)4935*1980*1935，车身结构5门5座SUV， 最高车速(km/h)170",
          publishTime: "2025-02-28 09:49:21",
        },
      ],
      plateSecondLevelCode: '',
      sort: 'desc',
      options: [],
      list: [],
      gjImgdefault: require('@/assets/solution/gjimgdefault.png'),

    };
  },
  created() {
    this.getDicts(); // 供给类型
    this.getList();
  },
  methods: {
    /** 查询字典数据列表 */
    getDicts() {
      let params = { dictType: "sys_information" };
      listData(params).then((res) => {
        if (res.code === 200) {
          this.options = res.rows
        }
      });
    },
    handleType(val) {
      if (val != '') {
        this.type = val.dictValue
        this.plateSecondLevelCode = val.dictValue
      } else {
        this.type = val
        this.plateSecondLevelCode = val
      }
      this.onSearch();
    },
    getList() {
      this.loading = true;
      let params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        keywords: this.keywords,
        newsInformationPlateId: this.type,
        sort: this.sort == 'asc' ? 'news_information_date asc' : 'news_information_date desc',
      };
      getNewsInformationList(params).then((res) => {
        if (res.code === 200) {
          this.list = res.rows;
          this.total = res.total;
          this.loading = false;
        }
      });
    },
    currentChange(val) {
      this.pageNum = val;
      this.getList()
    },
    onSearch() {
      this.pageNum = 1;
      this.getList();
    },

    goDetail(id) {
      this.$router.push("/supplyDetail?id=" + id);
    },
    initPage() {
      this.getList();
    },
    refresh() {
      this.pageNum = 1;
      this.supplyType = "";
      this.techType = "";
      this.achieveStage = "";
      this.cooperationMode = "";
      this.getList();
    },
    searchHot(val) {
      this.keywords = val
      this.onSearch()
    },
    goto(item){
      this.$router.push({path: '/dynamicInfoDetail', query: {id: item.newsInformationId}})
    }
  },
};
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  padding-bottom: 60px;
}

.content_banner {
  width: 100%;
  height: 300px;
  background-image: url("../../../assets/release/banner.png");
  background-size: 100% 100%;
  text-align: center;
  margin: 0 auto;
  padding-top: 71px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  font-size: 40px;
  color: #000;
}

.hot_search {
  font-size: 14px;
  color: #000;

  .hot_search_item {
    margin-right: 20px;
    color: #000;
    cursor: pointer;
  }
}

.activity-title-content {
  width: 100%;

  // background-color: #fff;
  .activity-title-box {
    width: 336px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 60px 0 40px;

    .activity-title {
      font-size: 40px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #333;
      line-height: 40px;
      padding: 0 40px;
    }

    .activity-divider {
      width: 48px;
      height: 4px;
      background: #21c9b8;
    }
  }

  .activity-search-box {
    margin-top: 20px;

    .activity-search-form {
      text-align: center;

      .activity-search-input {
        width: 792px;
        height: 54px;

        .activity-search-btn {
          width: 100px;
        }
      }
    }
  }
}

.pageStyle {
  margin-top: 60px;
  width: 100%;
  text-align: center;
}
</style>
<style lang="scss">
.activity-search-input {
  .el-input__inner {
    height: 54px;
    background: #fff;
    border-radius: 27px 0 0 27px;
    border: 1px solid #d9d9d9;
    font-size: 16px;
    font-family: PingFangSC-Regular, PingFang SC;
    line-height: 24px;
    padding-left: 30px;
  }

  .el-input-group__append {
    border-radius: 0px 100px 100px 0px;
    background: #21c9b8;
    font-size: 16px;
    font-family: PingFangSC-Regular, PingFang SC;
    color: #fff;
    line-height: 24px;
  }
}

.none-class {
  text-align: center;
  padding: 8% 0;

  .text {
    font-size: 14px;
    font-weight: 400;
    color: #999999;
    line-height: 14px;
  }
}
</style>

<style lang="scss" scoped>
.list-top {}

.list .info-box h3.omit {
  line-height: 25px;
  width: 100%;
  height: 50px;
  color: #333;
  font-size: 18px;
  font-weight: 400;
  color: #333;
  margin-top: 0px;
  margin-bottom: 10px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.list .info-box:hover h3.omit {
  color: #428AFA;
}

.list .info-box p.omit {
  color: rgba(153, 153, 153, 1);
  font-size: 12px;
  text-align: left;
  line-height: 20px;
}

.list .info-box span {
  display: inline-block;
  float: left;
  line-height: 12px;
  color: rgba(153, 153, 153, 1);
  font-size: 12px;
  padding-top: 12px;
}

.list-top .tip span {
  display: inline-block;
  height: 30px;
  line-height: 30px;
  border-radius: 4px;
  margin-right: 15px;
  padding: 0px 20px;
  cursor: pointer;
}

.list-top .tip span.active {
  background-color: rgb(215, 249, 253);
  color: #21c9b8;
}

.list .info-box {
  width: 603px;
  background-color: rgba(255, 255, 255, 1);
  display: inline-block;
  text-align: left;
  margin-bottom: 20px;
  cursor: pointer;
  text-align: left;
  font-size: 0px;
}

.el-pagination.is-background .el-pager li:not(.disabled).active,
.el-pagination.is-background .el-pager li:not(.disabled).hover {
  border-color: #21c9b8;
  color: #21c9b8;
}

.el-pagination.is-background .el-pager li:hover {
  color: #21c9b8;
}

.number:hover {
  color: #21c9b8;
}
</style>

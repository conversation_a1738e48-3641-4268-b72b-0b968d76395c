<template>
  <div class="content">
    <div class="content_banner">
      <div style="height: 37px">我有意向</div>
      <div style="height: 33px; margin-top: 21px">I have intentions</div>
    </div>
    <div class="card-container card-content">
      <!-- 左侧 -->
      <div class="card_left">
        <div class="imgStyle">
          <img style="width: 100%; height: 100%" src="../../../assets/device/ceshi.png" alt="" />
        </div>
        <div class="title">{{ form.title }}</div>
        <div style="display: flex; align-items: center; margin-top: 15px">
          <div class="publishTimeStyle">发布时间：{{ updateTime }}</div>
          <div class="detailStyle" @click="goDetail">查看详情 >></div>
        </div>
      </div>
      <!-- 右侧 -->
      <div class="card_right">
        <div>
          <span class="resourceType">资源类型：</span>
          <span class="resourceValue">{{ form.fieldName }}</span>
        </div>
        <div style="margin-top: 20px">
          <span class="resourceType">资源名称：</span>
          <span class="resourceValue">{{ form.title }}</span>
        </div>
        <div style="margin-top: 20px">
          <el-form ref="form" :rules="rules" :model="form" label-position="top">
            <el-form-item label="承接量：" prop='quantity'>
              <el-input v-model="form.quantity" type="number" placeholder="请输入"></el-input>
            </el-form-item>
            <el-form-item label="报价" prop="sum">
              <el-table :data="[1]">
                <el-table-column label="含税单价" align="center">
                  <template slot-scope="scope">
                    <div style="height: 50px;">
                      <el-form-item prop="price">
                        <el-input v-model="form.price" type="number" @change="numChange"></el-input>
                      </el-form-item>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="税率" align="center">
                  <template slot-scope="scope">
                    <div style="height: 50px;">
                      <el-form-item prop="rate">
                        <el-input v-model="form.rate" type="number"></el-input>
                      </el-form-item>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="运费" align="center">
                  <template slot-scope="scope">
                    <div style="height: 50px;">
                      <el-form-item prop="shippingFee">
                        <el-input v-model="form.shippingFee" type="number" @change="numChange"></el-input>
                      </el-form-item>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="含税合计" align="center">
                  <template slot-scope="scope">
                    <!-- <el-input v-model="form.sum" disabled></el-input> -->
                    {{ sum || 0 }}
                  </template>
                </el-table-column>
              </el-table>
            </el-form-item>
            <el-form-item label="交货期" prop="completionDate">
              <el-date-picker v-model="form.completionDate" type="date" placeholder="选择日期" value-format="yyyy-MM-dd"
                style="width: 100%" />
            </el-form-item>
            <el-form-item label="意向企业：" prop="companyName">
              <el-input disabled v-model="form.companyName" placeholder="请先维护关联企业"></el-input>
            </el-form-item>
            <el-form-item label="联系人：" prop="linkMan">
              <el-input disabled v-model="form.linkMan" placeholder="请先维护联系人"></el-input>
            </el-form-item>
            <el-form-item label="联系电话：" prop="linkTel">
              <el-input disabled v-model="form.linkTel" placeholder="请先维护联系方式"></el-input>
            </el-form-item>
            <el-form-item class="footer-submit">
              <el-button style="width: 100%; height: 50px" type="primary" @click="onSubmit">提交</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="promptStyle">温馨提示</div>
        <div class="desc" style="margin-top: 20px">
          1、我们会在最快的时间和您取得联系（工作时间周一至周五8:00-18:00）
        </div>
        <div class="desc" style="margin-top: 13px">
          2、紧急问题请拨打：15512688882
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { submitIntention } from "@/api/home";

export default {
  data() {
    return {
      form: {
        quantity: "",
      },
      sum: 0,
      updateTime: "",
      rules: {
        quantity: [
          { required: true, message: "请输入承接量", trigger: "blur" },
        ],
        // sum: [
        //   { required: true, message: "请输入承接量、单价、税率、运费", trigger: "blur" },
        // ],
        price: [
          { required: true, message: "请输入含税单价", trigger: "blur" },
        ],
        rate: [
          { required: true, message: "请输入税率", trigger: "blur" },
        ],
        shippingFee: [
          { required: true, message: "请输入运费", trigger: "blur" },
        ],
        completionDate: [
          { required: true, message: "请选择交货期", trigger: "blur" },
        ]
      }
    };
  },
  created() {
    let userinfo = JSON.parse(sessionStorage.getItem("userinfo"));
    if (userinfo) {
      this.form.companyName = userinfo.memberCompanyName;
      this.form.linkMan = userinfo.companyRealName;
      this.form.linkTel = userinfo.memberPhone;
    }
    if (this.$route.query.demandName && this.$route.query.demandName != 'null') {
      this.form.title = this.$route.query.demandName
    }
    if (this.$route.query.intentionType) {
      this.form.intentionType = parseInt(this.$route.query.intentionType)
    }
    if (this.$route.query.fieldName) {
      this.form.fieldName = this.$route.query.fieldName
    }
    if (this.$route.query.intentionId) {
      this.form.intentionId = this.$route.query.intentionId
    }
    if (this.$route.query.updateTime && this.$route.query.updateTime != 'null') {
      this.updateTime = this.$route.query.updateTime
    }
  },
  methods: {
    onSubmit() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.form.sum = this.sum
          submitIntention(this.form).then((res) => {
            if (res.code == 200) {
              this.$message.success("提交成功")
              this.cancel()
            } else {
              this.$message.error(res.msg)
            }
          })
        }
      })
    },
    numChange() {
      if (this.form.price && this.form.shippingFee) {
        let price = parseFloat(this.form.price)
        let shippingFee = parseFloat(this.form.shippingFee)
        this.sum = (price + shippingFee)
      }
    },
    goDetail() {
      this.$router.push("/productOrderDetail?id=" + this.$route.query.intentionId);
    },
    cancel() {
      this.$router.go(-1)
    }
  },
};
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  background-color: rgb(242, 242, 242);
  padding-bottom: 60px;
}

.content_banner {
  width: 100%;
  height: 300px;
  background-image: url("../../../assets/release/banner.png");
  background-size: 100% 100%;
  text-align: center;
  margin: 0 auto;
  padding-top: 71px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  font-size: 40px;
  color: #000;
}

.card-content {
  display: flex;
  background: #ffffff;
  border-radius: 2px;
  margin-top: -70px;
  padding: 60px 59px 62px 60px;

  .card_left {
    .imgStyle {
      width: 330px;
      height: 230px;
    }

    .title {
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 18px;
      color: #333333;
      margin-top: 23px;
    }

    .publishTimeStyle {
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 14px;
      color: #666666;
    }

    .detailStyle {
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 14px;
      color: #21c9b8;
      margin-left: auto;
      cursor: pointer;
    }
  }

  .card_right {
    margin-left: 40px;
    width: 100%;

    .resourceType {
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 16px;
      color: #030a1a;
    }

    .resourceValue {
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 16px;
      color: #666666;
    }

    .footer-submit {
      margin-top: 40px;
    }

    .promptStyle {
      margin-top: 30px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 16px;
      color: #030a1a;
    }

    .desc {
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 14px;
      color: #999999;
    }
  }
}
</style>

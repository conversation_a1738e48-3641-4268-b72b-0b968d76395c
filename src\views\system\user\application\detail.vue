<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="2.5" :xs="24">
        <user-menu />
      </el-col>
      <el-col :span="20" :xs="24">
        <div class="content">
          <div class="tabStyle">
            <div
              class="buttonStyle"
              :class="flag == 1 ? 'buttonHover' : ''"
              @click="getFlag(1)"
            >
              基本信息
            </div>
            <div
              class="buttonStyle"
              :class="flag == 2 ? 'buttonHover' : ''"
              @click="getFlag(2)"
            >
              开发管理
            </div>
          </div>
          <div style="margin-left: 20px">
            <div v-show="flag == 1">
              <div style="margin-top: 20px">
                <div class="title">应用凭证</div>
                <el-descriptions>
                  <el-descriptions-item label="AppKey:"
                    >kooriookami</el-descriptions-item
                  >
                  <el-descriptions-item label="AESKey"
                    >18100000000</el-descriptions-item
                  >
                  <el-descriptions-item label="AppSecret"
                    >苏州市</el-descriptions-item
                  >
                </el-descriptions>
              </div>
              <div style="margin-top: 20px">
                <div class="title">应用信息</div>
                <el-form
                  :model="ruleForm"
                  label-width="100px"
                  class="demo-ruleForm"
                >
                  <el-form-item label="应用名称:">
                    {{ ruleForm.appName }}
                  </el-form-item>
                  <el-form-item label="应用类型:">
                    {{ ruleForm.appCategory }}
                  </el-form-item>
                  <el-form-item label="应用服务端:">
                    {{ ruleForm.applicaServer == 0 ? "APP端" : "web端" }}
                  </el-form-item>
                  <el-form-item label="交付方式:">
                    {{ ruleForm.delivery == 0 ? "Saas服务" : "本地部署" }}
                  </el-form-item>
                  <el-form-item label="应用简介:">
                    {{ ruleForm.briefInto }}
                  </el-form-item>
                  <el-form-item label="应用详情:">
                    <div class="appliDetail" v-html="ruleForm.content"></div>
                  </el-form-item>
                  <el-form-item label="应用封面:">
                    <img
                      style="width: 100px; height: 100px"
                      :src="ruleForm.appLogo"
                      alt=""
                    />
                  </el-form-item>
                  <el-form-item label="应用提供:">
                    {{ ruleForm.supply }}
                  </el-form-item>
                  <el-form-item label="联系人:">
                    {{ ruleForm.linkman }}
                  </el-form-item>
                  <el-form-item label="联系方式:">
                    {{ ruleForm.phone }}
                  </el-form-item>
                </el-form>
              </div>
              <div style="margin-top: 20px">
                <div class="title">商品规格信息</div>
                <div style="margin-top: 20px">
                  <el-table
                    :data="tableData"
                    style="width: 50%; mni-height: 200px"
                  >
                    <el-table-column prop="spec" label="规格" width="180">
                    </el-table-column>
                    <el-table-column
                      prop="userNumber"
                      label="使用用户数"
                      width="180"
                    >
                    </el-table-column>
                    <el-table-column label="有效时间" width="200">
                      <template slot-scope="scope">
                        {{ parseTime(scope.row.validTime) }}
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </div>
              <div style="margin-top: 20px">
                <div class="title">商品价格信息</div>
                <div style="margin-top: 20px">
                  <el-table
                    :data="tableData"
                    style="width: 70%; mni-height: 200px"
                  >
                    <!-- <el-table-column prop="date" label="规格">
                    </el-table-column> -->
                    <el-table-column prop="orderCode" label="订货编码">
                    </el-table-column>
                    <el-table-column
                      prop="originalPrice"
                      label="商品原价（元）"
                    >
                    </el-table-column>
                    <el-table-column
                      prop="promotionPrice"
                      label="商品促销价（元）"
                    >
                    </el-table-column>
                    <el-table-column
                      prop="commissionRatio"
                      label="商品分佣比例（%）"
                    >
                    </el-table-column>
                  </el-table>
                </div>
              </div>
              <div style="margin-top: 20px">
                <div class="title">商品参数信息</div>
                <div style="margin-top: 20px">
                  <el-form
                    :model="ruleForm"
                    label-width="160px"
                    class="demo-ruleForm"
                  >
                    <el-form-item label="服务咨询电话:">
                      {{ ruleForm.appName }}
                    </el-form-item>
                    <el-form-item label="产品运营联系人手机号:">
                      {{ ruleForm.appCategory }}
                    </el-form-item>
                  </el-form>
                  <!-- <el-table
                    :data="tableData"
                    style="width: 50%; mni-height: 200px"
                  >
                    <el-table-column prop="date" label="名称" width="180">
                    </el-table-column>
                    <el-table-column prop="name" label="内容" width="180">
                    </el-table-column>
                  </el-table> -->
                </div>
              </div>
            </div>
            <div v-show="flag == 2">
              <div style="margin-top: 20px">
                <div class="title">开发管理配置</div>
                <el-form
                  ref="ruleForm"
                  label-width="170px"
                  class="demo-ruleForm"
                >
                  <el-form-item label="服务器出口IP:">
                    {{ ruleForm.serverIp }}
                  </el-form-item>
                  <el-form-item label="网页端（web）应用地址:">
                    {{ ruleForm.webUrl }}
                  </el-form-item>
                  <el-form-item label="网页端（web）体验地址:">
                    {{ ruleForm.webexperienceUrl }}
                  </el-form-item>
                  <!-- <el-form-item label="订单信息接收地址:">
                    {{ruleForm.serverexamineUrl}}
                  </el-form-item> -->
                  <el-form-item label="健康检查服务端地址:">
                    {{ ruleForm.serverexamineUrl }}
                  </el-form-item>
                  <el-form-item label="开发联系人:">
                    {{ ruleForm.developmentPeople }}
                  </el-form-item>
                  <!-- <el-form-item label="测试token:" prop="name"> </el-form-item> -->
                </el-form>
                <!-- <el-descriptions :column="1" style="height: 300px">
                  <el-descriptions-item label="服务器出口IP"
                    >kooriookami</el-descriptions-item
                  >
                  <el-descriptions-item label="网页端（web）应用地址"
                    >18100000000</el-descriptions-item
                  >
                  <el-descriptions-item label="网页端（web）体验地址"
                    >苏州市</el-descriptions-item
                  >
                  <el-descriptions-item label="订单信息接收地址"
                    >苏州市</el-descriptions-item
                  >
                  <el-descriptions-item label="健康检查服务端地址"
                    >苏州市</el-descriptions-item
                  >
                  <el-descriptions-item label="开发联系人手机号"
                    >苏州市</el-descriptions-item
                  >
                  <el-descriptions-item label="测试token"
                    >daddad#215</el-descriptions-item
                  >
                </el-descriptions> -->
              </div>
            </div>
          </div>
          <div class="btnStyle">
            <el-button @click="goBack">返回</el-button>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import UserMenu from "../components/userMenu.vue";
import { appliDetail } from "@/api/appliMarket";
import store from "@/store";

export default {
  name: "Operlog",
  dicts: ["sys_oper_type", "sys_common_status"],
  components: { UserMenu },
  data() {
    return {
      queryParams: {
        pageNum: 1,
      },
      total: 0,
      flag: 1,
      tableData: [
        {
          date: "2016-05-02",
          name: "王小虎",
          address: "上海市普陀区金沙江路 1518 弄",
        },
        {
          date: "2016-05-04",
          name: "王小虎",
          address: "上海市普陀区金沙江路 1517 弄",
        },
        {
          date: "2016-05-01",
          name: "王小虎",
          address: "上海市普陀区金沙江路 1519 弄",
        },
        {
          date: "2016-05-03",
          name: "王小虎",
          address: "上海市普陀区金沙江路 1516 弄",
        },
      ],
      ruleForm: {},
    };
  },
  created() {
    this.getDetail();
  },
  methods: {
    getDetail() {
      this.tableData = [];
      let id = this.$route.query.id;
      let params = {
        id,
        userId: store.getters.userId,
      };
      appliDetail(params).then((res) => {
        if (res.code === 200) {
          this.ruleForm = res.data;
          this.tableData.push(res.data);
        }
      });
    },
    getFlag(value) {
      this.flag = value;
    },
    goBack() {
      this.$router.go(-1);
    },
  },
};
</script>
<style lang="scss" scoped>
.app-container {
  background: #f4f5f9;
}
.content {
  width: 100%;
  padding: 40px;
  background: #ffffff;
  // background: rgb(242, 248, 255);
  .tabStyle {
    display: flex;
    .buttonStyle {
      width: 100px;
      padding: 10px;
      color: #21c9b8;
      text-align: center;
      cursor: pointer;
      border: 1px solid #21c9b8;
    }
    .buttonStyle:nth-child(1) {
      border-right: none;
    }
    .buttonStyle:nth-child(2) {
      border-left: none;
    }
    .buttonHover {
      background: #21c9b8;
      color: #ffffff;
    }
  }
  .title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 10px;
  }
  .btnStyle {
    text-align: center;
  }
}
::v-deep .appliDetail img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
</style>

<template>
  <div
    class="card-container2 wow animate__animated animate__fadeInUp"
    data-wow-duration="1s"
  >
    <div class="card-container">
      <div class="enterpriseTitle">
        动态资讯
        <div class="allEnterprise" @click="goMore">查看更多 ></div>
      </div>
    </div>
    <div class="card-container">
      <div class="content_top">
        <div class="title">
          {{ dynamicInfoData.newsInformationName }}
        </div>
        <div
          class="desc ellipsis2"
          v-html="dynamicInfoData.newsInformationContent"
        ></div>
        <div class="button">
          <div
            class="button_text"
            @click="goDetail(dynamicInfoData.newsInformationId)"
          >
            了解详情
          </div>
          <div class="button_icon">></div>
        </div>
        <div class="content_top_left"></div>
        <div class="content_top_right">
          <img
            style="width: 100%; height: 100%"
            :src="
              dynamicInfoData.newsInformationImg ||
              'https://xp-tech.oss-cn-beijing.aliyuncs.com/20250221/1740119158695402.png?Expires=4893719158&OSSAccessKeyId=LTAI4G5Udf4KbAUamwr8dKC9&Signature=alCK01ODa%2Bs1ReoXEFDgWDiJ4uM%3D'
            "
            alt=""
          />
        </div>
      </div>
      <div class="content_bottom">
        <div
          class="content_bottom_item"
          v-for="(item, index) in dynamicInfoList"
          :key="index"
          @click="goDetail(item.newsInformationId)"
        >
          <span class="bottom"></span>
          <span class="right"></span>
          <span class="top"></span>
          <span class="left"></span>
          <div>
            <img
              :src="
                item.newsInformationImg ||
                'https://xp-tech.oss-cn-beijing.aliyuncs.com/20250221/1740119158695402.png?Expires=4893719158&OSSAccessKeyId=LTAI4G5Udf4KbAUamwr8dKC9&Signature=alCK01ODa%2Bs1ReoXEFDgWDiJ4uM%3D'
              "
              alt=""
            />
          </div>
          <div class="title">
            {{ item.newsInformationName }}
          </div>
          <div
            class="desc ellipsis2"
            v-html="item.newsInformationIntroduction"
          ></div>
          <div class="detail">
            <div class="time">
              {{ item.createTime }}
            </div>
            <div class="detail_text">查看详情 ></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { infoData } from "@/api/home";

export default {
  name: "Dashboard",
  data() {
    return {
      dynamicInfoData: {},
      dynamicInfoList: [],
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      let params = {
        pageNum: 1,
        pageSize: 4,
      };
      infoData(params).then((res) => {
        if (res.code === 200) {
          this.dynamicInfoData = res.rows[0];
          this.dynamicInfoData.newsInformationContent =
            decodeURIComponent(res.rows[0].newsInformationContent) || "";
          this.dynamicInfoList = res.rows.splice(1, 3);
          this.dynamicInfoList.forEach((item) => {
            item.newsInformationContent =
              decodeURIComponent(item.newsInformationContent) || "";
          });
        }
      });
    },
    goDetail(id) {
      this.$router.push("/dynamicInfoDetail?id=" + id);
    },
    goMore() {
      this.$router.push("/aboutUs?index=1");
    }
  },
};
</script>
<style lang="scss" scoped>
.card-container2 {
  width: 100%;
  margin: 0 auto;
  margin-bottom: 62px;
  .enterpriseTitle {
    width: 100%;
    font-size: 36px;
    text-align: center;
    margin: 61px 0 60px 0;
    position: relative;
    font-family: Source Han Sans CN;
    font-weight: 500;
    color: #000000;
    .allEnterprise {
      position: absolute;
      top: 18px;
      right: 0;
      height: 15px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 14px;
      color: #787878;
      cursor: pointer;
    }
  }
  .content_top {
    width: 900px;
    height: 350px;
    background-color: #21c9b8;
    position: relative;
    padding-top: 71px;
    .title {
      width: 549px;
      height: 57px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 22px;
      color: #ffffff;
      line-height: 36px;
      margin-left: 69px;
    }
    .desc {
      width: 651px;
      height: 60px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 14px;
      color: #ffffff;
      line-height: 24px;
      margin-left: 69px;
      margin-top: 32px;
    }
    .button {
      width: 120px;
      height: 44px;
      border-radius: 2px;
      border: 1px solid #ffffff;
      margin-left: 69px;
      margin-top: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s ease-in;
      &:hover {
        background-color: #fff;
        border: 1px solid #37c9b8;
        .button_icon {
          background: #37c9b8;
          color: #fff;
        }
        .button_text {
          color: #37c9b8;
        }
      }
    }
    .button_text {
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 16px;
      color: #ffffff;
      transition: all 0.2s ease-in;
    }
    .button_icon {
      width: 16px;
      transition: all 0.2s ease-in;
      height: 16px;
      border-radius: 50%;
      text-align: center;
      line-height: 16px;
      color: #21c9b8;
      background: #ffffff;
      margin-left: 7px;
    }
    .content_top_left {
      position: absolute;
      left: -360px;
      top: 0;
      width: 360px;
      height: 350px;
      background-color: #21c9b8;
    }
    .content_top_right {
      position: absolute;
      right: -300px;
      top: 40px;
      width: 420px;
      height: 270px;
    }
  }
  .content_bottom {
    margin-top: 40px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    .content_bottom_item {
      width: 386px;
      height: 420px;
      background: #ffffff;
      box-shadow: 0px 1px 14px 0px rgba(6, 66, 213, 0.1);
      border-radius: 4px;
      margin-left: 21px;
      cursor: pointer;
      position: relative;
      span {
        position: absolute;
        z-index: 1;
        background-color: #37c9b8;
        transition: transform 0.5s ease;
      }
      .bottom,
      .top {
        height: 2px;
        left: -1px;
        right: -1px;
        transform: scaleX(0);
      }
      .left,
      .right {
        width: 2px;
        top: -1px;
        bottom: -1px;
        transform: scaleY(0);
      }
      .bottom {
        bottom: -1px;
        transform-origin: bottom right;
      }
      .right {
        right: -1px;
        transform-origin: top right;
      }
      .top {
        top: -1px;
        transform-origin: top left;
      }
      .left {
        left: -1px;
        transform-origin: bottom left;
      }
      img {
        width: 100%;
        height: 180px;
      }
      .title {
        height: 18px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 18px;
        color: #222222;
        margin-left: 31px;
        margin-top: 29px;
      }
      .desc {
        width: 323px;
        height: 60px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #666666;
        line-height: 27px;
        margin-left: 31px;
        margin-top: 50px;
      }
      .detail {
        display: flex;
        align-items: center;
        margin-top: 46px;
        margin-left: 30px;
        margin-right: 48px;
      }
      .time {
        height: 11px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #b1b1b1;
      }
      .detail_text {
        height: 14px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #b1b1b1;
        margin-left: auto;
      }
    }
    .content_bottom_item:nth-child(1) {
      margin-left: 0;
    }
    .content_bottom_item:hover {
      box-shadow: 0px 4px 20px 0px rgba(33, 201, 184, 0.2);
      .top {
        transform-origin: top right;
        transform: scaleX(1);
      }
      .left {
        transform-origin: top left;
        transform: scaleY(1);
      }
      .bottom {
        transform-origin: bottom left;
        transform: scaleX(1);
      }
      .right {
        transform-origin: bottom right;
        transform: scaleY(1);
      }
      .detail_text {
        color: #21c9b8;
      }
    }
  }
}
.ellipsis2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  text-overflow: ellipsis;
  word-wrap: break-word;
}
</style>

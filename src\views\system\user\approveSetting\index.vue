<!--
 * @Author: zhc
 * @Date: 2023-02-12 10:25:16
 * @LastEditTime: 2023-02-27 14:43:13
 * @Description: 
 * @LastEditors: zhc
-->
<!--
 * @Author: zhc
 * @Date: 2023-02-03 11:06:49
 * @LastEditTime: 2023-02-11 11:21:19
 * @Description: 
 * @LastEditors: zhc
-->
<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="2.5" :xs="24">
        <user-menu />
      </el-col>
      <el-col :span="20" :xs="24">
        <div class="approve-setting-container">
          <div class="header-small">
            <div class="red-tag"></div>
            申请认证
          </div>
          <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane label="名片认证" name="1">
              <el-form
                ref="personalForm"
                :model="personalForm"
                :rules="personaRules"
                label-width="120px"
              >
                <el-form-item label="真实姓名：" prop="name">
                  <div v-if="!personalEdit">{{ personalForm.name }}</div>
                  <el-input
                    v-model="personalForm.name"
                    v-else
                    placeholder="请输入真实姓名"
                  />
                </el-form-item>
                <el-form-item label="联系方式：" prop="phone">
                  <div v-if="!personalEdit">{{ personalForm.phone }}</div>
                  <el-input
                    v-model="personalForm.phone"
                    v-else
                    placeholder="请选择联系方式"
                  />
                </el-form-item>
                <el-form-item label="公司名称：" prop="companyName">
                  <div v-if="!personalEdit">{{ personalForm.companyName }}</div>
                  <el-select
                    v-model="personalForm.companyName"
                    v-else
                    filterable
                    remote
                    reserve-keyword
                    placeholder="请输入关键词"
                    :remote-method="getCompanyList"
                    :loading="personalLoading"
                    @change="personChanged"
                  >
                    <el-option
                      v-for="item in companyOptions"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="身份证明：" prop="personalCardList">
                  <div class="pic-class">
                    <el-upload
                      list-type="picture-card"
                      :headers="headers"
                      :action="uploadUrl"
                      :class="[personalEdit ? '' : 'hide']"
                      :disabled="!personalEdit"
                      :file-list="personalForm.personalCardList"
                      :accept="accept"
                      :before-upload="handleBeforeUpload"
                      :on-preview="handlePersonalCardPreview"
                      :on-remove="handleRemove"
                      :on-success="handlePersonalCardSuccess"
                    >
                      <i class="el-icon-plus"></i>
                      <div
                        v-if="personalEdit"
                        slot="tip"
                        class="el-upload__tip"
                      >
                        点击上传
                        <span class="red-text">名片、工作证、工牌</span>
                        等可以认证身份的图片
                      </div>
                    </el-upload>
                  </div>

                  <el-dialog
                    append-to-body
                    :visible.sync="imgVisible"
                    :close-on-click-modal="false"
                  >
                    <img width="100%" :src="imageUrl" alt="" />
                  </el-dialog>
                </el-form-item>
              </el-form>
              <div class="button-container">
                <el-button
                  v-if="!personalEdit"
                  type="danger"
                  @click="changePersonEdit"
                  >编辑</el-button
                >
                <el-button v-else type="danger" @click="submitPersonal()"
                  >保存</el-button
                >
              </div>
            </el-tab-pane>
            <el-tab-pane label="企业认证" name="2">
              <div class="staff-list">
                <el-form
                  ref="companyForm"
                  :model="companyForm"
                  :rules="companyRules"
                  label-width="120px"
                >
                  <el-row>
                    <el-col :span="24">
                      <el-form-item label="真实姓名：" prop="name">
                        <div v-if="!companyEdit">
                          {{ companyForm.name }}
                        </div>
                        <el-input
                          v-model="companyForm.name"
                          v-else
                          placeholder="请输入真实姓名"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row>
                    <el-col :span="24">
                      <el-form-item label="联系方式：" prop="phone">
                        <div v-if="!companyEdit">
                          {{ companyForm.phone }}
                        </div>
                        <el-input
                          v-model="companyForm.phone"
                          v-else
                          placeholder="请选择联系方式"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="24">
                      <el-form-item label="公司名称：" prop="companyName">
                        <div v-if="!companyEdit">
                          {{ companyForm.companyName }}
                        </div>
                        <el-select
                          v-model="companyForm.companyName"
                          v-else
                          filterable
                          remote
                          reserve-keyword
                          placeholder="请输入关键词"
                          :remote-method="getCompanyList"
                          :loading="companyLoading"
                          @change="companyChanged"
                        >
                          <el-option
                            v-for="item in companyOptions"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                          >
                          </el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-form-item label="企业授权书：">
                    <div class="pic-class">
                      <el-upload
                        list-type="picture-card"
                        :class="[companyEdit ? '' : 'hide']"
                        :headers="headers"
                        :action="uploadUrl"
                        :file-list="companyForm.companyCardList"
                        :accept="accept"
                        :disabled="!companyEdit"
                        :before-upload="handleBeforeUpload"
                        :on-preview="handleCompanyCardPreview"
                        :on-remove="handleCompanyCardRemove"
                        :on-success="handlecompanyCardSuccess"
                      >
                        <i class="el-icon-plus"></i>
                        <div
                          v-if="companyEdit"
                          slot="tip"
                          class="el-upload__tip"
                        >
                          支持jpg、png格式
                          <a class="red-text" @click="download">查看模板>></a>
                        </div>
                      </el-upload>
                    </div>
                    <el-dialog
                      append-to-body
                      :visible.sync="imgVisible"
                      :close-on-click-modal="false"
                    >
                      <img width="100%" :src="imageUrl" alt="" />
                    </el-dialog>
                  </el-form-item>
                  <el-form-item label="" prop="companyCardList">
                    <el-upload
                      :file-list="companyForm.authMatTerialList"
                      :headers="headers"
                      :action="uploadUrl"
                      :disabled="!companyEdit"
                      accept=".pdf, .docx, .xls"
                      :on-remove="handleompanyCardRemove"
                      :on-success="handleAuthMatTerialSuccess"
                      :limit="10"
                    >
                      <el-button
                        v-if="companyEdit"
                        class="apathy-upload-btn"
                        size="small"
                        icon="el-icon-upload2"
                        >上传文件
                      </el-button>
                      <span
                        v-if="companyEdit"
                        slot="tip"
                        class="el-upload__tip"
                      >
                        仅限doc、pdf、xls格式
                      </span>
                    </el-upload>
                  </el-form-item>
                </el-form>
                <div class="button-container">
                  <el-button
                    v-if="!companyEdit"
                    type="danger"
                    @click="changeCompanyEdit"
                    >编辑</el-button
                  >
                  <el-button v-else type="danger" @click="submitCompany()"
                    >保存</el-button
                  >
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import UserMenu from "../components/userMenu.vue";
import FileUpload from "@/components/FileUpload";
import store from "@/store";
import { getCompanyListByName } from "@/api/system/user";
import { getToken } from "@/utils/auth";
import {
  personalApprove,
  companyApprove,
  getApproveDetail,
} from "@/api/system/approve.js";
export default {
  name: "ApproveSetting",
  components: { UserMenu, FileUpload },
  data() {
    return {
      activeName: "1",
      uploadUrl: process.env.VUE_APP_BASE_API + "/file/upload", //上传地址
      accept: ".jpg, .jpeg, .png, .bmp",
      headers: { Authorization: "Bearer " + getToken() },
      companyOptions: [],
      isAdmin: false,
      loading: false,
      imgVisible: false,
      personalEdit: false,
      companyEdit: false,
      companyLoading: false,
      personalLoading: false,
      imageUrl: "",
      user: {
        userId: store.getters.userId,
        bussinessNo: store.getters.bussinessNo,
        phonenumber: store.getters.phonenumber,
      },
      personalForm: {
        personalCardList: [],
      },
      companyForm: {
        authMatTerialList: [],
        companyCardList: [],
      },
      personaRules: {
        companyName: [
          { required: true, message: "公司名称不能为空", trigger: "blur" },
        ],
        name: [
          { required: true, message: "真实姓名不能为空", trigger: "blur" },
        ],
        phone: [
          { required: true, message: "联系方式不能为空", trigger: "blur" },
        ],
        personalCardList: [
          { required: false, message: "身份证明不能为空", trigger: "blur" },
        ],
      },
      companyRules: {
        name: [
          { required: true, message: "真实姓名不能为空", trigger: "blur" },
        ],
        companyName: [
          { required: true, message: "公司名称不能为空", trigger: "blur" },
        ],

        phone: [
          { required: true, message: "联系方式不能为空", trigger: "blur" },
        ],
        authMatTerialList: [
          { required: false, message: "授权书不能为空", trigger: "blur" },
        ],
        companyCardList: [
          { required: false, message: "附件不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getApproveDetail();
  },
  methods: {
    handleClick(tab, event) {
      if (this.activeName == "1") {
      } else {
      }
      this.getApproveDetail();
      this.queryParams = {
        pageNum: 1,
        pageSize: 5,
      };
    },
    changePersonEdit() {
      this.personalEdit = true;
    },
    changeCompanyEdit() {
      this.companyEdit = true;
    },
    // 产品照片上传之前的钩子
    handleBeforeUpload(file) {
      let { name, type, size } = file;
      let typeList = this.accept
        .split(",")
        .map((item) => item.trim().toLowerCase().substr(1));
      let dotIndex = name.lastIndexOf(".");
      // 文件类型校验
      if (dotIndex === -1) {
        this.$message.error("请上传正确格式的文件");
        return false;
      } else {
        let suffix = name.substring(dotIndex + 1);
        if (typeList.indexOf(suffix.toLowerCase()) === -1) {
          this.$message.error("请上传正确格式的文件");
          return false;
        }
      }
      // 文件上传大小限制
      if (size > 1048576 * 20) {
        this.$message.error("文件大小不能超过20M！");
        return false;
      }
    },
    // 点击产品照片
    handlePersonalCardPreview(file) {
      this.imageUrl = file.url;
      this.imgVisible = true;
    },
    handleCompanyCardPreview(file) {
      this.imageUrl = file.url;
      this.imgVisible = true;
    },
    // 删除产品照片
    handleRemove(file, fileList) {
      this.personalForm.personalCardList = fileList;
    },
    // 删除产品照片
    handleCompanyCardRemove(file, fileList) {
      this.companyForm.companyCardList = fileList;
    },
    handlecompanyCardSuccess(res, file, fileList) {
      if (!this.companyForm.companyForm) {
        this.companyForm.companyCardList = [];
      }
      //此处写上传oss成功之后的逻辑
      if (res.code == 200) {
        this.companyForm.companyCardList.push(res.data);
      }
    },
    handlePersonalCardSuccess(res, file, fileList) {
      if (!this.personalForm.personalCardList) {
        this.personalForm.personalCardList = [];
      }
      //此处写上传oss成功之后的逻辑
      if (res.code == 200) {
        this.personalForm.personalCardList.push(res.data);
      }
    },
    handleAuthMatTerialSuccess(res, file, fileList) {
      if (!this.companyForm.authMatTerialList) {
        this.companyForm.authMatTerialList = [];
      }
      //此处写上传oss成功之后的逻辑
      if (res.code == 200) {
        this.companyForm.authMatTerialList.push(res.data);
      }
    },
    getCompanyList(query) {
      if (query !== "") {
        getCompanyListByName(query).then((response) => {
          this.companyOptions = response.data;
        });
      }
    },
    companyChanged(res) {
      this.companyOptions.forEach((item) => {
        if (item.id == res) {
          this.companyForm.bussinessNo = item.creditCode;
          this.companyForm.tianyanId = item.id;
          this.companyForm.companyName = item.name;
        }
      });
    },
    personChanged(res) {
      this.companyOptions.forEach((item) => {
        if (item.id == res) {
          this.personalForm.bussinessNo = item.creditCode;
          this.personalForm.tianyanId = item.id;
          this.personalForm.companyName = item.name;
        }
      });
    },
    handleompanyCardRemove(file, fileList) {
      this.companyForm.companyCardList = res.data;
    },
    submitPersonal() {
      this.$refs.personalForm.validate((valid) => {
        if (valid) {
          personalApprove({
            ...this.personalForm,
          }).then((res) => {
            if (res.code == 200) {
              this.$modal.msgSuccess("操作成功");
              this.getApproveDetail();
              this.personalEdit = false;
            }
          });
        }
      });
    },
    submitCompany() {
      // if (!this.companyForm.authMatTerialList) {
      //   this.$modal.msgError("请上传授权书");
      // }
      this.$refs.companyForm.validate((valid) => {
        if (valid) {
          companyApprove({
            ...this.companyForm,
          }).then((res) => {
            if (res.code == 200) {
              this.$modal.msgSuccess("操作成功");
              this.getApproveDetail();
              this.companyEdit = false;
            }
          });
        }
      });
    },
    download() {
      // let url =
      //   "https://xp-tech.oss-cn-beijing.aliyuncs.com/cykc/20230213/1676277801454790.docx";
      // window.open(url, "_blank");
      var link = document.createElement("a");
      link.href =
        "https://xp-tech.oss-cn-beijing.aliyuncs.com/cykc/20230213/1676277801454790.docx";
      link.download = "授权书";
      link.target = "_blank";
      link.click();
    },

    getApproveDetail() {
      getApproveDetail().then((res) => {
        if (res.code == 200) {
          this.personalForm = res.data ?? {};
          this.companyForm = res.data ?? {};
          if (!this.personalForm.personalCardList) {
            this.personalForm.personalCardList = [];
          }
          if (!this.companyForm.authMatTerialList) {
            this.companyForm.authMatTerialList = [];
          }
          if (!this.companyForm.companyCardList) {
            this.companyForm.companyCardList = [];
          }
        }
      });
    },
  },
};
</script>

<style lang="scss">
.app-container {
  background: #f4f5f9;
  .approve-setting-container {
    width: 100%;
    background: #fff;
    min-height: 700px;
    padding: 20px;
    .el-select {
      display: inline-block;
      position: relative;
      width: 100%;
    }
    .el-button--danger {
      background: #21c9b8;
      color: #fff;
      border-color: #21c9b8;
    }
    .header-small {
      text-align: center;
      display: flex;
      font-size: 20px;
      font-weight: 500;
      color: #333333;
      line-height: 20px;

      .red-tag {
        margin-right: 12px;
        width: 3px;
        height: 22px;
        background: #21c9b8;
      }
    }
    .apathy-upload-btn {
      margin-right: 20px;
    }
    .button-container {
      width: 100%;
      margin-top: 50px;
      text-align: center;
    }

    .el-tabs__nav {
      width: 100%;
      height: 40px;
      padding: 0 43%;
      display: flex;
      // justify-content: space-between;
    }

    .el-tabs__nav-wrap::after {
      background-color: transparent;
    }
    .el-tabs__active-bar {
      margin-left: 43%;
      background-color: #21c9b8;
    }
    .el-tabs__item.is-active {
      color: #21c9b8;
    }
    .el-tabs__item:hover {
      color: #21c9b8;
      cursor: pointer;
    }
  }
  .pic-class .hide .el-upload--picture-card {
    display: none;
  }
  .el-pagination {
    width: 100%;
    margin-top: 20px;
    text-align: center;
  }
  .el-pagination.is-background .el-pager li {
    background-color: #fff;
  }
  .el-pagination.is-background .el-pager li:not(.disabled).active {
    background-color: #21c9b8;
    color: #ffffff;
  }
  .el-pagination.is-background .el-pager li:not(.disabled):hover {
    color: #21c9b8;
  }
  .red-text {
    color: #21c9b8;
  }
  .trans-form {
    border-radius: 6px;
    background: #ffffff;
    width: 400px;
    padding: 25px 5px 5px 25px;
    .header {
      font-size: 18px;
      font-weight: 500;
      color: #121620;
      line-height: 18px;
      margin-bottom: 12px;
    }
    .el-input {
      height: 38px;
      input {
        height: 38px;
      }
    }
    .el-select {
      display: inline-block;
      position: relative;
      width: 100%;
    }
    .input-icon {
      height: 39px;
      width: 14px;
      margin-left: 2px;
    }
    .el-form-item--medium .el-form-item__content {
      display: flex;
      line-height: 36px;
    }
    .el-form-item {
      margin-bottom: 12px;
    }
    .el-input__suffix-inner {
      .active-style {
        color: #21c9b8;
        font-size: 14px;
      }
      .disabled-style {
        color: #999;
        font-size: 14px;
      }
    }
  }
  .el-button--primary {
    /* color: #FFFFFF; */
    background-color: #21c9b8 !important;
    border-color: #21c9b8;
  }
}
</style>

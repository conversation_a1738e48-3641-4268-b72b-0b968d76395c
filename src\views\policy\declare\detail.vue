<template>
  <div class="policy-declare-detail-container">
    <!-- banner图 -->
    <div class="policy-declarel-detail-banner">
      <img
        src="../../../assets/policyDeclare/policyDeclareDetailBanner.png"
        alt=""
      />
    </div>
    <div class="policy-declarel-detail-title-box">
      <div class="policy-declarel-detail-divider"></div>
      <div class="policy-declarel-detail-title">政策详情</div>
      <div class="policy-declarel-detail-divider"></div>
    </div>
    <div v-loading="loading" class="policy-declarel-detail-card">
      <div class="policy-declarel-detail-content">
        <div class="policy-declarel-detail-title">{{ data.title }}</div>
        <div class="headline-box">
          <div class="headline-address">{{ data.releaseUnitName }}</div>
          <div>{{ data.createTime }}</div>
        </div>
        <div class="declarel-detail-content">
          <div
            v-html="data.content"
            class="declarel-detail-text ql-editor"
          ></div>
        </div>
        <!-- 在线申报按钮 -->
        <div v-if="data.policyStatus !== 2" class="activity-area-btn">
          <el-button class="activity-sign-up" @click="goDeclare"
            >在线申报
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getPolicyDeclareDetail } from "@/api/policyDeclare";
import { mapGetters } from "vuex";

export default {
  data() {
    return {
      loading: false,
      data: {},
    };
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      this.loading = true;
      getPolicyDeclareDetail({ id: this.$route.query.id })
        .then((res) => {
          this.loading = false;
          this.data = res.data || {};
        })
        .catch(() => {
          this.loading = false;
        });
    },
    // 跳转在线申报页面
    goDeclare() {
      // 判断是否登录
      if (!this.token) {
        this.$confirm("请先登录", "提示", {
          confirmButtonText: "去登录",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          this.$store.dispatch("LogOut").then(() => {
            location.href = "/login";
          });
        });
        return;
      }
      this.$router.push({
        name: "AddPolicy",
        params: {
          id: this.$route.query.id,
          title: this.data.title,
        },
      });
    },
  },
  computed: {
    ...mapGetters(["token"]),
  },
};
</script>
<style lang="scss" scoped>
.policy-declare-detail-container {
  width: 100%;
  padding-bottom: 60px;
  background: #f4f5f9;

  .policy-declarel-detail-banner {
    width: 100%;
    height: 25.93vh;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .policy-declarel-detail-title-box {
    width: 336px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 60px 0 40px;

    .policy-declarel-detail-title {
      font-size: 40px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #333;
      line-height: 40px;
      padding: 0 40px;
    }

    .policy-declarel-detail-divider {
      width: 48px;
      height: 4px;
      background: #21c9b8;
    }
  }

  .policy-declarel-detail-card {
    width: 1200px;
    background: #fff;
    margin: 0 auto;

    .policy-declarel-detail-content {
      padding: 60px;

      .policy-declarel-detail-title {
        font-size: 32px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #333;
        line-height: 32px;
        word-wrap: break-word;
      }

      .headline-box {
        display: flex;
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #999;
        line-height: 12px;
        padding: 40px 0 10px;
        border-bottom: 1px solid #e8e8e8;

        .headline-address {
          max-width: 990px;
          word-break: break-all;
          padding-right: 40px;
        }
      }

      .declarel-detail-content {
        font-size: 16px;
        font-family: PingFangSC-Regular, PingFang SC;
        color: #333;
        line-height: 28px;
        padding-top: 60px;
      }

      .activity-area-btn {
        text-align: center;
        margin-top: 96px;

        .activity-sign-up {
          width: 400px;
          height: 50px;
          background: #21c9b8;
          border-radius: 4px;
          font-size: 20px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #fff;
          line-height: 20px;
        }
      }
    }
  }
}
</style>

<style lang="scss">
.policy-declare-detail-container {
  .declarel-detail-content {
    .declarel-detail-text {
      word-break: break-all;
      font-size: 16px;
      line-height: 28px;
      color: #333;
      font-family: PingFangSC-Regular, PingFang SC;

      img {
        max-width: 100%;
      }
    }
  }
}
</style>

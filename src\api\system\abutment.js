/*
 * @Author: zhc
 * @Date: 2023-02-11 14:45:07
 * @LastEditTime: 2023-02-13 16:07:24
 * @Description:
 * @LastEditors: zhc
 */

import request from "@/utils/request";

// 查询-对接记录列表
export function getAbutmentList(params) {
  return request({
    url: "/system/interactRecord/list",
    method: "get",
    params: params,
  });
}
// 修改-对接记录-接受-忽略
export function operateAbutment(params) {
  return request({
    url: "/system/interactRecord/operate",
    method: "get",
    params: params,
  });
}

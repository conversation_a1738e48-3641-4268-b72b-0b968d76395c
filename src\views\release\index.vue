<template>
  <div class="content">
    <div class="content_banner">
      <div class="type_first">
        <div
          class="type_first_item"
          :class="typeFirst == index ? 'type_first_item_active' : ''"
          v-for="(item, index) in typeFirstList"
          :key="index"
          @click="getTypeFirst(index)"
        >
          {{ item.typeName }}
        </div>
      </div>
      <div class="type_second" v-show="typeFirst == 0">
        <div
          class="type_second_item"
          v-for="(item, index) in typeSecondList"
          :key="index"
        >
          <div @click="getTypeSecond(index)">
            <div class="typeSecondName">{{ item.typeName }}</div>
            <div class="typeSecondLine" v-show="typeSecond == index"></div>
          </div>
        </div>
      </div>
    </div>
    <div class="card-container requireForm">
      <serviceRequire v-if="typeFirst == 0 && typeSecond == 0"></serviceRequire>
      <testingRequire v-if="typeFirst == 0 && typeSecond == 1"></testingRequire>
      <processRequire v-if="typeFirst == 0 && typeSecond == 2"></processRequire>
      <supply v-if="typeFirst == 1"></supply>
      <sharedOrders v-if="typeFirst == 2"></sharedOrders>
    </div>
  </div>
</template>
<script>
import serviceRequire from "./components/serviceRequire.vue";
import testingRequire from "./components/testingRequire.vue";
import processRequire from "./components/processRequire.vue";
import supply from "./components/supply.vue";
import sharedOrders from "./components/sharedOrders.vue";
export default {
  components: {
    serviceRequire,
    testingRequire,
    processRequire,
    sharedOrders,
    supply,
  },
  data() {
    return {
      typeFirstList: [
        {
          id: 1,
          typeName: "发布需求",
        },
        {
          id: 2,
          typeName: "发布供给",
        },
        {
          id: 3,
          typeName: "发布共享订单",
        },
      ],
      typeFirst: 0,
      typeSecondList: [
        {
          id: 1,
          typeName: "服务需求",
        },
        {
          id: 2,
          typeName: "检测需求",
        },
        {
          id: 3,
          typeName: "工序外协需求",
        },
      ],
      typeSecond: 0,
    };
  },
  created() {
    if (this.$route.query.index) {
      this.typeFirst = this.$route.query.index;
    }
    this.initData();
  },
  methods: {
    getTypeFirst(val) {
      this.typeFirst = val;
    },
    getTypeSecond(val) {
      this.typeSecond = val;
    },
    // 判断是否关联企业
    initData() {
      let userInfo = JSON.parse(sessionStorage.getItem("userinfo"));
      if (!userInfo?.memberCompanyName) {
        this.$confirm("您当前尚未登录或尚未关联企业，是否前往操作?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
          cancelButtonClass: "cancelButtonClass",
          confirmButtonClass: "customClass",
        })
          .then(() => {
            this.$router.push("/user/userCenter");
          })
          .catch(() => {
            this.$router.go(-1);
          });
        return;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  background-color: rgb(242, 242, 242);
  padding-bottom: 60px;
}
.content_banner {
  width: 100%;
  height: 300px;
  background-image: url("../../assets/release/banner.png");
  background-size: 100% 100%;
  padding-top: 63px;
  .type_first {
    width: 399px;
    height: 50px;
    background: #ffffff;
    border-radius: 25px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    .type_first_item {
      font-family: Source Han Sans CN;
      font-weight: 500;
      font-size: 18px;
      color: #333333;
      line-height: 70px;
      cursor: pointer;
      padding: 0 24.5px;
    }
    .type_first_item_active {
      height: 50px;
      line-height: 50px;
      background: #21c9b8;
      border-radius: 25px;
      text-align: center;
      color: #ffffff;
    }
  }
  .type_second {
    margin-top: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    .type_second_item {
      margin-left: 48px;
      height: 24px;
      cursor: pointer;
    }
    .type_second_item:nth-child(1) {
      margin-left: 0;
      position: relative;
    }
    .typeSecondName {
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 18px;
      color: #000;
    }
    .typeSecondLine {
      width: 40px;
      height: 4px;
      background: #55555588;
      border-radius: 2px;
      margin: 10px auto 0;
    }
  }
}
.requireForm {
  margin-top: -70px;
  padding: 59px 60px 57px 60px;
  width: 1200px;
  background: #ffffff;
  border-radius: 2px;
}
</style>

<template>
  <div>
    <entrepreneurship v-if="currentIndex == 0"></entrepreneurship>
    <fileSharing v-if="currentIndex == 1"></fileSharing>
    <scienceFunding v-if="currentIndex == 2"></scienceFunding>
  </div>
</template>
<script>
import entrepreneurship from "./components/entrepreneurship/index.vue";
import fileSharing from "./components/fileSharing/index.vue";
import scienceFunding from "./components/scienceFunding/index.vue";
export default {
  name: "innovationSharing",
  components: { entrepreneurship, fileSharing, scienceFunding },
  data() {
    return {
      currentIndex: 0,
    };
  },
  watch: {
    "$route.query.index": {
      handler(val) {
        this.currentIndex = val || 0;
      },
      deep: true,
    },
  },

  methods: {},
  created() {
    this.currentIndex = this.$route.query.index || 0;
  },
};
</script>
<style></style>

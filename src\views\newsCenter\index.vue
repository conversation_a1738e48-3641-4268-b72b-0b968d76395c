<template>
  <div class="activity-container">
    <div class="activity-banner">
      <img src="../../assets/newsCenter/newsCenterBanner.png" alt="" />
      <div class="bannerTitle">新闻中心</div>
      <div class="bannerDesc">即时了解行业新闻动态,助力企业高质量迅速发展</div>
    </div>
    <div class="content">
      <div class="content_type">
        <div
          class="everyType"
          v-for="item in typeList"
          :key="item.dictValue"
          @click="getItemData(item.dictValue)"
        >
          <div
            class="title"
            :style="item.dictValue == flag ? 'color:#21C9B8' : ''"
          >
            {{ item.dictLabel }}
          </div>
          <div v-show="item.dictValue == flag" class="icon"></div>
        </div>
      </div>
      <div class="content_item" v-loading="loading">
        <div
          class="everyItem"
          v-for="item in newsList"
          :key="item.id"
          @click="goDetail(item.id)"
        >
          <div class="item_left" v-if="item.updateTime">
            <div class="item_year">{{ item.updateTime.slice(0, 4) }}</div>
            <div class="icon_year"></div>
            <div class="item_month">{{ item.updateTime.slice(5, 10) }}</div>
          </div>
          <div class="item_middle">
            <div class="title">
              {{ item.title }}
            </div>
            <div class="desc">
              {{ item.brief }}
            </div>
          </div>
          <div class="item_right">
            <img :src="getPicUrl(item.picUrl)" alt="" />
          </div>
        </div>
      </div>
      <div></div>
      <div class="activity-page-end">
        <el-button class="activity-page-btn" @click="goHome">首页</el-button>
        <el-pagination
          v-if="newsList && newsList.length > 0"
          background
          layout="prev, pager, next"
          class="activity-pagination"
          :page-size="pageSize"
          :current-page="pageNum"
          :total="total"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
// import { getActivityList } from "@/api/purchaseSales";
import { newsType, newsList } from "@/api/newsCenter";
import { getDicts } from "@/api/system/dict/data";
import { caseList } from "@/api/classicCase";

export default {
  data() {
    return {
      fit: "cover",
      loading: false,
      form: {
        keywords: "", //搜索内容
      },
      formInfo: {
        caseType: "", // 案例类型
      },
      caseTypeList: [],
      data: [],
      pageNum: 1,
      pageSize: 10,
      total: 0,
      solutionTypeList: [
        {
          value: "0",
          label: "全部",
        },
        {
          value: "1",
          label: "节能减排",
        },
        {
          value: "2",
          label: "低碳认证",
        },
        {
          value: "3",
          label: "数据核算",
        },
        {
          value: "4",
          label: "中和服务",
        },
        {
          value: "5",
          label: "星碳培训",
        },
        {
          value: "6",
          label: "绿色会议",
        },
        {
          value: "7",
          label: "数据建模",
        },
        {
          value: "8",
          label: "资产管理",
        },
      ],
      flag: "1",
      typeList: [
        {
          dictValue: "1",
          dictLabel: "平台动态",
        },
        {
          dictValue: "2",
          dictLabel: "行业动态",
        },
        {
          dictValue: "3",
          dictLabel: "政策法规",
        },
      ],
      newsList: [],
    };
  },
  created() {
    this.initData();
    // this.getDictsList("activity_type", "activityTypeList");
    // this.search();
  },
  methods: {
    handleCurrentChange(pageNum) {
      this.pageNum = pageNum;
      this.initData();
    },
    onSearch() {
      this.pageNum = 1;
      this.initData();
    },
    goHome() {
      this.$router.push({ path: "/index" });
    },
    initData() {
      this.loading = true;
      let params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        kind: "0",
        typeTop: this.flag,
      };
      newsList(params).then((res) => {
        if (res.code === 200) {
          console.log(res.rows, "----------------");
          this.newsList = res.rows;
          this.total = res.total;
          this.loading = false;
        }
      });
      // newsType().then((res) => {
      //   if (res.code === 200) {
      //     console.log(res, "-----------------");
      //   }
      // });
      // getDicts("case_industry").then((res) => {
      //   const { code, data = [] } = res;
      //   if (code === 200) {
      //     this.caseTypeList = data;
      //     this.getCaseList();
      //   }
      // });
    },
    getItemData(value) {
      this.flag = value;
      this.initData();
    },
    goDetail(id) {
      let routeData = this.$router.resolve({
        path: "/newsDetail",
        query: { id },
      });
      window.open(routeData.href, "_blank");
    },
    getPicUrl(picUrl) {
      try {
        let data = JSON.parse(picUrl);
        return data[0].url;
      } catch (error) {
        return picUrl;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.activity-container {
  width: 100%;
  background: #ffffff;
  .activity-banner {
    width: 100%;
    height: 500px;
    position: relative;
    img {
      width: 100%;
      height: 100%;
    }
    .bannerTitle {
      position: absolute;
      top: 161px;
      left: 24%;
      font-size: 50px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #ffffff;
    }
    .bannerDesc {
      position: absolute;
      top: 249px;
      left: 24%;
      font-size: 24px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #ffffff;
    }
  }
  .content {
    width: 1200px;
    margin: 40px auto 0;
    .content_type {
      display: flex;
      width: 100%;
      margin-bottom: 30px;
      .everyType {
        width: 110px;
        text-align: center;
        margin-left: 66px;
        cursor: pointer;
        .title {
          font-size: 18px;
          font-family: Source Han Sans CN;
          font-weight: 500;
          color: #333333;
        }
        .icon {
          width: 110px;
          height: 4px;
          background: #21c9b8;
          margin-top: 30px;
        }
      }
      .everyType:nth-child(1) {
        margin-left: 20px;
      }
    }
    .content_item {
      width: 100%;
      .everyItem {
        display: flex;
        width: 100%;
        height: 230px;
        background: #ffffff;
        box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.06);
        padding: 45px 50px;
        margin-top: 30px;
        cursor: pointer;
        .item_left {
          margin-top: 43px;
          width: 53px;
          .item_year {
            font-size: 24px;
            font-family: Source Han Sans CN;
            font-weight: 400;
            color: #222222;
          }
          .icon_year {
            width: 58px;
            height: 2px;
            background: #21c9b8;
            margin-top: 2px;
          }
          .item_month {
            font-size: 18px;
            font-family: Source Han Sans CN;
            font-weight: 400;
            color: #222222;
            margin-top: 5px;
          }
        }
        .item_middle {
          width: 710px;
          margin-left: 40px;
          .title {
            font-size: 18px;
            font-family: Source Han Sans CN;
            font-weight: 500;
            color: #222222;
            margin-top: 31px;
            overflow: hidden;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
            text-overflow: ellipsis;
            word-wrap: break-word;
          }
          .desc {
            font-size: 16px;
            font-family: Source Han Sans CN;
            font-weight: 500;
            color: #65676a;
            margin-top: 17px;
            overflow: hidden;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            text-overflow: ellipsis;
            word-wrap: break-word;
          }
        }
        .item_right {
          width: 200px;
          height: 140px;
          margin-left: 85px;
          img {
            width: 100%;
            height: 100%;
          }
        }
      }
      .everyItem:hover {
        box-shadow: 0px 4px 20px 0px rgba(58, 180, 118, 0.3);
        .item_year {
          color: #21c9b8;
        }
        .title {
          color: #21c9b8;
        }
      }
      .everyItem:nth-child(1) {
        margin-top: 0;
      }
    }
    .activity-page-end {
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0 auto;
      padding: 24px 0 60px;
      .activity-page-btn {
        width: 82px;
        height: 32px;
        background: #fff;
        border-radius: 4px;
        border: 1px solid #d9d9d9;
        font-family: PingFangSC-Regular, PingFang SC;
        color: #333;
        line-height: 10px;
      }
    }
  }
  .none-class {
    text-align: center;
    padding: 8% 0;
    background: #fff;
    margin-top: 25px;
    .text {
      font-size: 14px;
      font-weight: 400;
      color: #999999;
      line-height: 14px;
    }
  }
}
</style>

<style lang="scss">
.activity-container {
  .activity-page-end {
    .activity-pagination {
      .btn-prev,
      .btn-next,
      .btn-quickprev {
        width: 32px;
        height: 32px;
        background: #fff;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        color: #333;
      }
      &.is-background {
        .el-pager {
          .number {
            width: 32px;
            height: 32px;
            border: 1px solid #d9d9d9;
            background: #fff;
            border-radius: 4px;
            line-height: 32px;
            &.active {
              background: #21c9b8;
              border: 1px solid #21c9b8;
            }
          }
        }
      }
    }
  }
}
</style>

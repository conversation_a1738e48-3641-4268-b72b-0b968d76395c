<template>
  <div>
    <platIntroduction v-if="currentIndex == 0"></platIntroduction>
    <dynamicInfo v-if="currentIndex == 1"></dynamicInfo>
  </div>
</template>
<script>
import platIntroduction from "./components/platIntroduction.vue";
import dynamicInfo from "./components/dynamicInfo";
export default {
  name: "manufacturingSharing",
  components: { platIntroduction, dynamicInfo },
  data() {
    return {
      currentIndex: 0,
    };
  },
  watch: {
    "$route.query.index": {
      handler(val) {
        this.currentIndex = val || 0;
      },
      deep: true,
    },
  },

  methods: {},
  created() {
    this.currentIndex = this.$route.query.index || 0;
  },
};
</script>
<style></style>

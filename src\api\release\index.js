import request from "@/utils/request";

// 发布服务需求提交
export function releaseService(data) {
  return request({
    url: "/portalweb/demand",
    method: "post",
    data
  });
}
// 发布检测需求提交
export function releaseDetection(data) {
  return request({
    url: "/system/testingRequirement",
    method: "post",
    data
  });
}
// 发布检测需求列表
export function releaseDetectionList(params) {
  return request({
    url: "/system/testingRequirement/list",
    method: "get",
    params
  });
}
// 发布检测需求详情
export function releaseDetectionDetail(params) {
  return request({
    url: `/system/testingRequirement/${params}`,
    method: "get",
  });
}
// 发布工序外协需求提交
export function releaseProcess(data) {
  return request({
    url: "/system/outsourcingRequirement",
    method: "post",
    data
  });
}

// 发布供给-应用领域
export function applicationData(params) {
  return request({
    url: "/portalweb/applicationField/listDesk",
    method: "get",
    params,
  });
}

// 发布供给-应用领域新增
export function applicationAdd(data) {
  return request({
    url: "/portalweb/applicationField",
    method: "post",
    data,
  });
}

// 发布供给-新增发布
export function supplyAdd(data) {
  return request({
    url: "/portalweb/supply",
    method: "post",
    data,
  });
}

// 发布共享订单-新增发布
export function shareOrdersAdd(data) {
  return request({
    url: "/system/manufactureOrder/withMaterials",
    method: "post",
    data,
  });
}

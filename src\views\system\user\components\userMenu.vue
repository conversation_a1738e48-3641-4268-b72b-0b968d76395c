<!--
 * @Author: zhc
 * @Date: 2023-02-03 10:58:54
 * @LastEditTime: 2023-02-13 19:08:08
 * @Description:
 * @LastEditors: zhc
-->
<template>
  <el-menu
    :default-active="this.$route.path"
    class="user-center-menu"
    @select="handleSelect"
    :router="true"
  >
    <el-menu-item index="/user/profile" route="/user/profile">
      <div class="item-hover">
        <i class="menu-icon el-icon-menu"></i>
        <span class="menu-text" slot="title">首页</span>
      </div>
    </el-menu-item>
    <el-menu-item index="/user/supplyDemand" route="/user/supplyDemand">
      <div class="item-hover">
        <i class="menu-icon el-icon-c-scale-to-original"></i>
        <span slot="title">供需管理</span>
      </div>
    </el-menu-item>
    <el-menu-item index="/user/reApplication" route="/user/reApplication">
      <div class="item-hover">
        <i class="menu-icon el-icon-c-scale-to-original"></i>
        <span slot="title">入驻申请</span>
      </div>
    </el-menu-item>
    <el-menu-item index="/user/sharedOrders" route="/user/sharedOrders">
      <div class="item-hover">
        <i class="menu-icon el-icon-c-scale-to-original"></i>
        <span slot="title">共享订单</span>
      </div>
    </el-menu-item>
    <el-menu-item index="/user/emInformation" route="/user/emInformation">
      <div class="item-hover">
        <i class="menu-icon el-icon-c-scale-to-original"></i>
        <span slot="title">用工信息</span>
      </div>
    </el-menu-item>
      <el-menu-item index="/user/equipmentManagement" route="/user/equipmentManagement">
      <div class="item-hover">
        <i class="menu-icon el-icon-c-scale-to-original"></i>
        <span slot="title">设备管理</span>
      </div>
    </el-menu-item>
      <el-menu-item index="/user/workshopManagement" route="/user/workshopManagement">
      <div class="item-hover">
        <i class="menu-icon el-icon-c-scale-to-original"></i>
        <span slot="title">车间管理</span>
      </div>
    </el-menu-item>
    <el-menu-item index="/user/dockingRecords" route="/user/dockingRecords">
      <div class="item-hover">
        <i class="menu-icon el-icon-c-scale-to-original"></i>
        <span slot="title">对接记录</span>
      </div>
    </el-menu-item>
    <el-menu-item index="/user/userCenter" route="/user/userCenter">
      <div class="item-hover">
        <i class="menu-icon el-icon-user"></i>
        <span slot="title">用户中心</span>
      </div>
    </el-menu-item>
    <el-menu-item index="/user/spCertification" route="/user/spCertification">
      <div class="item-hover">
        <i class="menu-icon el-icon-setting"></i>
        <span slot="title">认证设置</span>
      </div>
    </el-menu-item>
    <!-- <el-menu-item index="/user/userInfo" route="/user/userInfo">
      <div class="item-hover">
        <i class="menu-icon el-icon-user"></i>
        <span slot="title">个人资料</span>
      </div>
    </el-menu-item>
    <el-menu-item index="/user/invoiceInfo" route="/user/invoiceInfo">
      <div class="item-hover">
        <i class="menu-icon el-icon-tickets"></i>
        <span slot="title">发票信息</span>
      </div>
    </el-menu-item> -->
    <!-- <el-menu-item
      index="/user/companyInfo"
      route="/user/companyInfo"
      v-show="this.companyStatus"
    >
      <div class="item-hover">
        <i class="menu-icon el-icon-setting"></i>
        <span slot="title">公司信息</span>
      </div>
    </el-menu-item> -->
    <!-- <el-menu-item index="/user/spCertification" route="/user/spCertification">
      <div class="item-hover">
        <i class="menu-icon el-icon-setting"></i>
        <span slot="title">认证设置</span>
      </div>
    </el-menu-item>
    <el-menu-item index="/user/myCollect" route="/user/myCollect">
      <div class="item-hover">
        <i class="menu-icon el-icon-star-off"></i>
        <span slot="title">我的收藏</span>
      </div>
    </el-menu-item>
    <el-menu-item index="/user/mySubscriptions" route="/user/mySubscriptions">
      <div class="item-hover">
        <i class="menu-icon el-icon-finished"></i>
        <span slot="title">我的订阅</span>
      </div>
    </el-menu-item>
    <el-menu-item index="/user/application" route="/user/application">
      <div class="item-hover">
        <i class="menu-icon el-icon-news"></i>
        <span slot="title">应用管理</span>
      </div>
    </el-menu-item>
    <el-menu-item index="/user/orderManage" route="/user/orderManage">
      <div class="item-hover">
        <i class="menu-icon el-icon-c-scale-to-original"></i>
        <span slot="title">订单管理</span>
      </div>
    </el-menu-item> -->
    <!-- <el-menu-item index="/user/policyDeclare" route="/user/policyDeclare">
      <div class="item-hover">
        <i class="menu-icon el-icon-setting"></i>
        <span slot="title">政策申报</span>
      </div>
    </el-menu-item> -->
    <!-- <el-menu-item index="/user/noninductive" route="/user/noninductive">
      <div class="item-hover">
        <i class="menu-icon el-icon-setting"></i>
        <span slot="title" @click="add">无感兑现</span>
      </div>
    </el-menu-item> -->
    <!-- <el-menu-item index="/user/commercial" route="/user/commercial">
      <div class="item-hover">
        <i class="menu-icon el-icon-setting"></i>
        <span slot="title">商机推荐</span>
      </div>
    </el-menu-item>
    <el-menu-item>
      <div class="item-hover">
        <i class="menu-icon el-icon-setting"></i>
        <span slot="title" @click="add">数字化诊断</span>
      </div>
    </el-menu-item>
    <el-menu-item index="/user/companyDemand" route="/user/companyDemand">
      <div class="item-hover">
        <i class="menu-icon el-icon-setting"></i>
        <span slot="title">企业需求</span>
      </div>
    </el-menu-item>
    <el-menu-item index="/user/companyApply" route="/user/companyApply">
      <div class="item-hover">
        <i class="menu-icon el-icon-setting"></i>
        <span slot="title">企业资源</span>
      </div>
    </el-menu-item>
    <el-menu-item index="/user/abutmentRecord" route="/user/abutmentRecord">
      <div class="item-hover">
        <i class="menu-icon el-icon-setting"></i>
        <span slot="title">对接记录</span>
      </div>
    </el-menu-item>
    <el-menu-item index="/user/im" route="/user/im">
      <div class="item-hover">
        <i class="menu-icon el-icon-setting" route="/user/im"></i>
        <span slot="title">即时沟通</span>
      </div>
    </el-menu-item>
    <el-menu-item index="/user/approveSetting" route="/user/approveSetting">
      <div class="item-hover">
        <i class="menu-icon el-icon-setting"></i>
        <span slot="title">认证设置</span>
      </div>
    </el-menu-item>
    <el-menu-item
      index="/user/teamManage"
      route="/user/teamManage"
      v-show="this.companyStatus"
    >
      <div class="item-hover">
        <i class="menu-icon el-icon-setting"></i>
        <span slot="title">团队管理</span>
      </div>
    </el-menu-item> -->
    <!-- <el-menu-item index="/user/notice" route="/user/notice">
      <div class="item-hover">
        <i class="menu-icon el-icon-chat-dot-round"></i>
        <span slot="title">通知中心</span>
      </div>
    </el-menu-item> -->
  </el-menu>
</template>

<script>
import { checkShowMenuRole } from "@/api/system/user";
export default {
  name: "UserMenu",
  props: {},
  data() {
    return {
      activeIndex: this.$route.path,
      companyStatus: false,
      mobile: "",
      key: "QmRlODJTVGhkNg==",
      type: "cG9saWN5Y2FzaA==",
      base64EncodeChars:
        "ABCDEFGHIJKLMNOPORSTUWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",
    };
  },
  created() {
    // this.getUser();
  },
  methods: {
    getUser() {
      // getUserInfo(this.user.id).then((response) => {
      //   this.user = response.data;
      //   this.roleGroup = response.roleGroup;
      //   this.postGroup = response.postGroup;
      // });
      checkShowMenuRole().then((response) => {
        if (response.code == 200) {
          this.companyStatus = true;
        }
      });
    },
    handleSelect(key, keyPath) {
      console.log(key, keyPath);
      console.log(this.$route.path);
    },
    add() {
      window.open("https://zhenduan.ningmengdou.com/digital-diagosis-web/");
    },
  },
};
</script>
<style lang="scss" scoped>
.user-center-menu {
  width: 160px;
  border-right: #fff;
  padding: 20px 0 20px 0;
  background-color: rgb(239, 251, 247);
  .menu-icon {
    color: #333333;
    margin-right: 8px;
    margin-left: 16px;
  }
  .el-menu-item {
    color: #333333;
    font-weight: 500;
  }
  .item-hover:hover {
    color: #21c9b8;
    .menu-icon {
      color: #21c9b8 !important;
    }
  }
  .el-menu-item:hover {
    outline: 0 !important;
    color: #21c9b8 !important;
    background: #fff !important;
  }
  .el-menu-item.is-active {
    color: #fff !important;
    background-color: #21c9b8 !important;
    .menu-icon {
      color: #fff !important;
    }
  }
  .el-menu-item.is-active :hover {
    color: #fff !important;
    .menu-icon {
      color: #fff !important;
    }
  }
}
</style>

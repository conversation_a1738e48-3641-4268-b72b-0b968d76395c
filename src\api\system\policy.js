/*
 * @Author: zhc
 * @Date: 2023-02-08 11:45:25
 * @LastEditTime: 2023-02-08 14:24:57
 * @Description:
 * @LastEditors: zhc
 */
/*
 * @Author: zhc
 * @Date: 2023-02-07 09:59:21
 * @LastEditTime: 2023-02-08 09:41:05
 * @Description:
 * @LastEditors: zhc
 */
/*
 * @Author: zhc
 * @Date: 2023-02-06 14:52:03
 * @LastEditTime: 2023-02-06 17:54:33
 * @Description:
 *
 * @LastEditors: zhc
 */
import request from "@/utils/request";

// 门户PC-查询政策申报提交记录列表
export function listPolicy(params) {
  return request({
    url: "/system/policyApply/list",
    method: "get",
    params: params,
  });
}

// 门户PC-获取政策申报提交记录详细信息
export function getPolicyDetail(id) {
  return request({
    url: "/system/policyApply/getInfo",
    method: "get",
    params: { id: id },
  });
}
// 门户PC-提报撤回
export function revocationPolicy(id) {
  return request({
    url: "/system/policyApply/back",
    method: "get",
    params: { id: id },
  });
}
// 门户PC-保存申报信息-草稿-提审
export function editPolicyApply(params) {
  return request({
    url: "/system/policyApply/submit",
    method: "post",
    data: params,
  });
}

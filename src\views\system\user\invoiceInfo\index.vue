<!--
 * @Author: zhc
 * @Date: 2023-02-03 11:06:49
 * @LastEditTime: 2023-03-24 09:06:27
 * @Description: 
 * @LastEditors: zhc
-->
<template>
  <div class="user-info-page">
    <div class="app-container">
      <el-row :gutter="20">
        <el-col :span="2.5" :xs="24">
          <user-menu activeIndex="1" />
        </el-col>
        <el-col :span="20" :xs="24">
          <div class="user-info-container">
            <div class="header-small">
              <div class="red-tag"></div>
              发票信息
            </div>
            <div class="user-info-card">
              <el-button
                class="edit-button"
                icon="el-icon-edit"
                @click="openEditDialog"
                >点击修改</el-button
              >
              <!-- <el-image
                class="user-info-avatar"
                :src="user.avatar"
                :fit="fit"
              ></el-image> -->

              <!-- <div class="user-name">{{ user.realName || "--" }}</div>
              <div class="phone-class">
                <el-image :src="require('@/assets/user/phone.png')"></el-image>
                <span class="phone-number">{{ user.phonenumber }}</span>
              </div> -->
              <div class="info-box" style="width: 350px">
                <el-form ref="form" :model="user" label-width="80px">
                  <!-- <el-form-item label="抬头类型:">
                    {{ user.headType }}
                  </el-form-item> -->
                  <el-form-item label="发票类型:">
                    <span>{{
                      user.invoiceType == "1"
                        ? "专票"
                        : user.invoiceType == "2"
                        ? "普票"
                        : ""
                    }}</span>
                  </el-form-item>
                  <el-form-item label="公司名称:">
                    {{ user.companyName }}
                  </el-form-item>
                  <el-form-item label="税号:">
                    {{ user.dutyParagraph }}
                  </el-form-item>
                  <el-form-item label="公司地址:">
                    {{ user.address }}
                  </el-form-item>
                  <el-form-item label="公司电话:">
                    {{ user.phone }}
                  </el-form-item>
                  <el-form-item label="开户银行:">
                    {{ user.openAccount }}
                  </el-form-item>
                  <el-form-item label="银行账号:">
                    {{ user.bankAccount }}
                  </el-form-item>
                  <el-form-item label="邮箱地址:">
                    {{ user.email }}
                  </el-form-item>
                  <!-- <el-form-item label="身份认证:">
                    <div class="tag-group">
                      <a
                        class="label-container orange"
                        v-if="this.companyStatus == '1'"
                        @click="jumpToApprove"
                      >
                        <el-image
                          style="width: 12px; height: 12px"
                          :src="
                            require('@/assets/user/authentication_orange.png')
                          "
                        ></el-image>
                        <span>企业认证</span>
                      </a>
                      <a
                        class="label-container red"
                        v-if="personalStatus == '1'"
                        @click="jumpToApprove"
                      >
                        <el-image
                          class="red"
                          style="width: 12px; height: 12px"
                          :src="require('@/assets/user/authentication_red.png')"
                        ></el-image>
                        <span>名片认证</span>
                      </a>
                      <a
                        v-if="
                          this.personalStatus == '0' &&
                          this.companyStatus == '0'
                        "
                        class="label-container"
                        @click="jumpToApprove"
                      >
                        <el-image
                          style="width: 12px; height: 12px"
                          :src="require('@/assets/user/authentication.png')"
                        ></el-image>
                        <span>未认证</span>
                      </a>
                    </div>
                  </el-form-item> -->
                </el-form>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>

      <!-- 添加或修改部门对话框 -->
      <el-dialog
        title="发票信息修改"
        :visible.sync="editDialog"
        width="750px"
        append-to-body
      >
        <el-form ref="form" :model="form" :rules="rules" label-width="80px">
          <!-- <el-form-item label="抬头类型" prop="realName">
            <el-radio v-model="form.headType" label="1">公司</el-radio>
            <el-radio v-model="form.headType" label="2">个人</el-radio>
          </el-form-item> -->
          <el-form-item label="发票类型" prop="realName">
            <el-radio v-model="form.invoiceType" :label="1">专票</el-radio>
            <el-radio v-model="form.invoiceType" :label="2">普票</el-radio>
          </el-form-item>
          <el-form-item label="公司名称" prop="phonenumber">
            <el-input v-model="form.companyName" placeholder="请输入公司名称" />
          </el-form-item>
          <el-form-item label="税号" prop="weixin">
            <el-input v-model="form.dutyParagraph" placeholder="请输入税号" />
          </el-form-item>
          <el-form-item label="公司地址" prop="email">
            <el-input v-model="form.address" placeholder="请输入公司地址" />
          </el-form-item>
          <el-form-item label="公司电话" prop="email">
            <el-input v-model="form.phone" placeholder="请输入公司电话" />
          </el-form-item>
          <el-form-item label="开户银行" prop="email">
            <el-input v-model="form.openAccount" placeholder="请输入开户银行" />
          </el-form-item>
          <el-form-item label="银行账号" prop="email">
            <el-input v-model="form.bankAccount" placeholder="请输入银行账号" />
          </el-form-item>
          <el-form-item label="邮箱地址" prop="email">
            <el-input v-model="form.email" placeholder="请输入邮箱地址" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancelEditDialog">取 消</el-button>
        </div>
      </el-dialog>
      <el-dialog
        title="关联企业"
        :visible.sync="companyDialog"
        width="550px"
        append-to-body
      >
        <el-form ref="form" :model="form" :rules="rules" label-width="80px">
          <el-form-item label="企业名称">
            <el-select
              v-model="companyId"
              filterable
              remote
              reserve-keyword
              placeholder="请输入关键词"
              :remote-method="getCompanyList"
              :loading="companyLoading"
              @change="companyChanged"
              style="width: 100%"
            >
              <el-option
                v-for="item in companyOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="transferCompany">确 定</el-button>
          <el-button @click="cancelComapnyDialog">取 消</el-button>
        </div>
      </el-dialog>
      <el-dialog
        title="修改职务"
        :visible.sync="positionDialog"
        width="800px"
        append-to-body
      >
        <el-row>
          <el-col :span="5">
            <div class="left-container">
              <div
                v-for="(firstData, index) in positionFirstData"
                v-bind:key="firstData.postName"
                :class="
                  index === activeFirstIndex
                    ? 'position-header selected-style'
                    : 'position-header'
                "
              >
                <a @click="selectFisrt(index, firstData)">
                  {{ firstData.postName || "" }}
                </a>
              </div>
            </div>
          </el-col>
          <el-col :span="19">
            <div class="right-container">
              <div
                v-for="childData in positionChildData"
                v-bind:key="childData.postId"
              >
                <div>
                  <div class="second-header">
                    {{ childData.postName || "--" }}
                  </div>
                  <div class="position-container">
                    <a
                      v-for="pos in childData.children"
                      v-bind:key="pos.postId"
                      :class="
                        selectPositionData.postId == pos.postId
                          ? 'position-tag selected'
                          : 'position-tag'
                      "
                      @click="selectPosition(pos)"
                    >
                      {{ pos.postName || "--" }}
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="changePosition">确 定</el-button>
          <el-button @click="cancelPositionDialog">取 消</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { invoiceList, invoiceAdd, invoiceEdit } from "@/api/system/user";
import store from "@/store";
import UserMenu from "../components/userMenu.vue";
import userAvatar from "../profile/userAvatar";
import userInfo from "../profile/userInfo";
import { uploadUrl } from "@/api/oss";
import { getToken } from "@/utils/auth";
import {
  getUserInfo,
  updateUserInfo,
  checkAuthStatus,
  getCompanyListByName,
  transferCompany,
  getPositionData,
} from "@/api/system/user";

export default {
  name: "UserInfo",
  components: { userAvatar, userInfo, UserMenu },
  data() {
    return {
      fit: "cover",
      // 是否显示弹出层
      editDialog: false,
      companyDialog: false,
      positionDialog: false,
      actionUrl: uploadUrl(),
      headers: { Authorization: "Bearer " + getToken() },
      userId: store.getters.userId,
      user: {
        // name: store.getters.name,
        // avatar: store.getters.avatar,
        // id: store.getters.userId,
      },
      form: {
        headType: "1",
        invoiceType: 1,
        companyName: "",
        dutyParagraph: "",
        address: "",
        phone: "",
        openAccount: "",
        bankAccount: "",
        email: "",
      },
      rules: {},
      companyOptions: [],
      positionOptions: [],
      companyLoading: false,
      companyId: "",
      companyName: "",
      companyCode: "",
      personalStatus: "",
      companyStatus: "",
      positionFirstData: [],
      activeFirstIndex: 0,
      positionChildData: [],
      selectPositionData: {},
      identityType: "",
    };
  },
  created() {
    // this.getUser();
    // this.getPositionData();
    // this.isRelevanceCompany();
    this.getInvoiceData();
  },
  methods: {
    getInvoiceData() {
      invoiceList().then((res) => {
        if (res.code === 200) {
          console.log(res, "------------");
          this.user = res.data ? res.data : {};
        }
      });
    },
    isRelevanceCompany() {
      let flag = this.$route.query.relevanceCompany;
      if (flag == "1") {
        this.openComapnyDialog();
      }
    },
    cancelEditDialog() {
      this.editDialog = false;
      this.form = {};
    },
    openEditDialog() {
      if (this.user.id) {
        this.form = { ...this.user };
      } else {
        this.form = {
          headType: "1",
          invoiceType: 1,
          companyName: "",
          dutyParagraph: "",
          address: "",
          phone: "",
          openAccount: "",
          bankAccount: "",
          email: "",
        };
      }
      this.editDialog = true;
    },
    cancelComapnyDialog() {
      this.companyDialog = false;
      this.companyCode = "";
      this.companyId = "";
    },
    openComapnyDialog() {
      this.form = { ...this.user };
      this.companyDialog = true;
    },
    cancelPositionDialog() {
      this.selectPositionData = {};
      this.identityType = "";
      this.positionDialog = false;
    },
    openPositionDialog() {
      this.selectPositionData = {};
      this.identityType = "";
      this.positionDialog = true;
    },
    submitCompanyForm() {},
    getUser() {
      getUserInfo(this.userId).then((response) => {
        this.user = response.data;
        this.roleGroup = response.roleGroup;
        this.postGroup = response.postGroup;
      });
      checkAuthStatus().then((response) => {
        this.personalStatus = response.data.personalStatus;
        this.companyStatus = response.data.companyStatus;
      });
    },
    getPositionData() {
      getPositionData().then((response) => {
        response.data.forEach((item) => {
          this.positionFirstData.push(item);
        });
        this.positionChildData = response.data[0].children;
      });
    },
    selectFisrt(index, data) {
      this.activeFirstIndex = index;
      this.positionChildData = data.children;
    },
    selectPosition(position) {
      this.selectPositionData = position;
      this.identityType = position.postName;
    },
    companyChanged(res) {
      this.companyOptions.forEach((item) => {
        if (item.id == res) {
          this.companyCode = item.creditCode;
          this.companyId = item.id;
          this.companyName = item.name;
        }
      });
    },
    changePosition() {
      if (this.identityType) {
        this.form.identityType = this.identityType;
        this.cancelPositionDialog();
      }
    },
    getCompanyList(query) {
      if (query !== "") {
        getCompanyListByName(query).then((response) => {
          this.companyOptions = response.data;
        });
      }
    },
    transferCompany() {
      transferCompany({
        tianyanId: this.companyId,
        businessNo: this.companyCode,
      }).then((res) => {
        if (res.code == 200) {
          this.cancelComapnyDialog();
          this.form.companyName = this.companyName;
          this.form.bussinessNo = this.companyCode;
          let flag = this.$route.query.relevanceCompany;
          if (flag == "1") {
            this.$modal.msgSuccess("操作成功");
            store.dispatch("GetInfo");
            this.getUser();
          }
        }
      });
    },
    submitForm() {
      if (this.user.id) {
        let data = {
          id: this.user.id,
          ...this.form,
        };
        invoiceEdit(data).then((res) => {
          if (res.code == 200) {
            this.$modal.msgSuccess("操作成功");
            this.cancelEditDialog();
            this.getInvoiceData();
          }
        });
      } else {
        invoiceAdd(this.form).then((res) => {
          if (res.code == 200) {
            this.$modal.msgSuccess("操作成功");
            this.cancelEditDialog();
            this.getInvoiceData();
          }
        });
      }
    },
    handleUploadSuccess(res, file, fileList) {
      //此处写上传oss成功之后的逻辑
      if (res.code == 200) {
        this.form.avatar = res.data.url;
      }
    },
    reset() {},
    jumpToApprove() {
      this.$router.push("/user/approveSetting");
    },
  },
};
</script>

<style lang="scss" scoped>
.el-input {
  height: 36px;
  width: 380px;
  line-height: 36px;
}
.action-class {
  padding-left: 16px;
  color: #21c9b8;
}
.el-button--primary {
  color: #21c9b8;
  background-color: #ffffff;
  border-color: #21c9b8;
}
</style>
<style lang="scss">
.app-container {
  background: #f4f5f9;
  .user-info-container {
    background: #fff;
    height: calc(100vh - 150px);
    padding: 20px;
    position: relative;

    .edit-button {
      position: absolute;
      right: 10px;
      top: 90px;
      margin-left: 30px;
      color: #21c9b8;
      background-color: #ffffff;
      border-color: transparent;
    }
    .user-info-avatar {
      width: 120px;
      height: 120px;
      border-radius: 50%;
    }
    .header-small {
      text-align: center;
      display: flex;
      font-size: 20px;
      font-weight: 500;
      color: #333333;
      line-height: 20px;

      .red-tag {
        margin-right: 12px;
        width: 3px;
        height: 22px;
        background: #21c9b8;
      }
    }
    .user-info-card {
      margin-top: 80px;
      margin-right: 80px;
      text-align: center;
      .user-name {
        margin-top: 10px;
        font-size: 24px;
        font-weight: 500;
        color: #333333;
        line-height: 24px;
      }
      .phone-class {
        width: 100%;
        height: 30px;
        margin-top: 12px;

        .el-image {
          width: 16px;
          height: 16px;
        }
        .phone-number {
          margin-left: 12px;
          font-size: 16px;
          font-weight: 400;
          color: #333333;
          line-height: 16px;
        }
      }
      .info-box {
        margin: 24px auto 0 auto;
        .el-form-item {
          margin-bottom: 4px;
          text-align: start;
        }
        .tag-group {
          display: flex;
          .label-container {
            padding: 4px 12px;
            margin-top: 6px;
            margin-right: 6px;
            height: 24px;
            background: #f0f1f4;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            color: #8a8c94;
            line-height: 12px;
            .el-image {
              margin: 2px 4px 0 0;
            }
          }
          .orange {
            background: rgba(255, 191, 69, 0.2);
            color: #ff8a27;
          }
          .red {
            background: rgba(197, 37, 33, 0.1);
            color: #21c9b8;
          }
        }
      }
    }
    .position-header {
      color: red;
    }
  }
}

.left-container {
  height: 500px;
  margin-right: 10px;
  overflow-y: auto;
  text-align: center;
  .position-header {
    height: 50px;
    line-height: 50px;
    font-weight: 500;

    overflow-y: auto;
  }
  .selected-style {
    background: #21c9b8;
    color: #fff;
  }
}

.right-container {
  height: 500px;
  overflow-y: auto;

  .second-header {
    height: 65px;
    line-height: 65px;

    font-size: 18px;
    font-weight: 500;
  }
  .position-container {
    width: 70%;
    display: flex;
    flex-wrap: wrap;
    .position-tag {
      padding: 0 20px;
      font-size: 15px;
      line-height: 36px;
      color: #000;
    }
    .selected {
      background: #21c9b8 !important;
      color: #fff !important;
    }
    a:hover {
      cursor: pointer;
      color: #21c9b8;
      text-decoration: none;
    }
  }
}
</style>

<template>
  <div class="content">
    <div class="content_banner">
      <div style="height: 37px">供给大厅</div>
      <div style="height: 33px; margin-top: 21px">Supply Hall</div>
      <div class="activity-title-content">
        <div class="activity-search-box">
          <el-form ref="form" class="activity-search-form">
            <el-form-item>
              <el-input
                v-model="keywords"
                placeholder="请输入搜索内容"
                class="activity-search-input"
              >
                <el-button
                  slot="append"
                  class="activity-search-btn"
                  @click="onSearch"
                  >搜索</el-button
                >
              </el-input>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
    <div class="card-container card_top">
      <div class="card_top_item">
        <div class="largeCategory">供给分类：</div>
        <div
          class="smallCategory"
          :class="supplyType === item.dictValue ? 'smallCategoryActive' : ''"
          v-for="(item, index) in supplyTypeList"
          :key="index"
          @click="switchSupplyType(item.dictValue)"
        >
          {{ item.dictLabel }}
        </div>
      </div>
      <div class="card_top_itemLine"></div>
      <div class="card_top_item" v-if="supplyType == '1'">
        <div class="largeCategory">服务类别：</div>
        <div
          class="smallCategory"
          :class="techType === item.dictValue ? 'smallCategoryActive' : ''"
          v-for="(item, index) in techTypeList"
          :key="index"
          @click="switchTechType(item.dictValue)"
        >
          {{ item.dictLabel }}
        </div>
      </div>
      <div class="card_top_item" v-if="supplyType == '2'">
        <div class="largeCategory">产品类别：</div>
        <div
          class="smallCategory"
          :class="productType === item.dictValue ? 'smallCategoryActive' : ''"
          v-for="(item, index) in productTypeList"
          :key="index"
          @click="switchProductTypeType(item.dictValue)"
        >
          {{ item.dictLabel }}
        </div>
      </div>
      <!-- <div class="card_top_itemLine"></div>
      <div class="card_top_item">
        <div class="largeCategory">成果阶段：</div>
        <div class="smallCategory" :class="achieveStage === item.dictValue ? 'smallCategoryActive' : ''"
          v-for="(item, index) in achieveStageList" :key="index" @click="switchAchieveStage(item.dictValue)">
          {{ item.dictLabel }}
        </div>
      </div>
      <div class="card_top_itemLine"></div>
      <div class="card_top_item">
        <div class="largeCategory">合作方式：</div>
        <div class="smallCategory" :class="cooperationMode === item.dictValue ? 'smallCategoryActive' : ''
          " v-for="(item, index) in cooperationModeList" :key="index" @click="switchCooperationMode(item.dictValue)">
          {{ item.dictLabel }}
        </div>
      </div> -->
      <div class="buttonStyle">
        <div class="imgStyle" @click="initPage">
          <img
            style="width: 100%; height: 100%"
            src="../../../assets/serviceSharing/reset.png"
            alt=""
          />
        </div>
        <div class="buttonText" @click="refresh">重置筛选</div>
      </div>
    </div>
    <!-- 底部内容 -->
    <div class="card-container" v-loading="loading">
      <div class="content_bottom" v-if="supplyList && supplyList.length > 0">
        <div
          class="content_bottom_item tr2"
          v-for="(item, index) in supplyList"
          :key="index"
          @click="goDetail(item.id)"
        >
          <div class="detailTitle textOverflow1 tr2">
            {{ item.title }}
          </div>
          <div class="demandChunk">
            <!-- 左侧图片 -->
            <div>
              <img
                style="width: 130px; height: 130px"
                :src="
                  item.imageUrl
                    ? item.imageUrl
                    : require('../../../assets/demand/xqimgdefault.png')
                "
                alt=""
              />
            </div>
            <!-- 右侧内容 -->
            <div class="demand_right">
              <div class="demandTopRightflex">
                <div class="detailrightTitle tr2">供给方：</div>
                <div class="detailrightContent tr2 textOverflow1">
                  {{ item.organization }}
                </div>
              </div>
              <!-- <div class="demandTopRightflex">
                <div class="detailrightTitle">应用领域：</div>
                <div class="detailrightContent">
                  {{ item.applicationAreaName }}
                </div>
              </div> -->
              <!-- <div class="detailrightTitle2 textOverflow2">
                {{ item.desc }}
              </div> -->
              <div class="demandTopRightflex">
                <div class="detailrightTitle tr2">技术类别：</div>
                <div class="detailrightContent tr2 textOverflow1">
                  {{ item.technologyCategoryName }}
                </div>
              </div>
              <div class="demandTopRightflex">
                <div class="detailrightTitle tr2">发布时间：</div>
                <div class="detailrightContent tr2 textOverflow1">
                  {{ item.createTime }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="none-class" v-else>
        <el-image
          style="width: 160px; height: 160px"
          :src="require('@/assets/user/none.png')"
          :fit="fit"
        ></el-image>
        <div class="text">暂无数据</div>
      </div>
      <!-- 分页 -->
      <div class="pageStyle">
        <el-pagination
          v-if="supplyList && supplyList.length > 0"
          background
          layout="prev, pager, next"
          class="activity-pagination"
          :page-size="pageSize"
          :current-page="pageNum"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>
<script>
import { listData } from "@/api/system/dict/data";
import { supplyData } from "@/api/home";

export default {
  name: "demandHall",
  data() {
    return {
      loading: false,
      pageNum: 1,
      pageSize: 10,
      total: 0,
      keywords: "",
      form: {},
      supplyTypeList: [
        {
          dictLabel: "喷漆工",
        },
        {
          dictLabel: "安全员",
        },
        {
          dictLabel: "采购员",
        },
        {
          dictLabel: "巡逻员",
        },
        {
          dictLabel: "机械制图员",
        },
        {
          dictLabel: "运营专员",
        },
        {
          dictLabel: "宣传员",
        },
        {
          dictLabel: "项目经理",
        },
        {
          dictLabel: "文员",
        },
        {
          dictLabel: "其它",
        },
      ],
      supplyType: "1",
      techTypeList: [
        {
          dictLabel: "研究生",
        },
        {
          dictLabel: "本科",
        },
        {
          dictLabel: "大专",
        },
        {
          dictLabel: "高中",
        },
        {
          dictLabel: "中专",
        },
        {
          dictLabel: "其它",
        },
      ],
      techType: "",
      productTypeList: [],
      productType: "",
      achieveStageList: [
        {
          dictLabel: "特级工程师",
        },
        {
          dictLabel: "高级工程师",
        },
        {
          dictLabel: "工程师",
        },
        {
          dictLabel: "助理工程师",
        },
      ],
      achieveStage: "",
      cooperationModeList: [
        {
          dictLabel: "在职",
        },
        {
          dictLabel: "离职",
        },
      ],
      cooperationMode: "",
      supplyList: [
        {
          title: "普拉多 2025款 2.4T 旗舰VX版 5座",
          url: require("../../../assets/demand/xqimgdefault.png"),
          appliArea: "",
          requireType: "其它",
          desc: "中大型SUV，油电混合，最大功率(kW)243，最大扭矩(N·m)630，变速箱8挡手自一体，长*宽*高(mm)4935*1980*1935，车身结构5门5座SUV， 最高车速(km/h)170",
          publishTime: "2025-02-28 09:49:21",
        },
        {
          title: "普拉多 2025款 2.4T 旗舰VX版 5座",
          url: require("../../../assets/demand/xqimgdefault.png"),
          appliArea: "",
          requireType: "其它",
          desc: "中大型SUV，油电混合，最大功率(kW)243，最大扭矩(N·m)630，变速箱8挡手自一体，长*宽*高(mm)4935*1980*1935，车身结构5门5座SUV， 最高车速(km/h)170",
          publishTime: "2025-02-28 09:49:21",
        },
        {
          title: "普拉多 2025款 2.4T 旗舰VX版 5座",
          url: require("../../../assets/demand/xqimgdefault.png"),
          appliArea: "",
          requireType: "其它",
          desc: "中大型SUV，油电混合，最大功率(kW)243，最大扭矩(N·m)630，变速箱8挡手自一体，长*宽*高(mm)4935*1980*1935，车身结构5门5座SUV， 最高车速(km/h)170",
          publishTime: "2025-02-28 09:49:21",
        },
        {
          title: "普拉多 2025款 2.4T 旗舰VX版 5座",
          url: require("../../../assets/demand/xqimgdefault.png"),
          appliArea: "",
          requireType: "其它",
          desc: "中大型SUV，油电混合，最大功率(kW)243，最大扭矩(N·m)630，变速箱8挡手自一体，长*宽*高(mm)4935*1980*1935，车身结构5门5座SUV， 最高车速(km/h)170",
          publishTime: "2025-02-28 09:49:21",
        },
        {
          title: "普拉多 2025款 2.4T 旗舰VX版 5座",
          url: require("../../../assets/demand/xqimgdefault.png"),
          appliArea: "",
          requireType: "其它",
          desc: "中大型SUV，油电混合，最大功率(kW)243，最大扭矩(N·m)630，变速箱8挡手自一体，长*宽*高(mm)4935*1980*1935，车身结构5门5座SUV， 最高车速(km/h)170",
          publishTime: "2025-02-28 09:49:21",
        },
        {
          title: "普拉多 2025款 2.4T 旗舰VX版 5座",
          url: require("../../../assets/demand/xqimgdefault.png"),
          appliArea: "",
          requireType: "其它",
          desc: "中大型SUV，油电混合，最大功率(kW)243，最大扭矩(N·m)630，变速箱8挡手自一体，长*宽*高(mm)4935*1980*1935，车身结构5门5座SUV， 最高车速(km/h)170",
          publishTime: "2025-02-28 09:49:21",
        },
        {
          title: "普拉多 2025款 2.4T 旗舰VX版 5座",
          url: require("../../../assets/demand/xqimgdefault.png"),
          appliArea: "",
          requireType: "其它",
          desc: "中大型SUV，油电混合，最大功率(kW)243，最大扭矩(N·m)630，变速箱8挡手自一体，长*宽*高(mm)4935*1980*1935，车身结构5门5座SUV， 最高车速(km/h)170",
          publishTime: "2025-02-28 09:49:21",
        },
        {
          title: "普拉多 2025款 2.4T 旗舰VX版 5座",
          url: require("../../../assets/demand/xqimgdefault.png"),
          appliArea: "",
          requireType: "其它",
          desc: "中大型SUV，油电混合，最大功率(kW)243，最大扭矩(N·m)630，变速箱8挡手自一体，长*宽*高(mm)4935*1980*1935，车身结构5门5座SUV， 最高车速(km/h)170",
          publishTime: "2025-02-28 09:49:21",
        },
        {
          title: "普拉多 2025款 2.4T 旗舰VX版 5座",
          url: require("../../../assets/demand/xqimgdefault.png"),
          appliArea: "",
          requireType: "其它",
          desc: "中大型SUV，油电混合，最大功率(kW)243，最大扭矩(N·m)630，变速箱8挡手自一体，长*宽*高(mm)4935*1980*1935，车身结构5门5座SUV， 最高车速(km/h)170",
          publishTime: "2025-02-28 09:49:21",
        },
        {
          title: "普拉多 2025款 2.4T 旗舰VX版 5座",
          url: require("../../../assets/demand/xqimgdefault.png"),
          appliArea: "",
          requireType: "其它",
          desc: "中大型SUV，油电混合，最大功率(kW)243，最大扭矩(N·m)630，变速箱8挡手自一体，长*宽*高(mm)4935*1980*1935，车身结构5门5座SUV， 最高车速(km/h)170",
          publishTime: "2025-02-28 09:49:21",
        },
      ],
      fit: "cover",
    };
  },
  created() {
    this.getSupplyDict(); // 供给类型
    this.getTechTypeDict(); // 技术类别
    this.getStageDict(); // 成果阶段
    this.getCooperationDict(); // 合作方式
    this.getProductTypeDict(); // 产品类别
    this.getList();
  },
  methods: {
    /** 查询字典数据列表 */
    getSupplyDict() {
      let params = { dictType: "supply_type" };
      listData(params).then((response) => {
        this.supplyTypeList = response.rows;
        // this.supplyTypeList.unshift({ dictValue: "", dictLabel: "全部" });
      });
    },
    getTechTypeDict() {
      let params = { dictType: "technology_category" };
      listData(params).then((response) => {
        this.techTypeList = response.rows;
        this.techTypeList.unshift({ dictValue: "", dictLabel: "全部" });
      });
    },
    getProductTypeDict() {
      let params = { dictType: "product_category" };
      listData(params).then((response) => {
        this.productTypeList = response.rows;
        this.productTypeList.unshift({ dictValue: "", dictLabel: "全部" });
      });
    },
    getStageDict() {
      let params = { dictType: "supply_process" };
      listData(params).then((response) => {
        this.achieveStageList = response.rows;
        this.achieveStageList.unshift({ dictValue: "", dictLabel: "全部" });
      });
    },
    getCooperationDict() {
      let params = { dictType: "supply_cooperation" };
      listData(params).then((response) => {
        this.cooperationModeList = response.rows;
        this.cooperationModeList.unshift({ dictValue: "", dictLabel: "全部" });
      });
    },
    getList() {
      this.loading = true;
      let params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        type: this.supplyType,
        technologyCategory: this.techType,
        productType: this.productType,
        process: this.achieveStage,
        cooperationType: this.cooperationMode,
        keyword: this.keywords,
      };
      supplyData(params).then((res) => {
        if (res.code === 200) {
          this.supplyList = res.rows;
          this.total = res.total;
          this.loading = false;
        }
      });
    },
    onSearch() {
      this.getList();
    },
    handleSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.getList();
    },
    handleCurrentChange(pageNum) {
      this.pageNum = pageNum;
      this.getList();
    },
    switchSupplyType(value) {
      this.pageNum = 1;
      this.supplyType = value;
      this.getList();
    },
    switchTechType(value) {
      this.pageNum = 1;
      this.techType = value;
      this.getList();
    },
    switchProductTypeType(value) {
      this.pageNum = 1;
      this.productType = value;
      this.getList();
    },
    switchAchieveStage(value) {
      this.pageNum = 1;
      this.achieveStage = value;
      this.getList();
    },
    switchCooperationMode(value) {
      this.pageNum = 1;
      this.cooperationMode = value;
      this.getList();
    },
    goDetail(id) {
      this.$router.push("/supplyDetail?id=" + id);
    },
    initPage() {
      this.getList();
    },
    refresh() {
      this.pageNum = 1;
      this.supplyType = "";
      this.techType = "";
      this.achieveStage = "";
      this.cooperationMode = "";
      this.getList();
    },
  },
};
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  padding-bottom: 60px;
}

.content_banner {
  width: 100%;
  height: 300px;
  background-image: url("../../../assets/release/banner.png");
  background-size: 100% 100%;
  text-align: center;
  margin: 0 auto;
  padding-top: 71px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  font-size: 40px;
  color: #000;
}

.activity-title-content {
  width: 100%;

  // background-color: #fff;
  .activity-title-box {
    width: 336px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 60px 0 40px;

    .activity-title {
      font-size: 40px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #333;
      line-height: 40px;
      padding: 0 40px;
    }

    .activity-divider {
      width: 48px;
      height: 4px;
      background: #21c9b8;
    }
  }

  .activity-search-box {
    margin-top: 40px;

    .activity-search-form {
      text-align: center;

      .activity-search-input {
        width: 792px;
        height: 54px;

        .activity-search-btn {
          width: 100px;
        }
      }
    }
  }
}

.card_top {
  background: #ffffff;
  border-radius: 2px;
  margin-top: 30px;
  padding: 58px 60px 32px 62px;

  .card_top_item {
    display: flex;
    align-items: center;
    margin-top: 20px;

    .largeCategory {
      width: 90px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 18px;
      color: #222222;
      margin-right: 28px;
    }

    .smallCategory {
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 14px;
      color: #666666;
      padding: 12px 24px;
      cursor: pointer;
    }

    .smallCategoryActive {
      background: #e0f7f5;
      border-radius: 2px;
      color: #21c9b8;
    }
  }

  .card_top_item:nth-child(1) {
    margin-top: 0;
  }

  .card_top_itemLine {
    width: 100%;
    height: 1px;
    background: #eeeeee;
    margin-top: 20px;
  }

  .buttonStyle {
    margin-top: 9px;
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .imgStyle {
      width: 19px;
      height: 16px;
      cursor: pointer;
    }

    .buttonText {
      margin-left: 10px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 16px;
      color: #21c9b8;
      cursor: pointer;
    }
  }
}

.content_bottom {
  display: flex;
  align-items: center;
  flex-wrap: wrap;

  .content_bottom_item {
    margin-top: 20px;
    width: 590px;
    height: 208px;
    background: #ffffff;
    box-shadow: 0px 4px 18px 2px #e8f1fa;
    border-radius: 10px;
    padding: 20px;
    cursor: pointer;
    position: relative;
    z-index: 1;
    overflow: hidden;

    &:before {
      content: "";
      z-index: -1;
      position: absolute;
      top: 100%;
      left: 100%;
      width: 86px;
      height: 86px;
      border-radius: 50%;
      background-color: #21c9b8;
      transform-origin: center;
      transform: translate3d(-50%, -50%, 0) scale3d(0, 0, 0);
      transition: transform 0.3s ease-in;
    }

    .detailTitle {
      height: 30px;
      color: rgba(51, 51, 51, 1);
      font-size: 18px;
      margin-bottom: 10px;
    }

    .textOverflow1 {
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
    }

    .textOverflow2 {
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    .demandChunk {
      display: flex;
      justify-content: space-between;

      .demand_right {
        width: 413px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
      }

      .demandTopRightflex {
        display: flex;
        line-height: 24px;
      }

      .detailrightTitle {
        color: rgba(153, 153, 153, 1);
        font-size: 14px;
      }

      .detailrightTitle2 {
        color: rgba(0, 0, 0, 0.85);
        font-size: 14px;
      }

      .detailrightContent {
        width: 343px;
        color: rgba(51, 51, 51, 1);
        font-size: 14px;
      }
    }
  }

  .content_bottom_item:hover {
    box-shadow: 0px 3px 20px 0px rgba(132, 212, 178, 0.6);
    scale: 1.01;

    div {
      color: #ffffff !important;
    }

    &::before {
      transform: translate3d(-50%, -50%, 0) scale3d(15, 15, 15);
    }
  }

  .content_bottom_item:nth-child(2n) {
    margin-left: 20px;
  }
}

.pageStyle {
  margin-top: 60px;
  width: 100%;
  text-align: center;
}
</style>
<style lang="scss">
.activity-search-input {
  .el-input__inner {
    height: 54px;
    background: #fff;
    border-radius: 27px 0 0 27px;
    border: 1px solid #d9d9d9;
    font-size: 16px;
    font-family: PingFangSC-Regular, PingFang SC;
    line-height: 24px;
    padding-left: 30px;
  }

  .el-input-group__append {
    border-radius: 0px 100px 100px 0px;
    background: #21c9b8;
    font-size: 16px;
    font-family: PingFangSC-Regular, PingFang SC;
    color: #fff;
    line-height: 24px;
  }
}

.none-class {
  text-align: center;
  padding: 8% 0;

  .text {
    font-size: 14px;
    font-weight: 400;
    color: #999999;
    line-height: 14px;
  }
}
</style>

/*
 * @Author: zhc
 * @Date: 2023-02-02 11:35:59
 * @LastEditTime: 2023-06-08 10:38:55
 * @Description:
 * @LastEditors: zhc
 */
import Vue from "vue";

import Cookies from "js-cookie";

import Element from "element-ui";
import "./assets/styles/element-variables.scss";

import "@/assets/styles/index.scss"; // global css
import "@/assets/styles/ruoyi.scss"; // ruoyi css
import App from "./App";
import store from "./store";
import router from "./router";
import directive from "./directive"; // directive
import plugins from "./plugins"; // plugins
import { download } from "@/utils/request";
import * as RongIMLib from "@rongcloud/imlib-next";
import { defineCustomElements, imkit } from "@rongcloud/imkit";
import custom_service from "@/utils/im/custom_service.js";

import "./assets/icons"; // icon
import "./permission"; // permission control
import { getDicts } from "@/api/system/dict/data";
import { getConfigKey } from "@/api/system/config";

import {
  parseTime,
  resetForm,
  addDateRange,
  selectDictLabel,
  selectDictLabels,
  handleTree,
} from "@/utils/ruoyi";
// 分页组件
import Pagination from "@/components/Pagination";
// 自定义表格工具组件
import RightToolbar from "@/components/RightToolbar";
// 富文本组件
import Editor from "@/components/Editor";
// 文件上传组件
import FileUpload from "@/components/FileUpload";
// 图片上传组件
import ImageUpload from "@/components/ImageUpload";
// 图片预览组件
import ImagePreview from "@/components/ImagePreview";
// 字典标签组件
import DictTag from "@/components/DictTag";
// 头部标签组件
import VueMeta from "vue-meta";
// 字典数据组件
import DictData from "@/components/DictData";

import VideoPlayer from "vue-video-player/src";
import "vue-video-player/src/custom-theme.css";
import "video.js/dist/video-js.css";

//动画
// 使用样式库
import animated from "animate.css";
Vue.use(animated);

// 滚动动画 wow.js
import { WOW } from "wowjs";
Vue.prototype.$wow = new WOW({
  boxClass: "wow", // default
  animateClass: "animated", // default
  offset: 100, // default
  mobile: false, // default
  live: false,

  // live为true时，控制台会提示：MutationObserver is not supported by your browser. & WOW.js cannot detect dom mutations, please call .sync() after loading new content.

  callback: function (box) {
    console.log("WOW: animating <" + box.tagName.toLowerCase() + ">");
  },
});

// IMKit 初始化参数 appkey，从开发者后台获取
// const RONGYUN_APP_KEY = "qd46yzrfq63ff";
let token = "";
// window.RONGYUN_APP_KEY = RONGYUN_APP_KEY;
window.token = token;
import axios from "axios";
//main.js
import { Base64 } from "js-base64";
Vue.prototype.$Base64 = Base64;
Vue.prototype.$axios = axios;

// 全局方法挂载
Vue.prototype.getDicts = getDicts;
Vue.prototype.getConfigKey = getConfigKey;
Vue.prototype.parseTime = parseTime;
Vue.prototype.resetForm = resetForm;
Vue.prototype.addDateRange = addDateRange;
Vue.prototype.selectDictLabel = selectDictLabel;
Vue.prototype.selectDictLabels = selectDictLabels;
Vue.prototype.download = download;
Vue.prototype.handleTree = handleTree;

// 全局组件挂载
Vue.component("DictTag", DictTag);
Vue.component("Pagination", Pagination);
Vue.component("RightToolbar", RightToolbar);
Vue.component("Editor", Editor);
Vue.component("FileUpload", FileUpload);
Vue.component("ImageUpload", ImageUpload);

Vue.use(directive);
Vue.use(plugins);
Vue.use(VueMeta);
Vue.use(VideoPlayer);
DictData.install();

defineCustomElements();
/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online! ! !
 */

Vue.use(Element, {
  size: Cookies.get("size") || "medium", // set element-ui default size
});

Vue.config.productionTip = false;

new Vue({
  el: "#app",
  router,
  store,
  beforeCreate: () => {
    // 初始化imkit
    // let libOption = { appkey: RONGYUN_APP_KEY };
    // RongIMLib.init(libOption);
    // imkit.init({
    //   appkey: RONGYUN_APP_KEY,
    //   service: custom_service,
    //   libOption: libOption,
    // });
    // const PersonMessage = imkit.registerMessageType(
    //   "kit:person",
    //   true,
    //   true,
    //   [],
    //   false
    // );
    // window.PersonMessage = PersonMessage;
  },
  render: (h) => h(App),
});

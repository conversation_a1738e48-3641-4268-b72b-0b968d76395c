body{
	width: 100%;
	margin: 0px;
}
input::placeholder{
	color: rgba(194, 194, 194, 1);
	font-size: 14px;
}
.background{
	width: 100%;
	background: rgb(247, 248, 250);
}
.list-top{
	background-size: 100%;
	background-repeat: no-repeat;
	background-position: 0 0;
	text-align: center;
	position: relative;
}
.list-top p{
	margin: 0px;
	color: rgba(255, 255, 255, 1);
	text-align: center;
	font-family: SourceHanSansSC-regular;
}
.list-top p.title{
	font-size: 36px;
	padding: 40px 0px 10px 0px;
	line-height: 53px;
}
.list-top p.english-title{
	line-height: 40px;
	opacity: 0.8;
	font-size: 28px;
	font-weight: 300;
}
.list-top .box{
	position: absolute;
	left: calc( 50% - 613px);
	width: 1226px;
	height: 150px;
	line-height: 20px;
	border-radius: 4px;
	background: linear-gradient(180deg, rgba(244,246,249,0.95) 0%,rgba(255,255,255,0.95) 100%);
	color: rgba(16, 16, 16, 1);
	font-size: 14px;
	box-shadow: 0px 0px 8px 0px rgba(218, 235, 253, 75);
	font-family: Roboto;
	border: 2px solid rgba(255, 255, 255, 1);
}
.list-top .box div{
	width: 677px;
	line-height: 50px;
	margin: 0px auto;
	text-align: left;
	color: rgba(194, 194, 194, 1);
	font-size: 12px;
}
.list-top .box input{
	height: 30px;
	line-height: 30px;
	padding: 10px 20px;
	border-radius: 4px;
	background-color: rgba(255, 255, 255, 1);
	color: rgba(153, 153, 153, 1);
	font-size: 14px;
	text-align: left;
	font-family: Roboto;
	border: 0px;
	width: 637px;
	outline: none;
}
.list-top .box button{
	cursor: pointer;
	height: 50px;
	width: 80px;
	line-height: 50px;
	border-radius: 0px 4px 4px 0px;
	background-color: #428AFA;
	color: #fff;
	font-size: 14px;
	border: 0px;
	padding: 0px;
	position: absolute;
	right: 0px;
	top: 0px;
}
.list-top .box div span{
	cursor: pointer;
	margin-left: 15px;
	color: rgba(51, 51, 51, 1);
}
.list-top .img-box{
	height: 388px;
	width: 1160px;
	margin: 0px auto;
}
.list-top .img-box div{
	display:inline-block;
	width: 644px;
	float: left;
	vertical-align: top;
}
.list-top .img-box div p{
	color: #333;
	text-align: left;
}
.list-top .img-box img{
	width: 414px;
	height: 406px;
	float: right;
	vertical-align: top;
}
.list-top .tip{
	width: 100%;
	background-color: #fff;
	height: 200px;
	margin-top: 50px;
}
.list-top .tip div{
	width: 1226px;
	text-align: left;
	color: rgba(51, 51, 51, 1);
	font-size: 14px;
	padding-top: 130px;
	padding-bottom: 14px;
	margin: 0px auto;
}
.list-top .tip div.tip-sub2{
	padding-top: 14px;
	border-top: 1px dashed #ececec;
}
.list-top .tip div span.first{
	width: 70px;
	padding: 0px;
}
.list-top .tip span{
	display: inline-block;
	height: 30px;
	line-height: 30px;
	border-radius: 4px;
	margin-right: 15px;
	padding: 0px 20px;
	cursor: pointer;
}
.list-top .tip span.active{
	border-color: #21c9b8;
    		color: #21c9b8;
}
.list{
	width: 1226px;
	margin: 0px auto;
	padding-top: 40px;
}
.list .info-box{
	width: 603px;
	background-color: rgba(255, 255, 255, 1);
	display: inline-block;
	text-align: left;
	margin-bottom: 20px;
	cursor: pointer;
	text-align: left;
	font-size: 0px;
}
.list .info-box > div{
	display: inline-block;
	vertical-align: top;
	margin-top: 20px;
	margin-bottom: 20px;
	margin-left: 20px;
}
.list .info-box:nth-child(odd){
	margin-right: 20px;
}
p.omit{
	width: 100%;
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
}
.list .info-box p.omit{
	font-size: 14px;
	line-height: 28px;
	color: rgba(102, 102, 102, 1);
}
.page{
	text-align: center;
	height: 70px;
	margin-top: 20px;
}
.el-pagination.is-background .btn-next,
.el-pagination.is-background .btn-prev,
.el-pagination.is-background .el-pager li{
	border:1px solid #ddd;
	background-color: #fff;
}
.el-pager .more::before{
	line-height: 26px;
}
.el-pagination.is-background .el-pager li:hover{
	color: #21c9b8 !important;
	height: 28px !important;
}
.el-pagination.is-background .el-pager li:not(.disabled).active{
	border-color: #21c9b8;
	color: #21c9b8;
	background-color: #fff;
}
.el-pager li.btn-quicknext, .el-pager li.btn-quickprev{
	line-height: 26px;
}
.el-pagination.is-background .el-pager li{
	width: auto !important;
}

/*详情*/
.detail-box{
	width: 1226px;
	margin: 0px auto;
	margin-bottom: 20px;
	background-color: #fff;
	font-size: 0px;
}
.detail-box >div{
	display: inline-block;
	vertical-align: top;
}
.detail-box .detail-info{
	height: 35px;
	line-height: 35px;
	color: rgba(16, 16, 16, 1);
	font-size: 14px;
}
.detail-box .detail-info h3{
	margin: 0px;
	line-height: 38px;
	color: rgba(51, 51, 51, 1);
	font-size: 24px;
	font-weight: 400;
}
.detail-box .detail-info div span{
	display: inline-block;
	width: 70px;
	color: rgba(153, 153, 153, 1);
}
.detail-box .detail-info button{
	display: inline-block;
	cursor: pointer;
	margin-right: 10px;
	margin-top: 15px;
	width: 110px;
	height: 40px;
	line-height: 40px;
	border-radius: 4px;
	padding: 0px;
	font-size: 14px;
	text-align: left;
	font-family: Roboto;
	background-color: rgba(247, 154, 71, 0.2);
	color: rgba(247, 154, 71, 1);
	border: 0px;
}
.detail-box .detail-info button img{
	width: 15px;
	height: 15px;
	margin: 12.5px 10px;
	vertical-align: top;
}
.detail-box .detail-info button.dark{
	background-color: rgba(241, 162, 61, 1);
	color: rgba(255, 255, 255, 1);
}
.detail-box .title span{
	display: inline-block;
	height: 20px;
	width: 3px;
	background-color: rgba(247, 154, 71, 100);
	border-radius: 3px;
	margin: 5px 18px;
	vertical-align: top;
	float: none;
}
.detail-box .title{
	line-height: 30px;
	height: 30px;
	color: rgba(16, 16, 16, 1);
	font-size: 20px;
	margin: 0px;
	padding: 20px 0px;
	font-weight: 400;
}
.detail-box div.msg{
	color: rgba(102, 102, 102, 1);
	font-size: 16px;
	font-family: SourceHanSansSC-light;
	padding: 0px 40px 40px 40px;
	margin: 0px;
}
.tool-tip{
	max-width: 360px;
}
.sort{
	padding: 0px !important;
	margin: 0px !important;
}
.sort select{
	vertical-align: top;
	border: none;
	line-height: 18px;
}
.sort img{
	width: 17px;
	height: 17px;
	vertical-align: top;
	cursor: pointer;
}

<template>
  <div class="notice-detail-container">
    <div class="notice-detail-banner">
      <img src="../../assets/notice/noticeDetailBanner.png" alt="" />
    </div>
    <div class="notice-detail-title-box">
      <div class="notice-divider"></div>
      <div class="notice-detail-title">政策详情</div>
      <div class="notice-divider"></div>
    </div>
    <div class="notice-detail-content">
      <div class="notice-detail-box">
        <div class="notice-info-title">
          {{ data.title }}
        </div>
        <div class="notice-info-time">{{ data.updateTime }}</div>
        <div class="notice-info-divider"></div>
        <div class="notice-info-box">
          <div
            v-html="data.content"
            class="notice-info-content ql-editor"
          ></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getInfoDetail } from "@/api/notice";

export default {
  name: "policyPage",
  data() {
    return {
      data: {},
    };
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      let id = this.$route.query.id;
      this.loading = true;
      getInfoDetail({ id: id })
        .then((res) => {
          this.loading = false;
          this.data = res.data || {};
        })
        .catch(() => {
          this.loading = false;
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.notice-detail-container {
  width: 100%;
  padding: 0 0 100px;
  background: #f4f5f9;
  .notice-detail-banner {
    width: 100%;
    height: 26vh;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .notice-detail-title-box {
    width: 336px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 60px 0 40px;
    .notice-detail-title {
      font-size: 40px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #333;
      line-height: 40px;
      padding: 0 40px;
    }
    .notice-divider {
      width: 48px;
      height: 4px;
      background: #21c9b8;
    }
  }
  .notice-detail-content {
    width: 1200px;
    background: #fff;
    margin: 0 auto;
    .notice-detail-box {
      padding: 60px 116px 100px;
      font-family: PingFangSC-Semibold, PingFang SC;
      .notice-info-title {
        width: 960px;
        font-size: 32px;
        font-weight: 600;
        color: #333;
        line-height: 32px;
        text-overflow: ellipsis; /*让截断的文字显示为点点。还有一个值是clip意截断不显示点点*/
        white-space: nowrap; /*让文字不换行*/
        overflow: hidden; /*超出要隐藏*/
        word-wrap: break-word;
      }
      .notice-info-time {
        font-size: 12px;
        color: #999;
        line-height: 12px;
        padding-top: 40px;
      }
      .notice-info-divider {
        width: 100%;
        height: 1px;
        background: #e8e8e8;
        margin-top: 10px;
      }
      .notice-info-box {
        padding-top: 40px;
      }
    }
  }
}
</style>

<style lang="scss">
.notice-detail-container {
  .notice-info-content {
    word-break: break-all;
    font-size: 16px;
    line-height: 28px;
    color: #333;
    font-family: PingFangSC-Regular, PingFang SC;
    img {
      max-width: 100%;
    }
  }
}
</style>

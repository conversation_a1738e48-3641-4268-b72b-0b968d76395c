/*
 * @Author: zhc
 * @Date: 2023-02-10 09:37:43
 * @LastEditTime: 2023-02-21 16:27:10
 * @Description:
 * @LastEditors: zhc
 */
/*
 * @Author: zhc
 * @Date: 2023-02-08 11:45:25
 * @LastEditTime: 2023-02-08 14:24:57
 * @Description:
 * @LastEditors: zhc
 */
/*
 * @Author: zhc
 * @Date: 2023-02-07 09:59:21
 * @LastEditTime: 2023-02-08 09:41:05
 * @Description:
 * @LastEditors: zhc
 */
/*
 * @Author: zhc
 * @Date: 2023-02-06 14:52:03
 * @LastEditTime: 2023-02-06 17:54:33
 * @Description:
 *
 * @LastEditors: zhc
 */
import request from "@/utils/request";

// 获取当前用户所属公司的所有用户(非管理员)
export function getCompanyUserList(params) {
  return request({
    url: "/system/company/mag/getCompanyUserList",
    method: "get",
    params: params,
  });
}
// 申请记录分页列表
export function getApplyList(params) {
  return request({
    url: "/system/company/apply/list",
    method: "get",
    params: params,
  });
}

// 请离、管理员转移按钮权限判断
export function checkManagerAuth() {
  return request({
    url: "/system/company/apply/checkManagerAuth",
    method: "get",
  });
}

// 验证手机验证码
export function checkSmsCode(params) {
  return request({
    url: "/system/company/apply/checkSmsCode",
    method: "get",
    params: params,
  });
}
// 同意（1.用户加入企业2.发送系统消息）
export function applyAgree(id) {
  return request({
    url: "/system/company/apply/agree",
    method: "post",
    data: { id: id },
  });
}
// 拒绝（1.发送系统消息）
export function applyRefuse(id) {
  return request({
    url: "/system/company/apply/refuse",
    method: "post",
    data: { id: id },
  });
}
// 门户PC-保存申报信息-草稿-提审
export function editPolicyApply(params) {
  return request({
    url: "/system/policyApply/submit",
    method: "post",
    data: params,
  });
}
// 请离
export function askResignation(params) {
  return request({
    url: "/system/company/apply/askResignation",
    method: "post",
    data: params,
  });
}
// 转移管理员
export function transferManager(params) {
  return request({
    url: "/system/company/apply/transferManager",
    method: "post",
    data: params,
  });
}

@charset "utf-8";
.newsBox{
    width: 100%;
    height: 1034px;
}
.newsContent{
    width: 1200px;
    margin: 0 auto;
}
.newsBox .newsBottom{
    position: relative;
    margin-top: 30px;
}
.newsBox .newsBottom:before{
    content: "";
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    width: 62%;
    background: #21c9b8;
    z-index: 0;
}
.newsBottom{
    display: flex;
    justify-content: space-between;
    height: 360px;
}
.newsBottomContent{
    width: 1200px;
    margin: 0 auto;
    overflow: hidden;
    position: relative;
    z-index: 1;
}
.newsLeft{
    width: 627px;
    padding: 50px 0 0 35px;
    box-sizing: border-box;
    color: #fff;
    float: left;
}
.newsRight{
    width: 500px;
    height: 310px;
    border: 6px solid #fff;
    float: right;
    margin-top: 20px;
    background: #fff;
}
.newsRight img{
    width: 100%;
    height: 100%;
}

.newsLeft .newsLeftTitle{
    font-size: 30px;
    font-weight: bold;
    line-height: 40px;
}
.newsLeft .newsLeftDate{
    font-size: 14px;
    margin: 10px 0 20px 0;
    line-height: 19px;
}
.newsLeft .newsLeftDesc{
    font-size: 14px;
    line-height: 19px;
    height: 96px;
}
.newsLeft .newsBtn{
    margin-top: 57px;
    border: 1px solid #fff;
    color: #fff;
    line-height: 26px;
    width: 99px;
    text-align: center;
    border-radius: 2px;
    font-size: 12px;
    display: inline-block;
}
.newsList{
    width: 1200px;
    margin: 44px auto 0 auto;
    display: flex;
}

.newsList .newsItem {
    width: 390px;
    margin-right: 15px;
    background: #fff;
}
.newsList .newsItem:hover{
    box-shadow: 2px 2px 6px #669BCC33;
}
.newsList .newsItem:hover .img-box img{
    transform: scale(1.1);
}
.newsList .newListBody{
    background: #fff;
    padding: 15px;
    box-sizing: border-box;
    height: 215px;
}
.newsList .img-box{
    height: 214px;
    overflow: hidden;
}
.newsList .img-box img{
    width: 100%;
    height: 100%;
    transition: transform .3s ease;
}
.newsList .newstitle{
    font-size: 16px;
    font-weight: bold;
    line-height: 23px;
    height: 45px;
}
.newsList .newsdate{
    font-size: 12px;
    line-height: 16px;
    color: #A1A7B3;
    margin: 10px 0 18px 0;
}
.newsList .introduction{
    color: #45484D;
    font-size: 12px;
    line-height: 20px;
    height: 60px;
}
.newsList .newsBtn{
    text-align: right;
    color: #21c9b8;
    margin-top: 3px;
}
.newsList .newsBtn span{
    margin-right: 10px;
}
.newsList .newsItem:nth-child(3) {
    margin-right: 0;
}
.detailsnew{
    height: 84px;
    opacity: 0.8;
    background-color: rgba(255, 255, 255, 1);
    position: absolute;
    bottom: 0;
    width: 624px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
}
.timeDiv{
    width: 104px;
    text-align: center;
    color: rgba(51, 51, 51, 1);
}
.timeTop{
    font-size: 16px;
}
.timeBottom{
    font-size: 24px;
}
/* .content{
    width: 467px;
} */
.newstitle{
    color: rgba(51, 51, 51, 1);
    font-size: 18px;
}
.introduction{
    color: rgba(102, 102, 102, 1);
    font-size: 14px;
}
/*.rightDetails{*/
/*    height: 102px;*/
/*    display: flex;*/
/*    justify-content: flex-start;*/
/*    align-items: center;*/
/*}*/
/*.rightDetails:hover{*/
/*    background-color: rgba(255, 255, 255, 1);*/
/*}*/
/*.rightDetails:hover .timeTop{*/
/*    color: rgba(222, 120, 28, 1);*/
/*}*/
/*.rightDetails:hover .timeBottom{*/
/*    color: rgba(222, 120, 28, 1);*/
/*}*/
/*.rightDetails:hover .newstitle{*/
/*    color: rgba(222, 120, 28, 1);*/
/*}*/
.titleAll{
    color: #333333;
    font-size: 30px;
    text-align: center;
    width: 100%;
    float: left;
    font-weight: bold;
    line-height: 40px;
}
.titleIcon{
    display: block;
    width: 50px;
    height: 22px;
    margin: 0 auto;
}
.newsHeader{
    height: 120px;
    position: relative;
    padding: 30px 0;
    box-sizing: border-box;
}
.moreview{
    color: #4D4D4D;
    font-size: 16px;
    cursor: pointer;
    position: absolute;
    right: 0;
}
.moreview a{
    color: #4D4D4D;
}
.serveBox{
    width: 100%;
    background: white;
    height: 730px;
}
.serveContent{
    width: 1200px;
    margin: 0 auto;
}
.serveBottom{
    display: flex;
    justify-content: space-between;
}
.product{
    width: 100%;
    background: white;
    height: 664px;
}
.productContent{
    width: 1200px;
    margin: 0 auto;
}
.productBottom{
    display: flex;
    flex-wrap: wrap;
}
.arrange{
    width: 288px;
    border-radius: 2px;
    height: 211px;
    box-sizing: border-box;
    position: relative;
    cursor: pointer;
    transition: all 0.5s ease 0s;
    margin-right: 16px;
    margin-bottom: 15px;
    background: #ccc;
}
.arrange a{
    display: block;
    width: 100%;
    height: 100%;
}
.arrange:last-child,
.arrange:nth-child(4)
{
    margin-right: 0px;
}

.arrange:hover{
    width: 288px;
}
.dynamic{
    z-index: -1;
    height: 134px;
    opacity: 0;
    width: 100%;
    transition: all 0.5s ease 0s;
    position: absolute;
    top: 0px;
    left: 0px;
    overflow-y: hidden;
}
.dynamicbottom{
    z-index: -1;
    height: 100%;
    width: 100%;
    opacity: 0;
    transition: all 0.5s ease 0s;
    position: absolute;
    bottom: 0px;
    left: 0px;
    overflow-y: hidden;
    animation: frameout 0.2s linear;
    animation-fill-mode: forwards;
}
.arrange:hover .dynamicbottom{
    z-index: 111;
    height: 211px;
    opacity: 1;
    width: 288px;
    transition: all 0.5s ease 0s;
    position: absolute;
    bottom: 0px;
    left: 0px;
    overflow-y: hidden;
    animation: frame 0.2s linear;
    animation-fill-mode: forwards;
}
.arrange:hover .productContentTitle{
    display: none;
}
@keyframes frame{
    from{
        height: 0;
    }
    to{
        height: 211px;
    }
}
@keyframes frameout{
    from{
        height: 211px;
    }
    to{
        height: 0;
    }
}
/*需求大厅*/
.demandContent{
    width: 100%;
    background: #fff;
    padding-top: 20px;
}
.demandflex{
    width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content:space-between;
    flex-wrap: wrap;
}
.detailDemand{
    width: 590px;
    height:218px;
    background-color: rgba(255, 255, 255, 1);
    margin-bottom: 20px;
    padding: 20px;
    box-sizing: border-box;
}
.demandChunk{
    display: flex;
    justify-content: space-between;
}
.detailDemand a:hover .detailTitle{
    color: #21c9b8;
}
.detailTitle{
    height: 30px;
    color: rgba(51, 51, 51, 1);
    font-size: 18px;
    margin-bottom: 10px;
}
.detailDemand a{
    color: #333;
}
.bannerDemand{
    /* background-image: url("../images/new/demandbanner.jpg"); */
    width: 100%;
    height: 335px;
    text-align: center;
    color: rgba(255, 255, 255, 1);
    background-repeat: round;
}
.bannerSupply{
    /* background-image: url("../images/new/demandbanner.jpg"); */
    width: 100%;
    height: 335px;
    text-align: center;
    color: rgba(255, 255, 255, 1);
    background-repeat: round;
}
.demandTitle{
    font-size: 36px;
    padding-top: 125px;
    height: 52px;
    line-height: 52px;
}
.demandEng{
    font-size: 28px;
    line-height: 40px;
    font-weight: 300;
}
.icondiv{
    background-color: rgba(255, 255, 255, 1);
    width: 100%;
    height: 240px;
}
.demandQuery{
    width: 1200px;
    margin: 0 auto;
    right: 0;
    left: 0;
    height: 151px;
    position: absolute;
    top: 265px;
    /*opacity: 0.95;*/
    /*border: 2px solid rgba(255, 255, 255, 1);*/
    border-radius: 4px;
    /*box-shadow: 0px 0px 8px 0px rgba(218, 235, 253, 75);*/
    /* background: url(../images/new/xq_bg.png)no-repeat; */
    background-size: 100% 100%;
    /*background: linear-gradient(180deg, rgba(244,246,249,0.95) 0%,rgba(255,255,255,0.95) 100%);*/
    display: flex;
    flex-wrap: wrap;
    align-content: center;
    box-sizing: border-box;
}
.demandQuerydetail{
    width: 800px;
    margin: 0 auto;
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: space-between;
}
.demandQuerydetail .el-input-group{
    width: 677px;
    height: 50px;
}
.solutioninput .el-input-group{
    width: 800px !important;
}
.demandQuerydetail .el-input__inner{
    height: 50px;
    border: transparent;
}
.demandQuerydetail .el-input.is-active .el-input__inner, .el-input__inner:focus{
    border-color: #C0C4CC !important;
}
.publishBtn{
    width: 96px;
    height: 50px;
    line-height: 50px;
    border-radius: 4px;
    background: transparent linear-gradient(125deg, #21c9b8 0%, #5AAFFF 100%) 0% 0% no-repeat padding-box;
    color: #FFFFFF;
    font-size: 14px;
    text-align: center;
    cursor: pointer;
}
.hotQuery{
    width: 800px;
    margin: 0 auto;
    display: flex;
    flex-wrap: nowrap;
    margin-top: 10px;
}
.hotQueryFont{
    display: flex;
    justify-content: flex-start;
}
.hotTitle{
    color: #999;
    font-size: 12px;
    width: 70px;
    text-align: left;
}
.hotQueryFont div{
    /*width: 70px;*/
    margin-right: 12px;
    color: #4D4D4D;
    font-size: 12px;
    cursor: pointer;
}
.iconFlex{
    display: flex;
    position: absolute;
    bottom: 0;
    width: 1200px;
    right: 0;
    left: 0;
    margin: auto;
    justify-content: space-between;
}
.querySelect{
    position: absolute;
    width: 1200px;
    right: 0;
    left: 0;
    margin: auto;
    margin-top: 100px;
}
.imgBtn{
    width: 65px;
    height: 65px;
    line-height: 65px;
    border-radius: 10px;
    text-align: center;
    /*margin: 0 auto;*/
}
.imgBtnbg{
    /*background-color: #EAF2FE;*/
}
.imgBtnHoverbg{
    /*background-color: #21c9b8;*/
}
.icondivdetail{
    /*margin-right: 50px;*/
    width: 65px;
    text-align: center;
}
.pfont{
    width: 70px;
    height: 56px;
    line-height: 56px;
    text-align: center;
    color: rgba(102, 102, 102, 1);
    font-size: 14px;
    border-bottom: 2px solid white;
}
.pfonthover{
     width: 70px;
     border-bottom: 2px solid #21c9b8;
     height: 56px;
     line-height: 56px;
     text-align: center;
     color: #21c9b8;
     font-size: 14px;
 }
.demanddetailbg{
    background: #F7F8FA;
    padding: 115px 0 20px 0;
}
.detailcontent{
    width: 1200px;
    margin: 0 auto;
}
.demandDetaitop{
    background: white;
    width: 1200px;
    margin: 0 auto;
    height: 348px;
    margin-bottom: 20px;
    display: flex;
    flex-wrap: nowrap;
    justify-content: flex-start;;
}
.carouselDemand{
    width: 334px;
    height: 268px;
    margin: 40px;
}
.demandDetailbottom{
    background: white;
    width: 1200px;
    margin: 0 auto;
    min-height: 500px;
    padding: 20px;
    box-sizing: border-box;
}
.demandTopRight{
    height: 268px;
    margin: 40px;
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;
    justify-content: space-between;
}
.demandTopRighttitle{
    color: rgba(51, 51, 51, 1);
    font-size: 24px;
}
.demandTopRightflex{
    display: flex;
    line-height: 24px;
}
.detailrightTitle{
    color: rgba(153, 153, 153, 1);
    font-size: 14px;
    flex: none;
}
.detailrightContent{
    color: rgba(51, 51, 51, 1);
    font-size: 14px;
}
.intentionBtn{
    width: 110px;
    height: 40px;
    line-height: 40px;
    border-radius: 4px;
    background-color: #21c9b8;
    color: rgba(255, 255, 255, 1);
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    cursor: pointer;
}
.onlineBtn{
    width: 110px;
    height: 40px;
    line-height: 40px;
    border-radius: 4px;
    background-color: rgba(247, 154, 71, 0.2);
    color: rgba(247, 154, 71, 1);
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}
.demandDetailTitle{
    width: 100%;
    border-left: 3px solid rgba(247, 154, 71, 1);
    height: 21px;
    color: rgba(16, 16, 16, 1);
    font-size: 20px;
    line-height: 21px;
    padding-left: 10px;
}
.demandDetailContent{
    padding: 10px;
    color: rgba(102, 102, 102, 1);
    font-size: 16px;
}
.textOverflow{
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.textOverflow3{
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
}
.textOverflow1{
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
}
.demandQuerydetail .el-input-group__append, .el-input-group__prepend{
    background-color: #21c9b8;
    color: white;
    cursor: pointer;
}
.pagination{
    text-align: center;
    padding: 20px 0 40px;
}

.pagination .el-pagination.is-background .el-pager li:not(.disabled).active{
    color: #21c9b8;
    border:1px solid #21c9b8;
}
.pagination .el-pagination.is-background .el-pager li:not(.disabled):hover{
    color: #21c9b8;
    border:1px solid #21c9b8;
    margin: 0 5px;
    width: auto;
    height: 28px;
}
.pagination .el-pagination.is-background .btn-next, .el-pagination.is-background .btn-prev, .el-pagination.is-background .el-pager li{
    width: auto;
}
.pagination .more, .more:hover{
    display: inline-block;
}
.intentionBg{
    background-color: #F7F7F7;
    min-height: 1200px;
}
.intentionDemandcontent{
    width: 1200px;
    margin: 0 auto;
    right: 0;
    left: 0;
    min-height: 800px;
    position: absolute;
    top: 183px;
    background-color: rgba(255, 255, 255, 1);
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    padding: 40px 60px;
    box-sizing: border-box;
}
.intentionright{
    width: 734px;
}
.intentionLeft{
    width: 329px;
}
.intentTitle{
    color: rgba(51, 51, 51, 1);
    font-size: 20px;
    margin: 10px 0;
}
.flexBetween{
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.viewBtn{
    color: #21c9b8;
    font-size: 14px;
    cursor: pointer;
}
.timeIntention{
    color: rgba(153, 153, 153, 1);
    font-size: 14px;
}
.intentDescription{
    color: rgba(102, 102, 102, 1);
    font-size: 16px;
    margin: 10px 0;
}
.textOverflow4{
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 5;
    -webkit-box-orient: vertical;
}
.flexStart{
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-bottom: 20px;
}
.flexStart div:nth-child(1),
.flexDown div:nth-child(1)
{
    color: rgba(184, 184, 184, 1);
    font-size: 18px;
}
.flexStart div:nth-child(2){
    color: rgba(51, 51, 51, 1);
    font-size: 18px;
}
.flexDown{
    margin-bottom: 20px;
}
.subBtn{
    width: 100%;
    height: 46px;
    line-height: 46px;
    border-radius: 4px;
    background-color: #21c9b8;
    color: rgba(255, 255, 255, 1);
    font-size: 18px;
    text-align: center;
}
.hintBox{
    color: rgba(153, 153, 153, 1);
    font-size: 16px;
    text-align: left;
    margin: 40px 0;
}
.issueDemandcontent{
    width: 1200px;
    margin: 0 auto;
    right: 0;
    left: 0;
    /*min-height: 800px;*/
    /*position: absolute;*/
    /*top: 183px;*/
    background-color: rgba(255, 255, 255, 1);
    padding: 40px 60px;
    box-sizing: border-box;
    margin-top: -131px;
    /*margin-bottom: 20px;*/
}
.btnAll{
    width: 276px;
    border-radius: 28px;
    background-color: white;
    color: rgba(153, 153, 153, 1);
    box-shadow: 0px 0px 6px 0px rgba(205, 205, 205, 40);
    display: flex;
    height: 52px;
    align-items: center;
    margin: 0 auto;
}
.issueBtn{
    width: 148px;
    text-align: center;
    border-radius: 28px;
    background-color: white;
    height: 52px;
    line-height: 52px;
    color: rgba(153, 153, 153, 1);
    font-size: 18px;
    cursor: pointer;
}
.issueBtnActive{
    color: rgba(255, 255, 255, 1);
    background-color: #21c9b8;
    cursor: pointer;
}
.btnBox{
    width: 754px;
    float: right;
}
.flexBetweenintent{
    clear: both;
    display: flex;
    justify-content: space-between;
}
.intentionright .el-checkbox__inner{
    background-color: #EAF4FE;
    border: none;
}
.intentionright .el-checkbox__input.is-checked .el-checkbox__inner, .el-checkbox__input.is-indeterminate .el-checkbox__inner{
    background-color: #21c9b8;
    border: none;
}
.intentionright .el-checkbox__input.is-checked+.el-checkbox__label{
    color: #606266;
}
.keywordClass{
    width: 104px;
    height: 28px;
    line-height: 28px;
    border-radius: 2px;
    background-color: #21c9b8;
    color: rgba(255, 255, 255, 1);
    font-size: 12px;
    text-align: center;
}
.btnMargin{
    display: flex;
    margin-top: 20px;
    align-items: center;
}
.btnMargin span{
    color: rgba(153, 153, 153, 1);
    font-size: 14px;
}
.intentionright .el-upload--picture-card,
.intentionright .el-upload-list--picture-card .el-upload-list__item{
    width: 80px;
    height: 80px;
    /*line-height: 80px;*/
}
.intentionright .el-upload--picture-card{
    line-height: 80px;
}
.intentionright .el-upload-list__item.is-success .el-upload-list__item-status-label{
    display: none;
}
.intentionright .el-form--label-top .el-form-item__label{
    color: rgba(153, 153, 153, 1);
    font-size: 16px;
    line-height: 24px;
}
.draft{
    width: 154px;
    height: 44px;
    line-height: 44px;
    border-radius: 2px;
    background-color: rgba(255, 255, 255, 1);
    color: #21c9b8;
    font-size: 14px;
    text-align: center;
    border: 1px solid #21c9b8;
    font-weight: 500;
    cursor: pointer;
}
.push{
    width: 154px;
    height: 44px;
    line-height: 44px;
    border-radius: 2px;
    background-color: #21c9b8;
    color: rgba(255, 255, 255, 1);
    font-size: 14px;
    text-align: center;
    font-weight: 500;
    cursor: pointer;
}
.cancel{
    width: 154px;
    height: 44px;
    line-height: 44px;
    border-radius: 2px;
    background-color: rgba(255, 255, 255, 1);
    color: rgba(196, 196, 196, 1);
    font-size: 14px;
    text-align: center;
    border: 1px solid rgba(222, 222, 222, 1);
    font-weight: 500;
    cursor: pointer;
}
.issueBtnBox{
    display: flex;
    justify-content: space-around;
}
.uploadBtn{
    width: 115px;
    height: 36px;
    line-height: 36px;
    border-radius: 2px;
    background-color: #21c9b8;
    color: rgba(255, 255, 255, 1);
    font-size: 14px;
    text-align: center;
    cursor: pointer;
}
.querySelectLine{
    display: flex;
    justify-content: flex-start;
    height: 67px;
    line-height: 60px;
    border-bottom: 1px dashed rgba(236, 236, 236, 1);
    align-items: center;
}
.querySelectLinebtn{
    min-width: 70px;
    height: 30px;
    color: rgba(51, 51, 51, 1);
    font-size: 14px;
    text-align: center;
    cursor: pointer;
    line-height: 30px;
    margin: 0 10px;
}
.selectTrue{
    border-radius: 4px;
    background-color: #EAF2FE;
    color: #21c9b8!important;
    font-size: 14px;
    text-align: center;
    cursor: pointer;
}
.selectBtn{
    width: 1156px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
}
/*解决方案*/
.bannerSolution{
    /* background-image: url("../images/new/solutionBg.jpg"); */
    width: 100%;
    height: 540px;
    text-align: center;
    color: rgba(255, 255, 255, 1);
    background-repeat: round;
}
.solutionTitle{
    color: #fff;
    font-size: 48px;
    height: 66px;
}
.solutionEng{
    font-size: 20px;
    font-weight: 500;
    color: #fff
}
.bannerSolutionFlex{
    display: flex;
    width: 1200px;
    align-items: center;
    margin: 0 auto;
    text-align: left;
}
.solutionTop{
    top: 471px;
}
.iconFlexTitle{
    width: 110px;
    height: 45px;
    line-height: 26px;
    border-radius: 2px;
    color: rgba(51, 51, 51, 1);
    font-size: 18px;
    text-align: center;
    margin: 0 20px;
    cursor: pointer;
}
.solutioniconFlex{
    display: flex;
    position: absolute;
    bottom: 0;
    width: 1200px;
    right: 0;
    left: 0;
    margin: auto;
    justify-content: center;
}
.activeTitle{
    color: #21c9b8;
    border-bottom: 2px solid #21c9b8;
}
.leftsolution{
    width: 185px;
    height: 715px;
    line-height: 20px;
    opacity: 0.95;
    border-radius: 4px;
    background: linear-gradient(180deg, rgba(244,246,249,1) 0%,rgba(255,255,255,1) 100%);
    color: rgba(16, 16, 16, 1);
    font-size: 14px;
    box-shadow: 0px 0px 8px 0px rgba(218, 235, 253, 75);
    border: 2px solid rgba(255, 255, 255, 1);
    padding: 20px 0;
    box-sizing: border-box;
    overflow-y: auto;
}
.rightSolution{
    width: 1000px;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    align-content: flex-start;
}
.solutionBody{
    margin-top: 30px;
}
.solution-menu{
    width: 240px;
    height: 400px;
    overflow: hidden;
    overflow-y: auto;
    background: #EAF2FE;
    padding: 20px 0;
    box-sizing: border-box;
    border-radius: 2px;
    margin-right: 15px;
    float: left;
}
.solution-menu a{
    outline: none;
    display: block;
    box-sizing: border-box;
    padding: 20px 0;
    position: relative;
}

.solution-menu a span{
    border-left: 2px solid transparent;
    display: block;
    font-size: 16px;
    line-height: 20px;
    padding: 0 20px;
    box-sizing: border-box;
}
.solution-menu a.active{
    background: transparent linear-gradient(90deg, #FFFFFF 0%, #EAF5FF 100%) 0% 0% no-repeat padding-box;
    color: #21c9b8;
    font-weight: bold;
}
.solution-menu a.active .card-item-icon{
    display: block;
}
.solution-menu a.active span{
    border-left: 2px solid #21c9b8;
}
.solution-menu a:hover{
    font-weight: bold;
}
.solution-list{
    width: 945px;
}
.solutionContent{
    width: 490px;
    height: 124px;
    border: 2px solid transparent;
    padding: 20px;
    box-sizing: border-box;
    cursor: pointer;
}
.solutionContent:hover{
    opacity: 0.95;
    border-radius: 4px;
    background: linear-gradient(180deg, rgba(244,246,249,1) 0%,rgba(255,255,255,1) 100%);
    color: rgba(16, 16, 16, 1);
    font-size: 14px;
    box-shadow: 0px 0px 8px 0px rgba(218, 235, 253, 75);
    border: 2px solid rgba(255, 255, 255, 1);
}
.solutionContentTitle{
    color: rgba(51, 51, 51, 1);
    font-size: 18px;
    margin-bottom: 10px;
}
.solutionContent:hover .solutionContentTitle{
    color: #21c9b8;
}
.solutionContentValue{
    color: rgba(102, 102, 102, 1);
    font-size: 12px;
    line-height: 1.5;
}
.leftTitle{
    color: rgba(51, 51, 51, 1);
    font-size: 16px;
    margin: 30px 0;
    padding-left: 20px;
    border-left: 3px solid transparent;
    cursor: pointer;
}
.leftTitleHover{
    color: #21c9b8;
    border-left: 3px solid #21c9b8;
}
.soDetailBanner{
    width: 100%;
    height: 368px;
}
.schemeBox{
    width: 100%;
    background-color: white;
    height: 556px;
}
.sketchLeft{
    width: 526px;
    height: 316px;
    position: relative;
}
.sketchRight{
    height: 298px;
    width: 660px;
}
.sketchRight1{
    height: 298px;
    width: 860px;
    margin: 0 auto;
    text-align: center;
}
.sketchBg{
    position: absolute;
    right: 0;
    bottom: 0;
}
.sketch{
    position: absolute;
    top: -20px;
    left: -20px;
}
.painSpotBox{
    width: 100%;
    background-color: rgba(247, 248, 250, 1);
    /*height: 434px;*/
    padding-bottom: 30px;
}
.advantageBox{
    width: 100%;
    background-color: white;
    height: 685px;
}
.advantageLeft{
    width: 674px;
    padding: 40px;
    background-color: rgba(247, 248, 250, 1);
    box-sizing: border-box;
    height: 358px;
}
.advantageBox .el-carousel__container{
    height: 358px;
}
.circleBtn{
    width: 48px;
    height: 48px;
    border: 1px solid rgba(187, 187, 187, 1);
    border-radius: 50%;
    text-align: center;
    line-height: 48px;
    cursor: pointer;
}
.across{
    border-bottom: 2px solid rgba(217, 217, 217, 1);
}
.carouseBtn{
    display: flex;
    justify-content: space-between;
    margin: 20px 0;
    align-items: center;
}
.acrossBox{
    display: flex;
    flex-wrap: nowrap;
    justify-content: space-around;
    width: 1000px;
}
.acrossHover{
    border-bottom: 3px solid #21c9b8;
}
.advantageTitle{
    color: rgba(51, 51, 51, 1);
    font-size: 20px;
    font-weight: 500;
    margin-left: 20px;
}
.advantageTitleBox{
    display: flex;
    align-items: center;
}
.advsubtitle{
    font-size: 20px;
    margin: 20px 0;
}
.painContent{
    grid-template-columns:  repeat(4,291px);
    display: grid;
    justify-content: space-between;
    row-gap: 20px;
}
.painDiv{
    height: 254px;
    border-bottom: 2px transparent;
    box-sizing: border-box;
    padding: 25px;
}
.painDiv:hover{
    background: white;
    border-bottom: 2px solid #21c9b8;
}
.painDivTitle{
    margin: 20px 0;
    font-size: 20px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
}
.benefitBox{
    width: 100%;
    background-color: rgba(247, 248, 250, 1);
    height: 368px;
}
.benfitContent{
    display: flex;
    flex-wrap: nowrap;
    justify-content: space-between;
    text-align: center;
}
.benfitContentIndex{
    color: #21c9b8;
    font-size: 24px;
}
.orangeDiv{
    width: 16px;
    height: 16px;
    background-color: #21c9b8;
    border-radius: 50%;
    margin: 0 auto;
}
.dashesDiv{
    width: calc((100% - 16px) / 2);
    border-bottom: 1px dashed #21c9b8;
    position: absolute;
    right: 0;
}
.cutDiv{
    position: relative;
    display: flex;
    align-items: center;
}
.dashesLeftDiv{
    width: calc((100% - 16px) / 2);
    border-bottom: 1px dashed #21c9b8;
    position: absolute;
    left: 0;
}
.benfitContentTitle{
    font-size: 22px;
    margin: 20px 0;
    display: flex;
    align-items: center;
    justify-content: center;
}
.benfitContentfont{
    width: 90%;
    margin: 20px auto;
}
.caseBoxdetail{
    width: 100%;
    background-color: white;
    margin-bottom: 40px;
}
.casecontent{
    width: 100%;
    background-image: url("../images/caseBg.png");
    height: 512px;
    background-repeat: round;
    padding: 67px 0;
    box-sizing: border-box;
}
.caseLeft{
    width: 275px;
    height: 379px;
    background: linear-gradient(180deg, rgba(0,0,0,0) 0%,rgba(0,0,0,0.75) 53%,rgba(0,0,0,0) 100%);
    padding: 51px 0 10px 0px;
    box-sizing: border-box;
}
.caseLeftbtn{
    color: rgba(255, 255, 255, 1);
    height: 55px;
    line-height: 55px;
    cursor: pointer;
    border-left: 3px solid transparent;
}
.caseLeftbtnhover{
    background: linear-gradient(90.29deg, rgba(66,138,250,0.7) 0.25%,rgba(247,154,71,0) 99.76%);
    color: rgba(255, 255, 255, 1);
    border-left: 3px solid #21c9b8;
}
.caseRight{
    width: 910px;
    border-bottom: 1px solid rgba(100, 100, 100, 1);
}
.caseRightTitle{
    color: rgba(255, 255, 255, 1);
    font-size: 20px;
    margin: 20px 0;
}
.caseRightContent{
    color: rgba(255, 255, 255, 0.6);
    font-size: 16px;
}
.bannerScience{
    width: 100%;
    height: 414px;
    /* background-image: url("../images/scienceBg.jpg"); */
    background-repeat: round;
}
.customizationBox{
    width: 100%;
    height: 480px;
    background-color: rgba(247, 248, 250, 1);
}
.customizationContentdiv{
    width: 400px;
    height: 193px;
    line-height: 20px;
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 1);
    text-align: center;
    padding: 30px 0;
    box-sizing: border-box;
    position: relative;
}
.customizationContentdiv:after{
    content: "";
    position: absolute;
    top: 20px;
    right: 0;
    width: 1px;
    height: 153px;
    background: #F2F2F2;
    border-radius: 0 0 2px 2px;
}
.customizationContentdivTitle{
    font-size: 18px;
    margin: 10px 0;
    line-height: 24px;
}
.customizationContentdivContent:after {
    position: absolute;
    right: 0;
    top: 3px;
    content: "";
    width: 1px;
    height: 12px;
    background: #F2F2F2;
}
.customizationContentdivContent:last-child:after{
    width: 0;
}
.customizationContentdivContent{
    padding: 0 10px;
    box-sizing: border-box;
    position: relative;
    color: #999999;
    line-height: 19px;
    display: inline-block;
    font-size: 14px;
}
.customizationContentdivhover{
    height: 233px;
    background: transparent linear-gradient(130deg, #21c9b8 0%, #5AAFFF 100%) 0% 0% no-repeat padding-box;;
    color: rgba(255, 255, 255, 1);
    box-shadow: 0px 3px 6px #669BCC33;
    padding-top: 22px;
}
.customizationContentdivhover:after{
    width: 0;
}
.customizationContentdiv:last-child:after{
    width: 0;
}
.customizationContentdivhover .customizationContentdivContent{
    color: #D8E7FF;
}
.viewDetail{
    width: 93px;
    height: 26px;
    line-height: 26px;
    border-radius: 2px;
    font-size: 12px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 1);
    margin: 20px auto 0 auto;
    display: none;
}
.customizationContentdivhover .viewDetail{
    display: block;
}
.productDifferencesBox{
    width: 100%;
    background: white;
    height: 704px;
}
.productDifferencesbg{
    /* background-image: url("../images/new/productDifferencesbg.png"); */
    /* background-image: url("../images/new/productDifferencesbgnew.png"); */
    /* background-image: url("../images/new/productDifferencesbgnew1.png"); */
    /* background-image: url("../images/new/productDifferencesbgnew2.png"); */
    height: 554px;
    background-repeat: round;
    margin-top: -140px;
}
.logoBtnbox{
    width: 400px;
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: space-between;
    margin: 0 auto;
}
.logobtn{
    width: 90px;
    height: 75px;
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 1);
    text-align: center;
    box-shadow: 0px 0px 10px 0px rgba(188, 188, 188, 40);
    padding: 5px;
    box-sizing: border-box;
    cursor: pointer;
}
.logobtnImg{
    height: 40px;
    line-height: 40px;
}
.productContentfont{
    height: 450px;
    padding: 50px 0;
    box-sizing: border-box;
    width: 50%;
}
.productContentfontTitle{
    color: rgba(16, 16, 16, 1);
    font-size: 22px;
    margin-bottom: 20px;
}
.productContentfontContent{
    line-height: 35px;
}
.resourceBox{
    width: 100%;
    background-color: rgba(247, 248, 250, 1);
}
.resourceContent{
    margin-bottom: 30px;
}
.resourceContentheight .el-tabs__content{
    height: 210px;
}
.resourceContent .el-tabs__item.is-active,
.resourceContent .el-tabs__item,
.resourceContent .el-tabs__item:hover
{
    color: rgba(51, 51, 51, 1);
    font-weight: 400;
}
.resourceContentsj .el-tabs__item{
    width: 90px;
    text-align: center;
    padding: 0;
}
.resourceContent .el-tabs__active-bar{
    background-color: #21c9b8;
}
.resourceContentsj .el-tabs__active-bar{
    width: 90px;
    bottom: 6px;
    transition:none;
}
.resourceContentsj .el-tabs__nav{
    height: 60px;
}
.resourceContentsj .el-tabs__nav-wrap::after{
    bottom: 6px;
}
.resourceContentsj .el-tabs__item.is-active::after {
    content: "";
    position: absolute;
    bottom: -20px;
    left: calc(50% - 13px);
    border-left: 13px solid transparent;
    border-right: 13px solid transparent;
    border-bottom: 20px solid #21c9b8;
}
.serveTeBox{
    width: 100%;
    background-color: white;
}
.bannerSolutionBox{
    width: 100%;
}
.serviceContentLeft{
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    width: 40%;
    line-height: 50px;
    font-size: 18px;
}
.serviceContentRight{
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    width: 40%;
    line-height: 50px;
    font-size: 18px;
    color: white;
}
.flowContent{
    display: flex;
    flex-wrap: wrap;
}
.flowBg{
    /* background-image: url("../images/new/flowBg.png"); */
    width: 231px;
    height:220px;
}
.rightfa{
    width: 100px;
    height:220px;
    text-align: center;
    line-height: 220px;
}
.flowBgIndex{
    height: 63px;
    line-height: 63px;
    text-align: center;
    color: #21c9b8;
    font-size: 28px;
}
.flowBgItem{
    height: 60px;
    text-align: center;
    line-height: 60px;
}
.costBg{
    width: 291px;
    height: 206px;
    background-color: rgba(246, 248, 250, 1);
    color: rgba(16, 16, 16, 1);
    font-size: 14px;
    box-shadow: 0px 2px 6px 0px rgba(222, 222, 222, 40);
    padding: 40px;
    box-sizing: border-box;
    cursor: pointer;
}
.costBgItem{
    font-size: 18px;
    margin: 20px 0;
}
.costBg:hover{
    border-bottom: 4px solid #21c9b8;
    background-color: rgba(255, 255, 255, 1);
}
.costBox{
    width: 100%;
    background-color: rgba(247, 248, 250, 1);
    padding-bottom: 80px;
}
.serviceContentBg{
    /* background-image: url("../images/new/serviceContentbg.png"); */
    background-repeat: no-repeat;
    height: 300px;
    display: flex;
    justify-content: space-between;
    padding: 25px;
    box-sizing: border-box;
}
.servicelefticonDiv{
    width: 36px;
    height: 36px;
    line-height: 36px;
    background-color: #21c9b8;
    border-radius: 50%;
    text-align: center;
    margin-right: 20px;
}
.servicerighticonDiv{
    width: 36px;
    height: 36px;
    line-height: 36px;
    background-color: white;
    border-radius: 50%;
    text-align: center;
    margin-left: 20px;
}
.serviceContentLeftcontent {
    display: flex;
    align-items: center;
    border-bottom: 1px solid rgba(232, 232, 232, 1);
}
.serviceContentLeftcontent:last-child{
    border-bottom:none;
}
.serviceContentLeftbtn{
    width: 136px;
    height: 48px;
    line-height: 48px;
    border-radius: 4px;
    background-color: transparent;
    text-align: center;
    border: 1px solid #21c9b8;
    color: #21c9b8;
    font-size: 20px;
}
.serviceContentRightbtn{
    width: 146px;
    height: 48px;
    line-height: 48px;
    border-radius: 4px;
    background-color: #21c9b8;
    text-align: center;
    color: rgba(255, 255, 255, 1);
    font-size: 20px;
    margin-left: 573px;
}
.caseBox{
    width: 100%;
    background-color: rgba(247, 248, 250, 1);
}
.caseContent{
    /* background-image: url("../images/new/caseBg.png"); */
    height: 522px;
    background-repeat: round;
}
.caseContent .el-tabs__item.is-active,
.caseContent .el-tabs__item:hover{
    color: #21c9b8;
}
.caseContent .el-tabs__item{
    font-size: 18px;
    color: white;
    margin-bottom: 15px;
}
.caseTitle{
    text-align: center;
    color: rgba(255, 255, 255, 1);
    font-size: 24px;
    padding: 50px 0;
}
.detail_btn{
    width: 76px;
    height: 27px;
    line-height: 27px;
    border-radius: 2px;
    background-color: #21c9b8;
    text-align: center;
    color: white;
    cursor: pointer;
}
.demandTediv{
    width: 590px;
    height: 180px;
    line-height: 20px;
    opacity: 0.95;
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 1);
    color: rgba(16, 16, 16, 1);
    font-size: 14px;
    display: flex;
    justify-content: space-between;
    padding: 20px;
    box-sizing: border-box;
    margin-bottom: 20px;
    border: 2px solid transparent;
    align-items: center;
}
.demandTediv:hover{
    background: linear-gradient(180deg, rgba(244,246,249,1) 0%,rgba(255,255,255,1) 100%);
    box-shadow: 0px 0px 8px 0px rgba(218, 235, 253, 75);
    border: 2px solid rgba(255, 255, 255, 1);
}
.demandTediv:hover .demandTedivTitle{
    color: #21c9b8;
}
.demandTedivTitle{
    font-size: 18px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
}
.demandTedivTime{
    color: #21c9b8;
    font-size: 12px;
    margin: 10px 0;
}
.demandTedivDe{
    color: rgba(153, 153, 153, 1);
    font-size: 12px;
    height: 40px;
    margin-bottom: 10px;
}
.carouselDiv{
    width: 100%;
    height: 518px;
    box-sizing: border-box;
    position: relative;
}
.carouselDiv img{
    height: 100%;
    display: block;
}
.bannerContent{
    position: absolute;
    width: 1160px;
    top: 0;
    bottom: 100px;
    right: 0;
    left: 0;
    margin: auto;
    display: flex;
    flex-direction: column;
    justify-content: center;
}
.bannerBottom{
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 100px;
    background-color: rgba(251, 251, 251, 0.5);
    z-index: 9;
}
.bannerBottomcontent{
    margin: 0 40px;
    display: flex;
    justify-content: space-between;
    height: 100%;
}
.bannerBottomdiv{
    width: 25%;
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    cursor: pointer;
}
.bannerBottomdiv:hover{
    opacity: 0.83;
    background-color: rgba(255, 255, 255, 1);
}
.bannerBottomdiv:hover .bannerBottomdivTitle{
    color: #21c9b8;
}
.bannerBottomdivTitle{
    font-size: 18px;
}
.bannerBottomdivContent{
    color: rgba(153, 153, 153, 1);
    font-size: 14px;
}
.productContentTitle{
    color: rgba(16, 16, 16, 1);
    font-size: 14px;
    padding: 10px;
    position: absolute;
    box-sizing: border-box;
    bottom: 0;
    left: 0;
    width: 100%;
    color: #fff;
    background: rgba(0, 80, 204, 0.8);
}
.productContentTitle:after{
    content: "";
    width: 40px;
    height: 1px;
    display: block;
    background: #fff;
}
.productContentContent{
    color: rgba(102, 102, 102, 1);
    font-size: 14px;
}
.productContentTitlehover{
    color: rgba(255, 255, 255, 1);
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 5px;
    position: relative;
    line-height: 32px;
}
.productContentTitlehover:after{
    content: "";
    width: 40px;
    height: 1px;
    display: block;
    background: #fff;
}
.productDetailhover{
    color: #fff;
    font-size: 12px;
    position: absolute;
    bottom: 0;
    left: 20px;
    padding: 10px 0;
    box-sizing: border-box;
}
.productContentContenthover{
    color: #CCE6FF;
    font-size: 12px;
}
.productContenthoverBottom{
    height: 211px;
    padding: 10px 20px 0;
    box-sizing: border-box;
    background: rgba(15, 69, 153, 0.8);
}
.solutionBox{
    width: 100%;
    height: 631px;
}

.solveListDiv{
    width: 296px;
    position: relative;
    margin-bottom: 16px;
}

.solveDiv{
    height: 192px;
    border-radius: 2px;
    background: #F9FBFE;
    position: relative;
    font-size: 14px;
    padding: 20px 15px;
    box-sizing: border-box;
}
.solveListDiv:hover{
    box-shadow: 0px 0px 6px #669BCC33;
}
.solveListDiv:hover:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: #21c9b8;
    border-radius: 0 0 2px 2px;
}
.solveFlex{
    grid-template-columns:  repeat(3,305px);
    display: grid;
    justify-content: space-between;
}

.solveTop {
    display: flex;
    align-items: center;
}
.solveIcon{
    float: left;
    width: 27px;
    margin-right: 10px;
}
.solveTitle{
    font-size: 16px;
    font-weight: bold;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    width: calc(100% - 37px);
    display: inline-block;
    color: #4D4D4D;
}
.solveContent{
    color: #8A8F99;
    font-size: 12px;
    line-height: 18px;
    margin: 15px 0 0 0;
}
.solveBtn{
    width: 68px;
    height: 26px;
    text-align: center;
    border: 1px solid #21c9b8;
    color: #21c9b8;
    line-height: 24px;
    position: absolute;
    bottom: 20px;
    border-radius: 2px;
    box-sizing: border-box;
    font-size: 12px;
}
.solverightimg{
    position: absolute;
    right: 16px;
    top: 38px;
}
.solveListDiv:hover .solverightimg{
    right: 0px;
}
.arrows{
    position: absolute;
    right: 12px;
    top: 50px;
    color: white;
    display: none;
    cursor: pointer;
}
.solveListDiv:hover .arrows{
    display: block;
}
.supplyAnddemandbox{
    width: 100%;
    padding-bottom: 35px;
    padding-top: 18px;
    box-sizing: border-box;
    height: 668px;
}
.tabs-list{
    padding: 0 30px;
    box-sizing: border-box;
    background: #fff;
    margin-bottom: 15px;
    position: relative;
}
.tabs-list div{
    display: inline-block;
}
.tabs-list a{
    color: #4D4D4D;
    font-size: 16px;
    line-height: 21px;
    padding: 16px 0 13px 0;
    box-sizing: border-box;
    display: inline-block;
    border-bottom: 2px solid transparent;
}
.tabs-list .lint-text.active{
    font-weight: bold;
    color: #21c9b8;
    border-bottom: 2px solid #21c9b8;
}
.tabs-list .el-divider{
    margin: -2px 30px 0 30px;
}
.tabs-list .moreview{
    top: 0;
    right: 20px;
}

.tabs-list-border{
    border-bottom: 1px solid #E2E3E5;
    box-sizing: border-box;
    padding-right: 0;
}
.tabs-list-border a{
    padding-top: 0;
    padding-bottom: 10px;
}
.tabs-list-border .moreview{
    right: 5px;
}
.tabs-list-border .moreview a{
    font-size: 14px;
}
.supplyAnddemandList{
    overflow: hidden;
}
.supplyAnddemandList a{
    width: 288px;
    display: block;
    outline: none;
    background: #fff;
    margin-right: 16px;
    margin-bottom: 15px;
    padding: 30px 22px 0 30px;
    box-sizing: border-box;
    color: #4D4D4D;
    border-radius: 2px;
    height: 198px;
    position: relative;
    float: left;
}
.supplyAnddemandList .supply-bttom{
    position: absolute;
    width: 100%;
    bottom: 27px;
    left: 0;
    padding: 0 30px;
    box-sizing: border-box;
}
.supplyAnddemandList a:hover{
    box-shadow: 2px 2px 6px #669BCC33;
}
.supplyAnddemandList a:nth-child(4),.supplyAnddemandList a:nth-child(8){
    margin-right: 0;
}
.supplyAnddemandList .s-title{
    font-weight: bold;
    font-size: 18px;
    line-height: 24px;
    margin-bottom: 10px;
}
.supplyAnddemandList .s-subtitle{
    font-size: 14px;
    line-height: 20px;
    margin-bottom: 20px;
    color: #999999;
}
.supplyAnddemandList .tag{
    background: #EAF2FE;
    color: #21c9b8;
    padding: 0 10px;
    font-size: 12px;
    line-height: 20px;
    display: inline-block;
}
.supplyAnddemandList .img-arrow{
    float: right;
    display: block;
    width: 24px;
    height: 24px;
    /* background: url("../images/icon/arrow.svg")no-repeat; */
}
.supplyAnddemandList a:hover .img-arrow{
    /* background: url("../images/icon/arrow_c.svg")no-repeat; */
}
.listAllFlex{
    display: flex;
    justify-content: space-between;
    /*align-items: center;*/
    margin-bottom: 20px;
}
.listAll{
    width: 593px;
}
.pushBtn{
    width: 104px;
    height: 36px;
    border-radius: 2px;
    background-color: rgba(255, 255, 255, 1);
    text-align: center;
    border: 1px solid rgba(222, 121, 28, 1);
    color: rgba(222, 121, 28, 1);
    line-height: 36px;
}
.searchTitle{
    color: rgba(16, 16, 16, 1);
    font-size: 22px;
}
.searchTitle:hover{
    color: rgba(222, 121, 28, 1);
}
.listdiv{
    width: 593px;
    height: 112px;
    line-height: 20px;
    background-color: rgba(247, 248, 250, 1);
    color: rgba(16, 16, 16, 1);
    font-size: 14px;
    margin-bottom: 20px;
    position: relative;
}
.listdiv:hover .listdivtitle{
    color: rgba(222, 121, 28, 1);
}
.listdivContent{
    padding: 20px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
}
.listdivtitle{
    color: rgba(51, 51, 51, 1);
    font-size: 18px;
}
.orangesign{
    width: 2px;
    height: 20px;
    background: rgba(222, 121, 28, 1);
    position: absolute;
    top: 20px;
    display: none;
}
.listdiv:hover .orangesign{
    display: block;
}
.projectBox{
    width: 100%;
    height: 560px;
}
.projectBody{
    margin-top: 25px;
}

.projectBody .el-carousel__indicators{
    display: none;
}
.projectBody .carousel-item{
    padding: 40px;
    box-sizing: border-box;
    overflow: hidden;
    width: 290px;
}
.projectBody .carousel-item:hover .carousel-item-imgbox img{
    transform: scale(1.1);
}
.projectBody .carousel-item .carousel-item-imgbox img{
    transition: transform 0.3s ease;
}
.projectBody .carousel-item .carousel-item-imgbox{
    width: 354px;
    height: 221px;
    float: left;
    justify-content: center;
    align-content: center;
    display: none;
    overflow: hidden;
}
.projectBody .carousel-item .carousel-item-imgbox img{
    width: 100%;
    height: 100%;
}
.projectBody .el-carousel__item{
    background: #fff;
}
.projectBtn{
    border: 1px solid #fff;
    border-radius: 2px;
    padding: 0 10px;
    line-height: 24px;
    color: #fff;
    margin-top: 36px;
    font-size: 12px;
    display: none;
}
.projectBtn:hover{
    color: #fff;
}
.rectangle{
    margin: 20px 0;
    overflow: hidden;
}
.rectangle span{
    display: inline-block;
    width: 6px;
    height: 6px;
    background: #FFD800;
    margin-right: 5px;
    float: left;
}
.projectBody .carousel-item-right{
    padding-left: 0;
    box-sizing: border-box;
}
.projectBody .carousel-item .carousel-item-img {
    max-width: 100%;
    max-height: 100%;
}
.projectBody .carousel-item .p-title{
    font-size: 16px;
    font-weight: bold;
    color: #4D4D4D;
    line-height: 21px;
}
.projectBody .carousel-item .p-desc{
    font-size: 12px;
    color: #8A8F99;
    line-height: 21px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 5;
    -webkit-box-orient: vertical;
    min-height: 130px;
}
.projectBody .el-carousel__item.is-active {
    background: #21c9b8;
    width: 720px;
    margin-left: -60px;
    color: #fff;
}

.projectBody .el-carousel__item.is-active + .el-carousel__item .carousel-item{
    float: right;
}
/*.projectBody .el-carousel__container{*/
/*    background: yellow;*/
/*}*/
/*!*.projectBody .el-carousel__container .el-carousel__item:nth-child(4){*!*/
/*!*    background: green!important;*!*/
/*!*}*!*/
/*.projectBody .el-carousel__container:has(.is-active)>.el-carousel__item:nth-child(4){*/
/*    background: red;*/
/*}*/
/*!*.projectBody .el-carousel__container:has(.is-active)>.el-carousel__item:nth-child(3){*!*/
/*!*    background: green;*!*/
/*!*}*!*/
.projectBody .el-carousel__item.is-active .carousel-item{
    padding: 30px;
    width: 100%;
    height: 100%;
}
.projectBody .el-carousel__item.is-active .carousel-item .p-title {
    font-size: 20px;
    line-height: 26px;
    color: #fff;
}
.projectBody .el-carousel__item.is-active .carousel-item .p-desc {
    -webkit-line-clamp: 6;
    color: #fff;
}
.projectBody .el-carousel__item.is-active .carousel-item-imgbox{
    display: flex;
}
.projectBody .el-carousel__item.is-active .carousel-item-right {
    padding-left: 375px;
}
.projectBody .el-carousel__item.is-active .projectBtn{
    display: inline-block;
}
.selectcolor{
    color: rgba(255, 255, 255, 1);
}
.effectDiv{
    max-width: 500px;
    border-radius: 2px;
    border: 1px solid rgba(255, 255, 255, 0.6);
    padding: 10px;
}
.serveBottomFlex{
    display: flex;
}
.mapRb{
    position: absolute;
    right: 30px;
    bottom: 0;
    width: 100px;
}
.num{
    color: rgba(222, 120, 28, 1);
    font-size: 52px;
}
.numTitle{
    font-size: 20px;
}
.numflex{
    display: flex;
    margin: 50px 0;
}
.serviceCarousel{
    text-align: center;
    width: 368px;
    height: 150px;
    line-height: 30px;
    border-radius: 4px;
    background: linear-gradient(180deg, rgba(244,246,249,1) 0%,rgba(255,255,255,1) 100%);
    box-shadow: 0px 0px 8px 0px rgba(218, 235, 253, 75);
    border: 2px solid rgba(255, 255, 255, 1);
}
.serveBottomFlexright{
    display: flex;
    flex-direction: column;
    justify-content: space-around;
}
.timestampTitle{
    position: absolute;
    left: -120px;
    top: -5px;
    cursor: pointer;
    text-align: end;
    width: 100px;
}
.productdetailContent{
    display: flex;
    justify-content: space-between;
    /*align-items: center;*/
}
.productdetailContent .el-timeline-item__tail{
    border-left: 2px dashed #E4E7ED;
}
.productdetailContent .el-timeline-item{
    padding-bottom: 60px;
}
.productdetailContent .el-timeline{
    margin-left: 100px;
}
.activityContent{
    display: flex;
    justify-content: space-between;
    width: 100%;
}
.activityContentleft{
    width: 400px;
    margin: 0 20px;
}
.activeImgDiv{
    width: 615px;
    height: 373px;
    border-radius: 4px;
    box-shadow: 0px 0px 20px 0px rgba(255, 242, 230, 100);
}
.productListnowitemTitle{
    color: rgba(51, 51, 51, 1);
    font-size: 22px;
    font-weight: 500;
    margin-bottom: 20px;
}
.productBox{
    background-color: white;
    height: 553px;
}
.selectBottom{
    height: 40px;
    padding: 10px;
    box-sizing: border-box;
    cursor: pointer;
}
.cancelBtn{
    cursor: pointer;
}
.searchBoxApp .el-input-group__append, .el-input-group__prepend{
    background-color: #21c9b8;
    color: white;
    cursor: pointer;
}
.apptag .el-tag{
    background-color: #21c9b8;
    border-color: #21c9b8;
    color: white;
    margin: 0 10px;
}
.apptag .el-tag .el-tag__close{
    color: white;
}
.apptag .el-tag .el-tag__close:hover{
    background-color: transparent;
}
.customClass {
    background-color: #21c9b8 !important;
    border-color: #21c9b8 !important;
}
.cancelButtonClass:focus, .cancelButtonClass:hover{
    color: #21c9b8 !important;
    border-color: #21c9b8 !important;
    background-color: white !important;
}
.bannerDevelop{
    width: 100%;
    height: 400px;
    /* background-image: url('../images/bannerDevelop.png'); */
    background-repeat: no-repeat;
    background-position: center center;
}
.aboutMengdou{
    width: 100%;
    height: 90px;
    text-align: center;
    font-size: 24px;
    padding-top: 60px;
    box-sizing: border-box;
    margin-top: 10px;
}
.bannerMengdou{
    width: 100%;
    height: 400px;
    /* background-image: url('../images/bannerMengdou.png'); */
    background-repeat: no-repeat;
    background-position: center center;
    margin-bottom: 100px;
}
.bannerDevelop81{
    width: 100%;
    height: 400px;
    /* background-image: url('../images/bannerDevelop81.png'); */
    background-repeat: no-repeat;
    box-sizing: border-box;
    background-position: center center;
}
.bannerMengdou81{
    width: 100%;
    height: 470px;
    /* background-image: url('../images/bannerMengdou81.png'); */
    background-repeat: no-repeat;
    background-position: center center;
    margin-top: 40px;
    box-sizing: border-box;
    padding-top: 40px;
}
.mendouDate{
    width: 500px;
    height: 470px;
    margin-left: 52%;
}
.mendouBusiness{
    width: 100%;
    height: 90px;
    text-align: center;
    font-size: 24px;
    padding-top: 60px;
    box-sizing: border-box;
    margin-top: 10px;
    margin-bottom: 50px;
}
ul.bankuai {
    width: 60%;
    display: flex;
    flex-flow: row wrap;
    justify-content: space-between;
    padding-top: 30px;
    padding-bottom: 30px;
    margin-left: 20%;
}

ul.bankuai li {
    width: 18%;
    box-shadow: 2px 3px 5px #eee;
    border-radius: 5px;
    padding: 2%;
}

ul.bankuai li strong {
    font-size: 16px;
}

ul.bankuai li:hover {
    box-shadow: 2px 3px 5px #f8dabc;
}

ul.bankuai li:hover a {
    color: #de791b;
}
.business-img{
    width: 30px;
    height: 30px;
    margin-bottom: 15px;
}
.jiantou{
    width: 30px;
    height: 13px;
    /* background-image: url('../images/jiantou0.png'); */
    background-repeat: no-repeat;
    background-position: center center;
    background-size: 100% 100%;
    margin-top: 12px;
}
.jiantou:hover{
    /* background-image: url('../images/jiantou1.png'); */
}
.cooperate{
    width: 1153px;
    height: 691px;
    /* background-image: url('../images/cooperate.png'); */
    background-repeat: no-repeat;
    background-position: center center;
    margin: 0 auto;
    margin-bottom: 90px;
}
.qufnelunbo .el-carousel__button{
    width: 15px!important;
    height: 8px!important;
    margin-right: 16px !important;
}
.qufnelunbo .is-active .el-carousel__button{
    width: 30px!important;
   background-color: #FB8051;
   border-radius: 8px;
}
.qufnelunbo .el-carousel__indicators--horizontal{
    bottom: 12%!important;
}
/* 第一个球 */
.rongyuzizhi0{
    width: 140px;
    height: 140px;
    /* background-image: url('../images/rongyuzizhilogo01.png'); */
    background-repeat: no-repeat;
    background-size: 100% 100%;
    position: absolute;
    left: 98px;
    top: -950px;
    z-index: 10000;
}
.rongyuzizhih50{
    width: 140px;
    height: 60px;
    text-align: center;
    position: absolute;
    left: 98px;
    top: -810px;
    z-index: 10000;
    font-size: 18px;
}
.rongyuzizhi0:hover{
    /* background-image: url('../images/rongyuzizhilogo0.png'); */
}
.rongyuzizhi00{
    width: 140px;
    height: 140px;
    /* background-image: url('../images/rongyuzizhilogo0.png'); */
    background-repeat: no-repeat;
    background-size: 100% 100%;
    position: absolute;
    left: 98px;
    top: -950px;
    z-index: 10000;
}
/* 第二个球 */
.rongyuzizhi1{
    width: 140px;
    height: 140px;
    /* background-image: url('../images/rongyuzizhilogo11.png'); */
    background-repeat: no-repeat;
    background-size: 100% 100%;
    position: absolute;
    left: -35px;
    top: -635px;
    z-index: 10000;
}
.rongyuzizhih51{
    width: 140px;
    height: 60px;
    text-align: center;
    position: absolute;
    left: -35px;
    top: -490px;
    z-index: 10000;
    font-size: 18px;
}
.rongyuzizhi1:hover{
    /* background-image: url('../images/rongyuzizhilogo1.png'); */
}
.rongyuzizhi10{
    width: 140px;
    height: 140px;
    /* background-image: url('../images/rongyuzizhilogo1.png'); */
    background-repeat: no-repeat;
    background-size: 100% 100%;
    position: absolute;
    left: -35px;
    top: -635px;
    z-index: 10000;
}
/* 第三个球 */
.rongyuzizhi2{
    width: 140px;
    height: 140px;
    /* background-image: url('../images/rongyuzizhilogo21.png'); */
    background-repeat: no-repeat;
    background-size: 100% 100%;
    position: absolute;
    left: 98px;
    top: -282px;
    z-index: 10000;
}
.rongyuzizhih52{
    width: 140px;
    height: 60px;
    text-align: center;
    position: absolute;
    left: 98px;
    top: -135px;
    z-index: 10000;
    font-size: 18px;
}
.rongyuzizhi2:hover{
    /* background-image: url('../images/rongyuzizhilogo2.png'); */
}
.rongyuzizhi20{
    width: 140px;
    height: 140px;
    /* background-image: url('../images/rongyuzizhilogo2.png'); */
    background-repeat: no-repeat;
    background-size: 100% 100%;
    position: absolute;
    left: 98px;
    top: -282px;
    z-index: 10000;
}
/* 第四个球 */
.rongyuzizhi3{
    width: 140px;
    height: 140px;
    /* background-image: url('../images/rongyuzizhilogo31.png'); */
    background-repeat: no-repeat;
    background-size: 100% 100%;
    position: absolute;
    left: 740px;
    top: -950px;
    z-index: 10000;
}
.rongyuzizhih53{
    width: 160px;
    height: 60px;
    text-align: center;
    position: absolute;
    left: 730px;
    top: -810px;
    z-index: 10000;
    font-size: 18px;
}
.rongyuzizhi3:hover{
    /* background-image: url('../images/rongyuzizhilogo3.png'); */
}
.rongyuzizhi30{
    width: 140px;
    height: 140px;
    /* background-image: url('../images/rongyuzizhilogo3.png'); */
    background-repeat: no-repeat;
    background-size: 100% 100%;
    position: absolute;
    left: 740px;
    top: -950px;
    z-index: 10000;
}
/* 第五个球 */
.rongyuzizhi4{
    width: 140px;
    height: 140px;
    /* background-image: url('../images/rongyuzizhilogo41.png'); */
    background-repeat: no-repeat;
    background-size: 100% 100%;
    position: absolute;
    right: -35px;
    top: -635px;
    z-index: 10000;
}
.rongyuzizhih54{
    width: 200px;
    height: 60px;
    text-align: center;
    position: absolute;
    right: -65px;
    top: -490px;
    z-index: 10000;
    font-size: 18px;
}
.rongyuzizhi4:hover{
    /* background-image: url('../images/rongyuzizhilogo4.png'); */
}
.rongyuzizhi40{
    width: 140px;
    height: 140px;
    /* background-image: url('../images/rongyuzizhilogo4.png'); */
    background-repeat: no-repeat;
    background-size: 100% 100%;
    position: absolute;
    right: -35px;
    top: -635px;
    z-index: 10000;
}
/* 第六个球 */
.rongyuzizhi5{
    width: 140px;
    height: 140px;
    /* background-image: url('../images/rongyuzizhilogo51.png'); */
    background-repeat: no-repeat;
    background-size: 100% 100%;
    position: absolute;
    left: 740px;
    top: -282px;
    z-index: 10000;
}
.rongyuzizhih55{
    width: 140px;
    height: 60px;
    text-align: center;
    position: absolute;
    left: 740px;
    top: -135px;
    z-index: 10000;
    font-size: 18px;
}
.rongyuzizhi5:hover{
    /* background-image: url('../images/rongyuzizhilogo5.png'); */
}
.rongyuzizhi50{
    width: 140px;
    height: 140px;
    /* background-image: url('../images/rongyuzizhilogo5.png'); */
    background-repeat: no-repeat;
    background-size: 100% 100%;
    position: absolute;
    left: 740px;
    top: -282px;
    z-index: 10000;
}

.jiangxiangbanner{
    width: 100%;
    height: 480px;
    /* background-image: url('../images/jiangxiangbanner.png'); */
    background-repeat: no-repeat;
    /* background-size: 100% 100%; */
    background-position: center center;
}
.jiangxiangbanner3{
    width: 1225px;
    height: 593px;
    /* background-image: url('../images/jiangxiangbanner3.png'); */
    background-repeat: no-repeat;
    /* background-size: 100% 100%; */
    background-position: center center;
    margin: 0 auto;
    margin-top: 40px;
    margin-bottom: 90px;
}
.jiangxiangTop{
    width: 969px;
    height: 10px;
    margin: 0 auto;
    position: relative;
    overflow: visible;
}
.bannerDevelop81-h2{
    width: 1160px;
    margin: 0 auto;
    line-height: 66px;
    font-size: 42px;
    padding-top: 122px;
    padding-bottom: 18px;
}
.bannerDevelop81-p{
    width: 1160px;
    margin: 0 auto;
    line-height: 30px;
    font-size: 20px;
    font-weight: 300;
}

.card-item-icon {
    display: none;
    width: 80px;
    /*margin-top: -20px;*/
    height: 75px;
    background-repeat: no-repeat;
    background-size: 100% auto;
    background-position: top;
    position: absolute;
    right: 12px;
    top: -15px;
}
.solution-menu .active .card-item-icon {
    -webkit-animation: tpm-product-multi__icon-enter .5s steps(9) forwards;
    animation: tpm-product-multi__icon-enter .5s steps(9) forwards
}

@-webkit-keyframes tpm-product-multi__icon-enter {
    0% {
        background-position: 0 0
    }

    to {
        background-position: 0 -720px
    }
}

@keyframes tpm-product-multi__icon-enter {
    0% {
        background-position: 0 0
    }

    to {
        background-position: 0 -720px
    }
}

@-webkit-keyframes tpm-product-multi__icon-leave {
    0% {
        background-position: 0 -720px
    }

    to {
        background-position: 0 0
    }
}

@keyframes tpm-product-multi__icon-leave {
    0% {
        background-position: 0 -720px
    }

    to {
        background-position: 0 0
    }
}

.industryBody {
    height: 570px;
    color: #fff;
    margin-top: 20px;
}

.industryBody .industryOrganWarp {
    display: flex;
    flex-direction: row;
    align-items: stretch;
    height: 570px;
}
.industryBody .industryOrganWarp .item {
    display: flex;
    flex: 1;
    transition: all 0.5s ease;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
    overflow: hidden;
}

.industryBody .industryOrganWarp .item.active {
    flex-grow: 4;
    /*box-shadow: inset 0 0 20px #000;*/
}
.industryBody .retract{
    display: flex;
}
.industryBody .expand{
    display: none;
}
.industryBody .retract,.industryBody .expand{
    align-content: center;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    width: 80%;
    margin: 0 auto;
}
.industryBody .retract .i-icon{
    width: 73px;
    height: auto;
}
.industryBody .retract .i-title{
    margin-top: 20px;
    font-size: 20px;
    line-height: 26px;
}

.industryBody .expand .i-title{
    font-size: 30px;
    line-height: 40px;
    font-weight: bold;
}
.industryBody .expand .i-desc{
    margin: 30px 0 62px 0;
}
.industryBody .expand .i-btn{
    background: #21c9b8;
    border-radius: 2px;
    line-height: 40px;
    display: inline-block;
    color: #fff;
    text-align: center;
    width: 130px;
}
.industryBody .industryOrganWarp .item.active .expand{
    display: flex;
}
.industryBody .industryOrganWarp .item.active .retract{
    display: none;
}
.applicationBox{
    height: 658px;
    box-sizing: border-box;
    padding-top: 48px;
}
.applicationBody {
    display: flex;
    flex-wrap: wrap;
}

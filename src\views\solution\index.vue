<template>
  <div class="content">
    <div class="content_banner">
      <div style="height: 37px">解决方案</div>
      <div style="height: 33px; margin-top: 1px"></div>
      <div class="activity-title-content">
        <div class="activity-search-box">
          <el-form ref="form" class="activity-search-form">
            <el-form-item>
              <el-input v-model="params.searchStr" placeholder="请输入搜索内容" class="activity-search-input">
                <el-button slot="append" class="activity-search-btn" @click="onSearch">搜索</el-button>
              </el-input>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div class="hot_search">
        <span>热门搜索：</span>
        <span class="hot_search_item" @click="searchHot('供应链管理')">供应链管理</span>
        <span class="hot_search_item" @click="searchHot('设备智慧物联')">设备智慧物联</span>
        <span class="hot_search_item" @click="searchHot('生产过程管控')">生产过程管控</span>
        <span class="hot_search_item" @click="searchHot('科技成果转化')">科技成果转化</span>
        <span class="hot_search_item" @click="searchHot('企业运营管理')">企业运营管理</span>
        <span class="hot_search_item" @click="searchHot('产业转型升级')">产业转型升级</span>
        <span class="hot_search_item" @click="searchHot('产融服务')">产融服务</span>
      </div>
    </div>
    <!-- 底部内容 -->
    <div class="content_bottom">
      <div class="icondiv">
        <div class="solutioniconFlex">
          <div v-for="(item, index) in typeList" :key="item.id"
            :class="['iconFlexTitle', aaa == item.id ? 'activeTitle' : '']" @click="changeSolve(item.id)">
            {{ item.typeName }}
          </div>
        </div>
      </div>
      <div class="demandContent" style="padding-top: 40px">
        <div class="demandflex" style="height: 715px">
          <div class="leftsolution">
            <div :class="['leftTitle', bbb == 1 ? 'leftTitleHover' : '']" @click="changeSolveB(1)">
              全部（{{ total1 }}）
            </div>
            <div v-for="(item, index) in typeNestList" :key="index" :class="[
              'leftTitle',
              bbb == item.solutionTypeId ? 'leftTitleHover' : '',
            ]" @click="changeSolveB(item.solutionTypeId)">
              <span class="tr2">{{ item.solutionTypeName }}（{{ item.totalCount }}）</span>
            </div>
          </div>
          <div class="rightSolution" v-if="dataList && dataList.length > 0">
            <div v-for="(item, index) in dataList" :key="index" class="solutionContent tr2">
              <div @click="goDetail(item.solutionId)">
                <div class="solutionContentTitle tr2">
                  {{ item.solutionName }}
                </div>
                <div class="solutionContentValue tr2 textOverflow">
                  {{ item.solutionIntroduction }}
                </div>
              </div>
            </div>
          </div>
          <div class="rightEmpty" v-else>
            <el-empty description="暂无数据"></el-empty>
          </div>
        </div>
        <!-- 分页 -->
        <div class="pageStyle">
          <el-pagination v-if="dataList && dataList.length > 0" background layout="prev, pager, next"
            class="activity-pagination" :page-size="params.pageSize" :current-page="params.pageNum" :total="total"
            @size-change="handleSizeChange" @current-change="handleCurrentChange">
          </el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { getSolutionList, getSolutionTypeList } from "@/api/solution";
export default {
  name: "demandHall",
  data() {
    return {
      params: {
        parentId: "",
        searchStr: "",
        solutionTypeId: "",
        pageNum: 1,
        pageSize: 10,
        category: 1,
      },
      total: 0,
      total1: 0,
      keywords: "",
      form: {},
      flag: "全部",
      appliTypeData: [
        {
          dictValue: "0",
          dictLabel: "全部",
        },
        {
          dictLabel: "创新研发",
          dictValue: "1",
        },
        {
          dictLabel: "物料采购",
          dictValue: "2",
        },
        {
          dictLabel: "智能制造",
          dictValue: "3",
        },
        {
          dictLabel: "数字化管理",
          dictValue: "4",
        },
        {
          dictLabel: "软件服务",
          dictValue: "5",
        },
        {
          dictLabel: "供应链金融",
          dictValue: "6",
        },
        {
          dictLabel: "运营宣传",
          dictValue: "7",
        },
        {
          dictLabel: "其他",
          dictValue: "8",
        },
      ],
      appliTypeImgList: [
        {
          url: require("@/assets/appliMarket/type1.png"),
        },
        {
          url: require("@/assets/appliMarket/type2.png"),
        },
        {
          url: require("@/assets/appliMarket/type3.png"),
        },
        {
          url: require("@/assets/appliMarket/type4.png"),
        },
        {
          url: require("@/assets/appliMarket/type5.png"),
        },
        {
          url: require("@/assets/appliMarket/type6.png"),
        },
        {
          url: require("@/assets/appliMarket/type7.png"),
        },
        {
          url: require("@/assets/appliMarket/type8.png"),
        },
        {
          url: require("@/assets/appliMarket/type9.png"),
        },
      ],
      demandList: [
        {
          title: "需要采购KBOFLEX伺服编码器电缆",
          url: require("@/assets/demand/xqimgdefault.png"),
          appliArea: "",
          requireType: "其它",
          desc: "需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆",
          publishTime: "2025-02-28 09:49:21",
        },
        {
          title: "需要采购KBOFLEX伺服编码器电缆",
          url: require("@/assets/demand/xqimgdefault.png"),
          appliArea: "",
          requireType: "其它",
          desc: "需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆",
          publishTime: "2025-02-28 09:49:21",
        },
        {
          title: "需要采购KBOFLEX伺服编码器电缆",
          url: require("@/assets/demand/xqimgdefault.png"),
          appliArea: "",
          requireType: "其它",
          desc: "需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆",
          publishTime: "2025-02-28 09:49:21",
        },
        {
          title: "需要采购KBOFLEX伺服编码器电缆",
          url: require("@/assets/demand/xqimgdefault.png"),
          appliArea: "",
          requireType: "其它",
          desc: "需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆",
          publishTime: "2025-02-28 09:49:21",
        },
        {
          title: "需要采购KBOFLEX伺服编码器电缆",
          url: require("@/assets/demand/xqimgdefault.png"),
          appliArea: "",
          requireType: "其它",
          desc: "需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆",
          publishTime: "2025-02-28 09:49:21",
        },
        {
          title: "需要采购KBOFLEX伺服编码器电缆",
          url: require("@/assets/demand/xqimgdefault.png"),
          appliArea: "",
          requireType: "其它",
          desc: "需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆",
          publishTime: "2025-02-28 09:49:21",
        },
        {
          title: "需要采购KBOFLEX伺服编码器电缆",
          url: require("@/assets/demand/xqimgdefault.png"),
          appliArea: "",
          requireType: "其它",
          desc: "需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆",
          publishTime: "2025-02-28 09:49:21",
        },
        {
          title: "需要采购KBOFLEX伺服编码器电缆",
          url: require("@/assets/demand/xqimgdefault.png"),
          appliArea: "",
          requireType: "其它",
          desc: "需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆",
          publishTime: "2025-02-28 09:49:21",
        },
        {
          title: "需要采购KBOFLEX伺服编码器电缆",
          url: require("@/assets/demand/xqimgdefault.png"),
          appliArea: "",
          requireType: "其它",
          desc: "需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆",
          publishTime: "2025-02-28 09:49:21",
        },
        {
          title: "需要采购KBOFLEX伺服编码器电缆",
          url: require("@/assets/demand/xqimgdefault.png"),
          appliArea: "",
          requireType: "其它",
          desc: "需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆",
          publishTime: "2025-02-28 09:49:21",
        },
      ],
      aaa: "1",
      bbb: 1,
      typeList: [
        {
          id: "1",
          typeName: "行业解决方案",
        },
        {
          id: "2",
          typeName: "领域解决方案",
        },
      ],
      typeNestList: [],
      dataList: [],
    };
  },
  created() {
    this.getTypeNext('1');
  },
  methods: {
    async getDemandList() {
      this.params.category = this.aaa;
      let res = await getSolutionList(this.params);
      if (res.code == 200) {
        this.dataList = res.rows;
        this.total = res.total;
        console.log(this.total, "total");
        if (this.params.solutionTypeId == "") {
          this.total1 = res.total;
        }
      }
    },
    searchHot(val) {
      this.params.searchStr = val;
      this.onSearch();
    },
    onSearch() {
      this.params.pageNum = 1;
      this.getDemandList();
    },
    getappliData(value) {
      this.flag = value;
      this.getDemandList();
    },
    async getTypeNext(val) {
      let res = await getSolutionTypeList({ category: val });
      if (res.code == 200) {
        this.typeNestList = res.rows;
        this.getDemandList();
      }
    },
    changeSolve(val) {
      this.aaa = val;
      this.params.parentId = val;
      this.params.solutionTypeId = "";
      this.bbb = 1;
      this.params.pageSize = 10;
      this.params.pageNum = 1;
      this.getTypeNext(val);
    },
    changeSolveB(val) {
      this.bbb = val;
      this.params.pageSize = 10;
      this.params.pageNum = 1;
      if (val == 1) {
        this.params.solutionTypeId = "";
      } else {
        this.params.solutionTypeId = val;
      }
      this.getDemandList();
    },
    handleSizeChange(pageSize) {
      this.params.pageSize = pageSize;
      this.getDemandList();
    },
    handleCurrentChange(pageNum) {
      this.params.pageNum = pageNum;
      this.getDemandList();
    },

    goDetail(id) {
      this.$router.push("/solutionDetail?id=" + id);
    },
  },
};
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  padding-bottom: 60px;
}

.content_banner {
  width: 100%;
  height: 300px;
  background-image: url("../../assets/release/banner.png");
  background-size: 100% 100%;
  text-align: center;
  margin: 0 auto;
  padding-top: 71px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  font-size: 40px;
  color: #000;

  .hot_search {
    font-size: 14px;
    color: #000;

    .hot_search_item {
      margin-right: 20px;
      color: #000;
      cursor: pointer;
    }
  }
}

.content_bottom {
  .icondiv {
    background-color: rgba(255, 255, 255, 1);
    width: 100%;
    height: 100px;
    position: relative;

    .solutioniconFlex {
      display: flex;
      position: absolute;
      bottom: 0;
      width: 1200px;
      right: 0;
      left: 0;
      margin: auto;
      justify-content: center;

      .iconFlexTitle {
        width: 110px;
        height: 45px;
        line-height: 26px;
        border-radius: 2px;
        color: rgba(51, 51, 51, 1);
        font-size: 18px;
        text-align: center;
        margin: 0 20px;
        cursor: pointer;
      }

      .activeTitle {
        color: #0cad9d;
        border-bottom: 2px solid #0cad9d;
      }
    }
  }

  .demandContent {
    width: 100%;
    background: #f7f8fa;
    // background: #fff;
    padding-top: 20px;
    box-shadow: #21c9b8 solid 1px;
    // border: #21c9b8 solid 1px;

    .demandflex {
      width: 1200px;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;

      .leftsolution {
        width: 185px;
        height: 715px;
        line-height: 20px;
        opacity: 0.95;
        border-radius: 4px;
        background: linear-gradient(180deg,
            rgba(244, 246, 249, 1) 0%,
            rgba(255, 255, 255, 1) 100%);
        color: rgba(16, 16, 16, 1);
        font-size: 14px;
        box-shadow: 0px 0px 8px 0px rgba(218, 235, 253, 75);
        border: 2px solid rgba(255, 255, 255, 1);
        padding: 20px 0;
        box-sizing: border-box;
        overflow-y: auto;

        .leftTitle {
          color: rgba(51, 51, 51, 1);
          font-size: 16px;
          margin: 30px 0;
          padding-left: 20px;
          border-left: 3px solid transparent;
          cursor: pointer;
        }

        .leftTitleHover {
          color: #0cad9d;
          border-left: 3px solid #0cad9d;
        }
      }

      .rightSolution {
        width: 1000px;
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        align-content: flex-start;

        .solutionContent {
          width: 490px;
          height: 124px;
          border: 2px solid transparent;
          padding: 20px;
          box-sizing: border-box;
          cursor: pointer;
        }

        .solutionContent:hover {
          opacity: 0.95;
          border-radius: 4px;
          background: linear-gradient(180deg,
              rgba(244, 246, 249, 1) 0%,
              rgba(255, 255, 255, 1) 100%);
          color: rgba(16, 16, 16, 1);
          font-size: 14px;
          box-shadow: 0px 0px 8px 0px rgba(218, 235, 253, 75);
          border: 2px solid rgba(255, 255, 255, 1);
        }

        .solutionContentTitle {
          color: rgba(51, 51, 51, 1);
          font-size: 18px;
          margin-bottom: 10px;
        }

        .solutionContent:hover .solutionContentTitle {
          color: #0cad9d;
        }

        .solutionContentValue {
          color: rgba(102, 102, 102, 1);
          font-size: 12px;
          line-height: 1.5;
        }
      }

      .rightEmpty {
        width: 1000px;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
}

.activity-title-content {
  width: 100%;

  // background-color: #fff;
  .activity-title-box {
    width: 336px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 60px 0 40px;

    .activity-title {
      font-size: 40px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #333;
      line-height: 40px;
      padding: 0 40px;
    }

    .activity-divider {
      width: 48px;
      height: 4px;
      background: #21c9b8;
    }
  }

  .activity-search-box {
    margin-top: 40px;

    .activity-search-form {
      text-align: center;

      .activity-search-input {
        width: 792px;
        height: 54px;

        .activity-search-btn {
          width: 100px;
        }
      }
    }
  }
}

.content_bottom {
  display: flex;
  align-items: center;
  flex-wrap: wrap;

  .content_bottom_item {
    margin-top: 20px;
    width: 590px;
    height: 208px;
    background: #ffffff;
    box-shadow: 0px 4px 18px 2px #e8f1fa;
    border-radius: 10px;
    padding: 20px;
    cursor: pointer;

    .detailTitle {
      height: 30px;
      color: rgba(51, 51, 51, 1);
      font-size: 18px;
      margin-bottom: 10px;
    }

    .textOverflow1 {
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
    }

    .demandChunk {
      display: flex;
      justify-content: space-between;

      .demand_right {
        width: 413px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
      }

      .demandTopRightflex {
        display: flex;
        line-height: 24px;
      }

      .detailrightTitle {
        color: rgba(153, 153, 153, 1);
        font-size: 14px;
      }

      .detailrightTitle2 {
        color: rgba(0, 0, 0, 0.85);
        font-size: 14px;
      }

      .detailrightContent {
        width: 343px;
        color: rgba(51, 51, 51, 1);
        font-size: 14px;
      }
    }
  }

  .content_bottom_item:hover {
    box-shadow: 0px 3px 20px 0px rgba(132, 212, 178, 0.6);
    scale: 1.01;
  }

  .content_bottom_item:nth-child(2n) {
    margin-left: 20px;
  }
}

.pageStyle {
  margin-top: 60px;
  width: 100%;
  text-align: center;
  margin-bottom: 30px;
}
</style>
<style lang="scss">
.activity-search-input {
  .el-input__inner {
    height: 54px;
    background: #fff;
    border-radius: 27px 0 0 27px;
    border: 1px solid #d9d9d9;
    font-size: 16px;
    font-family: PingFangSC-Regular, PingFang SC;
    line-height: 24px;
    padding-left: 30px;
  }

  .el-input-group__append {
    border-radius: 0px 100px 100px 0px;
    background: #21c9b8;
    font-size: 16px;
    font-family: PingFangSC-Regular, PingFang SC;
    color: #fff;
    line-height: 24px;
  }
}
</style>

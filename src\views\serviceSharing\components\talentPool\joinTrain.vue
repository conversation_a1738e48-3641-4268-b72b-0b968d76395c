<template>
  <div class="content">
    <div class="content_banner">
      人才服务
    </div>
    <div class="card-container content_card">
      <div class="card-title">人才培训</div>
      <el-form ref="form" :rules="rules" :model="form" label-position="top">
        <el-row :gutter="50">
          <el-col :span="12">
            <el-form-item label="姓名" prop="name">
              <el-input v-model="form.name" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系方式" prop="contact">
              <el-input v-model="form.contact" placeholder="请输入联系方式"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="性别" prop="gender">
              <el-select v-model="form.gender" placeholder="请选择性别" clearable style="width: 100%">
                <el-option v-for="dict in sexList" :key="dict.dictLabel" :label="dict.dictLabel"
                  :value="dict.dictValue" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="培训课程" prop="trainingCourse">
              <el-select v-model="form.trainingCourse" placeholder="请选择培训课程" clearable style="width: 100%">
                <el-option v-for="dict in courseList" :key="dict.dictLabel" :label="dict.dictLabel"
                  :value="dict.dictValue" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="申请时间" prop="applyTime">
              <el-date-picker v-model="form.applyTime" type="date" placeholder="选择申请时间" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item class="footer-submit">
              <el-button type="primary" @click="onSubmit">提交</el-button>
              <el-button style="margin-left: 140px" @click.once="onCancel">取消</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
</template>
<script>
import { listData } from "@/api/system/dict/data";
import { applyTrain } from "@/api/serviceSharing";

export default {
  data() {
    return {
      form: {},
      rules: {
        name: [{ required: true, message: "姓名不能为空", trigger: "blur" }],
        contact: [
          { required: true, message: "联系方式不能为空", trigger: "blur" },
        ],
        trainingCourse: [
          { required: true, message: "培训课程不能为空", trigger: "blur" },
        ],
        applyTime: [
          { required: true, message: "申请时间不能为空", trigger: "blur" },
        ],
      },
      courseList: [], // 培训课程
      sexList: [], // 性别
    };
  },
  created() {
    this.getSex();
    this.getCourse()
  },
  methods: {
    getSex() {
      let params = { dictType: "sys_user_sex" };
      listData(params).then((response) => {
        this.sexList = response.rows;
      });
    },
    getCourse() {
      let params = { dictType: "train_course" };
      listData(params).then((response) => {
        this.courseList = response.rows
      })
    },
    onSubmit() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          applyTrain(this.form).then((response) => {
            if (response.code === 200) {
              this.$message({
                message: "提交申请成功",
                type: "success",
                duration: 2000,
              })
              this.onCancel()
            } else {
              this.$message.warning(response.msg)
            }
          })
        }
      });
    },
    onCancel() {
      this.$router.go(-1);
    },
  },
};
</script>
<style scoped lang="scss">
.content {
  width: 100%;
  padding-bottom: 60px;
  background-color: #f2f2f2;
}

.content_banner {
  width: 100%;
  height: 300px;
  background-image: url("../../../../assets/release/banner.png");
  background-size: 100% 100%;
  text-align: center;
  margin: 0 auto;
  font-family: Source Han Sans CN;
  font-weight: 500;
  font-size: 40px;
  color: #000;
  line-height: 300px;

  .imgContent {
    width: 100%;
    display: flex;
    justify-content: center;
    margin-top: 10px;

    .imgStyle {
      width: 1256px;
      height: 206px;
      position: relative;
    }
  }
}

.content_card {
  // height: 1530px;
  background: #ffffff;
  border-radius: 2px;
  margin-top: 30px;
  padding: 29px 60px 57px 60px;

  .card-title {
    font-weight: 500;
    font-size: 22px;
    color: #222222;
    line-height: 70px;
  }
}

.addStyle {
  font-family: Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #21c9b8;
  margin-left: auto;
  cursor: pointer;
}

.footer-submit {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin-top: 60px;

  .el-button {
    width: 140px;
    height: 50px;
  }
}
</style>

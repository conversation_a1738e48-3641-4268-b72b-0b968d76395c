<template>
  <div
    class="cardBg wow animate__animated animate__fadeInUp"
    data-wow-duration="1s"
  >
    <div class="card-container">
      <div class="enterpriseTitle">
        <div>专家智库</div>
        <div class="allEnterprise" @click="gothinkTank">查看全部>></div>
      </div>
      <div class="expert-library-list">
        <div
          v-for="(item, index) in data"
          :key="index"
          class="list-item-content"
          @click="goExpertLibrary(item.id)"
        >
          <div class="list-item-box">
            <div class="item-headline">
              <div class="item-title">
                {{ item.expertName }}
              </div>
            </div>
            <div class="expert-library-label">
              <div
                v-for="(val, index1) in item.techniqueTypeName"
                :key="index1"
                class="library-label-item"
              >
                <span v-if="index1 < 2" class="expert-library-type">{{
                  `#${val}`
                }}</span>
                <span v-else>…</span>
              </div>
            </div>
            <div class="expert-library-box">
              {{ item.synopsis }}
            </div>
          </div>
          <div class="list-item-img">
            <img v-if="item.headPortrait" :src="item.headPortrait" alt="" />
            <img
              v-else
              src="../../../assets/expertLibrary/defaultImg.png"
              alt=""
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getExpertList } from "@/api/purchaseSales";
import CryptoJS from "crypto-js";
let secretKey = "9zVn0%bqmUYSGw2n";

export default {
  data() {
    return {
      loading: false,
      data: [],
      pageNum: 1,
      total: 0,
    };
  },
  created() {
    // this.searchExpert();
  },
  methods: {
    gothinkTank() {
      let routeData = this.$router.resolve({
        path: "/thinkTank",
      });
      window.open(routeData.href, "_blank");
    },
    searchExpert() {
      this.loading = true;
      getExpertList({
        pageSize: 4,
      })
        .then((res) => {
          let key = CryptoJS.enc.Utf8.parse(secretKey);
          let decrypt = CryptoJS.AES.decrypt(res, key, {
            mode: CryptoJS.mode.ECB,
            padding: CryptoJS.pad.Pkcs7,
          });
          res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));

          this.loading = false;
          let { rows, total } = res || [];
          this.data = rows.slice(0, 4);
          this.data.forEach((item) => {
            item.techniqueTypeName = item.techniqueTypeName
              ? item.techniqueTypeName.split(",")
              : [];
          });
          this.total = total;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    // 跳转到专家详情页面
    goExpertLibrary(id) {
      let routeData = this.$router.resolve({
        path: "/expertDetail",
        query: { id },
      });
      window.open(routeData.href, "_blank");
    },
  },
};
</script>

<style lang="scss" scoped>
.cardBg {
  width: 100%;
  height: 740px;
  background-image: url("../../../assets/images/home/<USER>");
  background-size: 100% 100%;
}
.enterpriseTitle {
  width: 100%;
  font-size: 36px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  color: #000000;
  text-align: center;
  margin: 60px 0 20px 0;
  padding-top: 70px;
  position: relative;
  .allEnterprise {
    position: absolute;
    top: 8 px;
    right: 0;
    font-size: 16px;
    font-family: Source Han Sans CN;
    font-weight: 500;
    color: #21c9b8;
    line-height: 26px;
    cursor: pointer;
  }
}
.content {
  display: flex;
  justify-content: space-between;
  width: 100%;
  height: 280px;
  .contentItem {
    width: 23%;
    height: 100%;
    text-align: center;
    cursor: pointer;
    background: #ffffff;
    box-shadow: 0px 2px 20px 0px rgba(32, 84, 252, 0.14);
    border-radius: 4px;
    img {
      width: 100%;
      height: 230px;
    }
  }
}
.expert-library-list {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  width: 100%;
  .list-item-content {
    display: flex;
    justify-content: space-between;
    width: 578px;
    background: #fff;
    margin-top: 31px;
    padding: 28px 32px;
    min-height: 240px;
    .list-item-box {
      flex: 1;
      .item-headline {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .item-title {
          width: 280px;
          font-size: 32px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #333;
          line-height: 32px;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
          word-wrap: break-word;
        }
      }
      .expert-library-label {
        display: flex;
        flex-wrap: wrap;
        margin: 0 0 16px;
        .library-label-item {
          max-width: 350px;
          padding: 6px 12px;
          background: #f4f5f9;
          border-radius: 4px;
          font-size: 12px;
          font-family: PingFangSC-Regular, PingFang SC;
          color: #666;
          line-height: 12px;
          margin: 24px 16px 0 0;
          .expert-library-type {
            word-wrap: break-word;
          }
        }
      }
      .expert-library-box {
        width: 370px;
        font-size: 16px;
        font-family: PingFangSC-Regular, PingFang SC;
        color: #666;
        line-height: 32px;
        overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        text-overflow: ellipsis;
        word-wrap: break-word;
      }
    }
    .list-item-img {
      width: 120px;
      height: 168px;
      margin-left: 24px;
      img {
        width: 100%;
        height: 100%;
      }
    }
    &:hover {
      cursor: pointer;
    }
  }
  .list-item-content:hover {
    box-shadow: 0px 2px 20px 0px rgba(13, 230, 96, 0.3);
  }
}
</style>

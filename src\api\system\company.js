/*
 * @Author: zhc
 * @Date: 2023-02-12 11:18:27
 * @LastEditTime: 2023-12-09 20:50:14
 * @Description:
 * @LastEditors: JHY
 */
/*
 * @Author: zhc
 * @Date: 2023-02-11 14:45:07
 * @LastEditTime: 2023-02-11 15:26:15
 * @Description:
 * @LastEditors: zhc
 */

import request from "@/utils/request";

// 企业详情
export function getCompanyDetail(params) {
  return request({
    url: "/system/company/mag/detailShow",
    method: "get",
    params,
  });
}
// 个人中心-公司信息
export function getCompanyDetailByBussinessNo(params) {
  return request({
    url: "/system/company/mag/detailByBussinessNo",
    method: "get",
    params,
  });
}
// 修改企业信息
export function editCompany(params) {
  return request({
    url: "/system/company/mag/edit",
    method: "post",
    data: params,
  });
}

// 认证企业设置
export function setCompanyAuth(params) {
  return request({
    url: "/portalweb/Member/addCompany",
    method: "post",
    data: params,
  });
}
// 搜索企业
export function searchCompany(params) {
  return request({
    url: "/portalweb/Company/searchByKeywords",
    method: "get",
    params,
  });
}
// 根据公司名称查询社会信用代码
export function getCompanyCodeByName(params) {
  return request({
    url: "/portalweb/Company/searchByCustomerName",
    method: "get",
    params,
  });
}
// 获取我的企业信息
export function getMyCompanyInfo(params) {
  return request({
    url: "/portalweb/Member/getMyCompany",
    method: "get",
    params,
  });
}
<template>
  <div class="activity-container">
    <div class="activity-banner">
      <img src="../../assets/classicCase/classicCaseBanner.png" alt="" />
      <div class="bannerTitle">典型案例</div>
      <div class="bannerDesc">
        积累众多优秀典型案例，提供适用于不同行业、领域的低碳转型项目案例
      </div>
      <div class="classicCaseType">
        <div
          v-for="item in caseTypeList"
          :key="item.dictValue"
          class="caseName"
          :class="activeName == item.dictValue ? 'caseNameHover' : ''"
          @click="getCaseType(item.dictValue)"
        >
          {{ item.dictLabel }}
        </div>
      </div>
    </div>
    <div v-loading="loading">
      <!-- <div class="activity-title-content">
        <div class="activity-title-box">
          <div class="activity-divider"></div>
          <div class="activity-title">典型案例</div>
          <div class="activity-divider"></div>
        </div>
        <div class="activity-search-box">
          <el-form ref="form" class="activity-search-form" :model="form">
            <el-form-item>
              <el-input
                v-model="form.keywords"
                placeholder="请输入搜索内容"
                class="activity-search-input"
              >
                <el-button
                  slot="append"
                  class="activity-search-btn"
                  @click="onSearch"
                  >搜索</el-button
                >
              </el-input>
            </el-form-item>
          </el-form>
        </div>
      </div> -->
      <div class="activity-info-content">
        <div v-if="data && data.length > 0" class="activityList">
          <div
            v-for="(item, index) in data"
            :key="index"
            class="activity-list-item"
            @click="goCaseDetail(item.id)"
          >
            <div class="list-item-content">
              <div class="list-item-img">
                <img v-if="item.coverPicUrl" alt="" :src="item.coverPicUrl" />
              </div>
              <div class="list-item-title">
                {{ item.name }}
              </div>
              <div class="list-item-icon"></div>
              <div v-html="item.detail" class="detailContent"></div>
              <div class="itemButton">案例详情</div>
            </div>
          </div>
        </div>
        <div class="none-class" v-else>
          <el-image
            style="width: 160px; height: 160px"
            :src="require('@/assets/user/none.png')"
            :fit="fit"
          ></el-image>
          <div class="text">暂无数据</div>
        </div>
        <div class="activity-page-end">
          <el-button class="activity-page-btn" @click="goHome">首页</el-button>
          <el-pagination
            v-if="data && data.length > 0"
            background
            layout="prev, pager, next"
            class="activity-pagination"
            :page-size="pageSize"
            :current-page="pageNum"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          >
          </el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getDicts } from "@/api/system/dict/data";
import { caseList } from "@/api/classicCase";

export default {
  data() {
    return {
      fit: "cover",
      loading: false,
      form: {
        keywords: "", //搜索内容
      },
      formInfo: {
        caseType: "", // 案例类型
      },
      caseTypeList: [],
      data: [],
      pageNum: 1,
      pageSize: 9,
      total: 0,
      activeName: "",
    };
  },
  created() {
    this.initData();
    // this.getDictsList("activity_type", "activityTypeList");
    // this.search();
  },
  methods: {
    getCaseList() {
      this.loading = true;
      let params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        industry: this.formInfo.caseType,
        name: this.form.keywords,
      };
      caseList(params).then((response) => {
        this.data = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    changeRadio() {
      this.onSearch();
    },
    handleSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.onSearch();
    },
    handleCurrentChange(pageNum) {
      this.pageNum = pageNum;
      this.getCaseList();
    },
    onSearch() {
      this.pageNum = 1;
      this.getCaseList();
    },
    goCaseDetail(id) {
      let routeData = this.$router.resolve({
        path: "/caseDetail",
        query: { id },
      });
      window.open(routeData.href, "_blank");
    },
    goHome() {
      this.$router.push({ path: "/index" });
    },
    initData() {
      getDicts("case_industry").then((res) => {
        const { code, data = [] } = res;
        if (code === 200) {
          this.caseTypeList = data;
          // console.log(this.caseTypeList, "9999999999999");
          this.caseTypeList.unshift({
            dictValue: "",
            dictLabel: "全部",
          });
          this.getCaseList();
        }
      });
    },
    getCaseType(value) {
      this.activeName = value;
      this.formInfo.caseType = this.activeName;
      this.getCaseList();
    },
  },
};
</script>

<style lang="scss" scoped>
.activity-container {
  width: 100%;
  // background: #f4f5f9;
  .activity-banner {
    width: 100%;
    height: 500px;
    position: relative;
    img {
      width: 100%;
      height: 100%;
    }
    .bannerTitle {
      position: absolute;
      top: 161px;
      left: 24%;
      font-size: 50px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #ffffff;
    }
    .bannerDesc {
      position: absolute;
      top: 249px;
      left: 24%;
      font-size: 24px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #ffffff;
    }
    .classicCaseType {
      position: absolute;
      bottom: -45px;
      left: calc((100% - 1100px) / 2);
      width: 1100px;
      height: 90px;
      background: #ffffff;
      box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.04);
      border-radius: 45px;
      display: flex;
      justify-content: center;
      align-items: center;
      .caseName {
        width: 100px;
        height: 40px;
        border-radius: 20px;
        margin-left: 15px;
        text-align: center;
        line-height: 40px;
        cursor: pointer;
        font-size: 18px;
        font-family: Source Han Sans CN;
        font-weight: 500;
        color: #979797;
      }
      .caseNameHover {
        background: #21c9b8;
        color: #ffffff;
      }
      .caseName:nth-child(1) {
        margin-left: 0;
      }
    }
  }
  .activity-title-content {
    width: 100%;
    background-color: #fff;
    padding-bottom: 18px;
    .activity-title-box {
      width: 336px;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 60px 0 40px;
      .activity-title {
        font-size: 40px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #333;
        line-height: 40px;
        padding: 0 40px;
      }
      .activity-divider {
        width: 48px;
        height: 4px;
        background: #21c9b8;
      }
    }
    .activity-search-box {
      .activity-search-form {
        text-align: center;
        .activity-search-input {
          width: 792px;
          height: 54px;
          .activity-search-btn {
            width: 100px;
          }
        }
      }
    }
  }
  .activity-info-content {
    width: 1200px;
    margin: 40px auto 0;
    background: rgb(253, 253, 253);
    .activity-search-type-box {
      background: #fff;
      margin-bottom: -7px;
      .activity-search-line {
        padding: 14px 24px;
        .activity-search-line-item {
          margin-bottom: 0;
        }
        & + .activity-search-line {
          border-top: 1px solid #f5f5f5;
        }
      }
    }
    .activityList {
      display: flex;
      width: 100%;
      flex-wrap: wrap;
    }
    .activity-list-item {
      width: 380px;
      height: 420px;
      background: #ffffff;
      border-radius: 12px;
      margin-top: 24px;
      margin-left: 26px;
      .list-item-content {
        padding: 20px;
        cursor: pointer;
        .list-item-img {
          width: 340px;
          height: 200px;
          img {
            width: 100%;
            height: 100%;
            border-radius: 5px;
          }
        }
        .list-item-title {
          margin-top: 20px;
          font-size: 20px;
          font-family: Source Han Sans CN;
          font-weight: 500;
          color: #333333;
        }
        .list-item-icon {
          width: 38px;
          height: 4px;
          background: #21c9b8;
          border-radius: 2px;
          margin-top: 14px;
        }
        .detailContent {
          color: #666;
          overflow: hidden;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          text-overflow: ellipsis;
        }
        .itemButton {
          width: 140px;
          height: 44px;
          line-height: 44px;
          border: 1px solid #21c9b8;
          border-radius: 22px;
          margin: 15px auto;
          text-align: center;
          font-size: 18px;
          font-family: Source Han Sans CN;
          font-weight: 500;
          color: #21c9b8;
        }
        .list-item-info {
          width: 700px;
          padding-left: 24px;
          font-family: PingFangSC-Regular, PingFang SC;
          margin: auto;
          .cusPoints {
            position: relative;
            font-size: 20px;
            font-family: Source Han Sans CN;
            font-weight: 400;
            color: #222222;
            line-height: 30px;
            margin-top: 20px;
            .rectangle {
              position: absolute;
              top: 12px;
              left: 0;
            }
          }
          .list-item-text {
            // height: 40px;
            overflow: hidden;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            text-overflow: ellipsis;
            font-size: 16px;
            color: #666;
            // line-height: 30px;
            word-wrap: break-word;
            margin-top: 15px;
          }
          .list-item-time {
            color: #999;
            line-height: 14px;
            margin-top: 24px;
          }
        }
        &:hover {
          .list-item-title {
            color: #21c9b8;
          }
        }
      }
    }
    .activity-list-item:nth-child(3n + 1) {
      margin-left: 0;
    }
    .activity-list-item:hover {
      background-color: #ffffff;
      box-shadow: 0 4px 16px 0 rgba(38, 74, 116, 0.1);
      .list-item-title {
        color: #21c9b8;
      }
      .itemButton {
        background: #21c9b8;
        color: #ffffff;
      }
    }
    .activity-page-end {
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0 auto;
      padding: 24px 0 60px;
      .activity-page-btn {
        width: 82px;
        height: 32px;
        background: #fff;
        border-radius: 4px;
        border: 1px solid #d9d9d9;
        font-family: PingFangSC-Regular, PingFang SC;
        color: #333;
        line-height: 10px;
      }
    }
  }
  .none-class {
    text-align: center;
    padding: 8% 0;
    background: #fff;
    margin-top: 25px;
    .text {
      font-size: 14px;
      font-weight: 400;
      color: #999999;
      line-height: 14px;
    }
  }
}
</style>

<style lang="scss">
.activity-container {
  .activity-search-input {
    .el-input__inner {
      height: 54px;
      background: #fff;
      border-radius: 27px 0 0 27px;
      border: 1px solid #d9d9d9;
      font-size: 16px;
      font-family: PingFangSC-Regular, PingFang SC;
      line-height: 24px;
      padding-left: 30px;
    }
    .el-input-group__append {
      border-radius: 0px 100px 100px 0px;
      background: #21c9b8;
      font-size: 16px;
      font-family: PingFangSC-Regular, PingFang SC;
      color: #fff;
      line-height: 24px;
    }
  }
  .activity-search-line {
    .el-form-item__label {
      width: 88px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #999;
      padding-right: 32px;
      text-align: left;
    }
    .activity-search-radio {
      width: 1050px;
      margin-top: 11px;
      .el-radio-button {
        padding-bottom: 20px;
        .el-radio-button__inner {
          border: none;
          padding: 0 32px 0 0;
          background: none;
          &:hover {
            color: #21c9b8;
          }
        }
        &.is-active {
          .el-radio-button__inner {
            color: #21c9b8;
            background: none;
          }
        }
        .el-radio-button__orig-radio:checked {
          & + .el-radio-button__inner {
            box-shadow: unset;
          }
        }
      }
    }
  }
  .activity-page-end {
    .activity-pagination {
      .btn-prev,
      .btn-next,
      .btn-quickprev {
        width: 32px;
        height: 32px;
        background: #fff;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        color: #333;
      }
      &.is-background {
        .el-pager {
          .number {
            width: 32px;
            height: 32px;
            border: 1px solid #d9d9d9;
            background: #fff;
            border-radius: 4px;
            line-height: 32px;
            &.active {
              background: #21c9b8;
              border: 1px solid #21c9b8;
            }
          }
        }
      }
    }
  }
}
</style>

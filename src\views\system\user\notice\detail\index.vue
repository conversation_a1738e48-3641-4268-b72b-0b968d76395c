<!--
 * @Author: zhc
 * @Date: 2023-02-03 11:06:49
 * @LastEditTime: 2023-02-21 14:03:04
 * @Description: 
 * @LastEditors: zhc
-->
<template>
  <div class="app-container">
    <div class="notice-record-detail">
      <el-row :gutter="20">
        <el-col :span="2.5" :xs="24">
          <user-menu />
        </el-col>
        <el-col :span="20" :xs="24">
          <div class="info-container">
            <div class="header">
              <el-button icon="el-icon-arrow-left" @click="goBack"></el-button>
              <div class="header-text">消息详情</div>
            </div>
            <el-form ref="form" :model="form" label-width="120px">
              <el-col :span="24">
                <el-form-item label="资源类型:" prop="policyTitle">
                  {{ form.resourceTypeName || "--" }}
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="资源标题:" prop="publishCompany">
                  {{ form.resourceTitle || "--" }}
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="资源描述:" prop="policyType">
                  {{ form.resourceDescribe || "--" }}</el-form-item
                >
              </el-col>
              <el-col :span="24">
                <el-form-item label="联系人:" prop="policyType">
                  {{ form.contactPhone || "--" }}</el-form-item
                >
              </el-col>
            </el-form>
            <div class="delete-btn">
              <el-button type="danger" @click="deleteInfo">删除</el-button>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import UserMenu from "../../components/userMenu.vue";
import { deleteInfo, getInfoDetail } from "@/api/system/info";
export default {
  name: "Notice",
  components: { UserMenu },
  data() {
    return {
      form: {},
    };
  },
  created() {
    this.getDetail();
  },
  methods: {
    getDetail() {
      let userId = this.$route.query.id;
      getInfoDetail(userId).then((response) => {
        this.form = response.data;
        this.total = response.total;
      });
    },
    goBack() {
      this.$router.go(-1);
    },
    deleteInfo(row) {
      this.$confirm("是否确认删除该消息？", { type: "error" })
        .then((_) => {
          deleteInfo({ ids: this.$route.query.id }).then((response) => {
            if (response.code == 200) {
              this.$router.go(-1);
            }
          });
        })
        .catch((_) => {});
    },
  },
};
</script>

<style lang="scss">
.app-container {
  background: #f4f5f9;
  .notice-record-detail {
    .info-container {
      width: 100%;
      height: 500px;
      padding-top: 12px;

      background-color: white;
      .header {
        display: flex;
        margin-bottom: 30px;
        text-align: center;
        .el-button {
          height: 40px;
          border-color: transparent;
          padding: 10px 10px 10px 20px;
          font-size: 20px;
          color: #000;
        }
        .el-button:hover {
          background-color: white;
        }
        .header-text {
          line-height: 40px;
        }
      }
      .delete-btn {
        width: 100%;
        text-align: center;
      }
    }
  }
}
</style>

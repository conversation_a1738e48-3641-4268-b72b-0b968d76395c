<template>
  <div class="company-tab-container">
    <div v-loading="loading" class="tab-main">
      <el-scrollbar noresize class="left">
        <div class="tab-content">
          <div
            v-for="(item, index) in tabs"
            :key="index"
            :class="{ active: tabIndex === index }"
            class="tab-content-item"
            @click="onTabChange(index)"
          >
            {{ item.dictLabel }}
          </div>
        </div>
      </el-scrollbar>
      <el-row class="right" :gutter="24">
        <template v-if="items.length > 0">
          <el-col :span="8" v-for="item in items" :key="item.id">
            <router-link
              target="_blank"
              :to="`/enterpriseDetail?id=${item.id}&businessNo=${item.businessNo}`"
            >
              <div class="card">
                <el-image
                  class="card-img"
                  :src="item.url ? item.url : defaultUrl"
                  fit="fill"
                />
                <div class="card-footer">
                  <div class="title" :title="item.company">
                    {{ item.company }}
                  </div>
                  <div class="tag">{{ item.tag }}</div>
                </div>
              </div>
            </router-link>
          </el-col>
        </template>
        <template v-else>
          <el-empty />
        </template>
      </el-row>
    </div>
    <div class="tab-page-end">
      <!-- <span class="demonstration">完整功能</span> -->
      <el-pagination
        class="company-tab-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pageNum"
        :page-sizes="[100, 200, 300, 400]"
        :page-size="pageSize"
        layout=" prev, pager, next "
        :total="total"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import { head, map } from "ramda";
import { getDicts } from "@/api/system/dict/data";
import { listCompany } from "@/api/zhm";
import CryptoJS from "crypto-js";
let secretKey = "9zVn0%bqmUYSGw2n";

export default {
  name: "CompanyTab",
  data() {
    return {
      loading: false,
      tabs: [],
      tabIndex: 0,
      items: [],
      pageNum: 1,
      pageSize: 6,
      total: 0,
      defaultUrl: require("../../../../assets/purchaseSales/companyDefault.png"),
    };
  },
  created() {
    this.initData();
  },
  methods: {
    initData() {
      getDicts("industrial_chain").then((res) => {
        const { code, data = [] } = res;
        if (code === 200) {
          this.tabs = data;
          this.tabs.unshift({
            dictLabel: "全部",
            dictValue: undefined,
          });
          const item = head(data);
          this.getCompanyData(item.dictValue);
        }
      });
    },
    getCompanyData(type) {
      this.loading = true;
      listCompany({
        industrialChain: type,
        recommendStatus: 1,
        pageNum: this.pageNum,
        // pageSize: this.pageSize,
      })
        .then((res) => {
          let key = CryptoJS.enc.Utf8.parse(secretKey);
          let decrypt = CryptoJS.AES.decrypt(res, key, {
            mode: CryptoJS.mode.ECB,
            padding: CryptoJS.pad.Pkcs7,
          });
          res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));
          const { code, rows = [] } = res;
          if (code === 200) {
            this.items = map((item) => {
              let url;
              const images = item.companyPictureList || [];
              if (images.length > 0) {
                url = head(images).url;
              }
              return {
                id: item.id,
                company: item.name,
                businessNo: item.businessNo,
                tag: item.category,
                url,
              };
            }, rows);
          }
          this.total = res.total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    onTabChange(index) {
      if (index !== this.tabIndex) {
        this.tabIndex = index;
        this.pageNum = 1;
        const item = this.tabs[index] || {};
        this.getCompanyData(item.dictValue);
      }
    },
    handleSizeChange(newSize) {
      // console.log(`每页 ${val} 条`);
      this.pageSize = newSize;
      const item = this.tabs[this.tabIndex] || {};

      this.getCompanyData(item.dictValue);
    },
    handleCurrentChange(newPage) {
      // console.log(`当前页: ${val}`);
      this.pageNum = newPage;
      const item = this.tabs[this.tabIndex] || {};
      this.getCompanyData(item.dictValue);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "~@/assets/styles/mixin.scss";
.company-tab-container {
  .tab-main {
    position: relative;
    display: flex;
    flex-shrink: 0;
    width: 100%;
    flex-direction: row;
    ::v-deep .el-scrollbar__wrap {
      overflow-x: hidden;
      overflow-y: auto;
    }
    .left {
      width: 148px;
      height: 580px;
      background: #21c9b8;
      .el-scrollbar__wrap {
        height: 103%;
      }
      // ::-webkit-scrollbar-track-piece {
      //   background-color: #21C9B8 !important;
      // }
      .tab-content {
        padding: 24px 0 24px 18px;
        &-item {
          @include ellipsis;
          display: flex;
          justify-content: center;
          align-items: center;
          flex-shrink: 0;
          height: 40px;
          font-size: 14px;
          font-weight: 500;
          color: #ffffff;
          line-height: 14px;
          transition: background, color 0.25ms ease;
          margin-bottom: 12px;
          cursor: pointer;
          &.active {
            color: #21c9b8;
            background: linear-gradient(270deg, #fbfdff 0%, #ffffff 100%);
          }
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
    .right {
      flex: 1;
      padding-left: 36px;
      .card {
        width: 100%;
        min-height: 300px;
        background: #ffffff;
        box-shadow: 0 10px 50px 0 rgba(38, 74, 116, 0.1);
        margin-bottom: 24px;
        &-img {
          width: 100%;
          height: 200px;
          background: #ffffff;
        }
        &-footer {
          padding: 16px 20px;
          .title {
            @include ellipsis;
            // @include multiEllipsis(2);
            font-size: 18px;
            font-weight: 500;
            color: #333333;
            line-height: 26px;
            margin-bottom: 10px;
          }
          .tag {
            display: inline-block;
            background: rgba(197, 37, 33, 0.1);
            border-radius: 4px;
            padding: 6px 10px;
            font-size: 12px;
            font-weight: 400;
            color: #21c9b8;
            line-height: 12px;
          }
        }
      }
    }
  }
}
</style>
<style lang="scss">
.company-tab-container {
  .tab-page-end {
    .company-tab-pagination {
      width: 220px;
      margin: 0 auto;
      .btn-prev,
      .btn-next,
      .btn-quickprev {
        width: 32px;
        height: 32px;
        background: #ffffff;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        margin: 0 6px;
        color: #333;
      }
      .el-pager {
        .number {
          width: 32px;
          height: 32px;
          border: 1px solid #d9d9d9;
          background: #ffffff;
          border-radius: 4px;
          line-height: 32px;
          margin: 0 6px;
          &.active {
            background: #21c9b8;
            border: 1px solid #21c9b8;
            color: #fff;
          }
        }
      }
    }
  }
}
</style>

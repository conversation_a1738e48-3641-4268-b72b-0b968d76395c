<template>
  <div class="expert-library-detail">
    <!-- banner图 -->
    <div class="expert-detail-banner">
      <img
        src="../../../../assets/expertLibrary/expertLibraryDetailBanner.png"
        alt=""
      />
    </div>
    <div class="expert-detail-title-box">
      <div class="expert-detail-divider"></div>
      <div class="expert-detail-title">专家详情</div>
      <div class="expert-detail-divider"></div>
    </div>
    <div v-loading="loading" class="expert-detail-content">
      <div class="expert-detail-headline">
        <div class="expert-detail-headline-img">
          <img v-if="data.headPortrait" :src="data.headPortrait" alt="" />
          <img
            v-else
            src="../../../../assets/expertLibrary/defaultImg.png"
            alt=""
          />
        </div>
        <div class="expert-detail-headline-info">
          <div class="headline-info-title">{{ data.expertName }}</div>
          <div class="headline-info-box">
            <span class="headline-info-laber">单位：</span>
            <span class="headline-info-description">
              {{ data.workUnit }}
            </span>
          </div>
          <div class="headline-info-box">
            <span class="headline-info-laber">职位：</span>
            <span class="headline-info-description">{{ data.post }}</span>
          </div>
          <div class="headline-info-box">
            <span class="headline-info-laber">研究方向：</span>
            <span class="headline-info-description">{{
              data.researchDirection
            }}</span>
          </div>
          <div class="headline-content-btn">
            <el-button
              v-if="showBtn"
              class="headline-btn-style intention-btn"
              @click="goIntention"
              >我有意向</el-button
            >
            <el-button @click="goChat" icon="el-icon-chat-dot-round"
              >在线沟通</el-button
            >
          </div>
        </div>
      </div>
      <div class="expert-detail-introduce">
        <div class="introduce-content">
          <div class="introduction-line"></div>
          <div class="introduction-title">专家介绍</div>
        </div>
        <div class="introduction-text-content">
          <div
            v-html="data.introduce"
            class="introduction-text ql-editor"
          ></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getExpertDetail, getCheckSubmit } from "@/api/purchaseSales";
import { getInfo } from "@/api/login";
import { getCompanyInfoByLoginInfo } from "@/api/apathy";
import { mapGetters } from "vuex";
import { getCustomerServicerInfo } from "@/api/expertLibrary/index";
import CryptoJS from "crypto-js";
let secretKey = "9zVn0%bqmUYSGw2n";

export default {
  data() {
    return {
      loading: false,
      data: {},
      showBtn: true,
    };
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      this.loading = true;
      getExpertDetail({ id: this.$route.query.id })
        .then((res) => {
          let key = CryptoJS.enc.Utf8.parse(secretKey);
          let decrypt = CryptoJS.AES.decrypt(res, key, {
            mode: CryptoJS.mode.ECB,
            padding: CryptoJS.pad.Pkcs7,
          });
          res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));

          this.loading = false;
          this.data = res.data || {};
          if (!this.token) {
            this.showBtn = true;
          } else {
            this.getInfo();
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
    // 判断此资源是不是自己发布的
    getInfo() {
      getInfo().then((res) => {
        if (this.data.createById === res.user.userId) {
          this.showBtn = false;
        } else {
          this.showBtn = true;
        }
      });
    },
    goChat() {
      // 判断是否登录
      if (!this.token) {
        this.$confirm("请先登录", "提示", {
          confirmButtonText: "去登录",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          this.$store.dispatch("LogOut").then(() => {
            location.href = "/login";
          });
        });
        return;
      }

      getCustomerServicerInfo().then((res) => {
        if (res.code == 200) {
          let routeData = this.$router.resolve({
            path: "/user/im",
            query: {
              userId: res.data.id,
            },
          });
          window.open(routeData.href, "_blank");
        } else {
          this.$message({
            type: "warning",
            message: "获取客服信息失败,请稍后再试",
          });
        }
      });
    },
    // 跳到我有意向页面
    goIntention() {
      // 判断是否登录
      if (!this.token) {
        this.$confirm("请先登录", "提示", {
          confirmButtonText: "去登录",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          this.$store.dispatch("LogOut").then(() => {
            location.href = "/login";
          });
        });
        return;
      }
      // 是否加入企业
      this.loading = true;
      getCompanyInfoByLoginInfo()
        .then((res) => {
          if (res.data) {
            // 是否对此资源提交过意向
            getCheckSubmit({
              id: this.$route.query.id,
              resourceType: "resource_expet",
            })
              .then((res) => {
                this.loading = false;
                // true 提交过  false未提交过
                if (res.data) {
                  this.$message({
                    type: "warning",
                    message: "已经提交过了哦！",
                  });
                } else {
                  let routeData = this.$router.resolve({
                    path: "/addIntention",
                    query: {
                      id: this.$route.query.id,
                      type: "resource_expet",
                      title: this.data.expertName,
                    },
                  });
                  window.open(routeData.href, "_blank");
                }
              })
              .catch(() => {
                this.loading = false;
              });
          } else {
            this.loading = false;
            this.$message({
              type: "warning",
              message: "必须加入企业才可提交我有意向",
            });
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
  },
  computed: {
    ...mapGetters(["token"]),
  },
};
</script>

<style lang="scss" scoped>
.expert-library-detail {
  width: 100%;
  background: #f4f5f9;
  padding-bottom: 60px;
  .expert-detail-banner {
    width: 100%;
    height: 25.92vh;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .expert-detail-title-box {
    width: 336px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 60px 0 40px;
    .expert-detail-title {
      font-size: 40px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #333;
      line-height: 40px;
      padding: 0 40px;
    }
    .expert-detail-divider {
      width: 48px;
      height: 4px;
      background: #21c9b8;
    }
  }
  .expert-detail-content {
    width: 1200px;
    background: #fff;
    margin: 0 auto;
    padding: 60px 60px 124px;
    .expert-detail-headline {
      display: flex;
      .expert-detail-headline-img {
        width: 240px;
        height: 240px;
        margin-right: 40px;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .expert-detail-headline-info {
        flex: 1;
        .headline-info-title {
          max-width: 792px;
          font-size: 32px;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: #333;
          line-height: 32px;
          padding-bottom: 20px;
          word-wrap: break-word;
        }
        .headline-info-box {
          display: flex;
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          line-height: 32px;
          .headline-info-laber {
            width: 70px;
            color: #666;
          }
          .headline-info-description {
            flex: 1;
            max-width: 712px;
            color: #333;
            word-wrap: break-word;
          }
        }
        .headline-content-btn {
          padding-top: 52px;
          .headline-btn-style {
            width: 100px;
            height: 32px;
            border-radius: 4px;
            font-family: PingFangSC-Regular, PingFang SC;
            padding: 8px 11px;
          }
          .intention-btn {
            background: #21c9b8;
            color: #fff;
          }
          .communication-btn {
            border: 1px solid #21c9b8;
            color: #21c9b8;
            background: transparent;
          }
        }
      }
    }
    .expert-detail-introduce {
      padding-top: 60px;
      .introduce-content {
        display: flex;
        align-items: center;
        padding-bottom: 40px;
        .introduction-line {
          width: 4px;
          height: 20px;
          background: #21c9b8;
        }
        .introduction-title {
          font-size: 24px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #333;
          line-height: 24px;
          padding-left: 8px;
        }
      }
      .introduction-text-content {
        font-size: 16px;
        font-family: PingFangSC-Regular, PingFang SC;
        color: #333;
        line-height: 28px;
      }
    }
  }
}
</style>

<style lang="scss">
.expert-library-detail {
  .introduction-text-content {
    .introduction-text {
      word-break: break-all;
      font-size: 16px;
      line-height: 28px;
      color: #333;
      font-family: PingFangSC-Regular, PingFang SC;
      img {
        max-width: 100%;
      }
    }
  }
}
</style>

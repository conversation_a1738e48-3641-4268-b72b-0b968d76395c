// import { resolve } from "core-js/fn/promise";

/*
 * @Author: zhc
 * @Date: 2023-05-18 15:05:26
 * @LastEditTime: 2023-05-31 08:53:00
 * @Description:
 * @LastEditors: zhc
 */
import * as RongIMLib from "@rongcloud/imlib-next";
import { getUserListByIds } from "@/api/system/user";
let flag = false;
export default {
  // 获取用户详情
  getUserProfile: async (userId) => {
    // 需要通过 userId 向应用服务器获取 user 信息，拼接成如下格式
    // 注意：userInfo 的 Key 不可修改
    if (flag) {
      return;
    }
    flag = true;
    let url = "";
    let userName = "";
    await getUserListByIds([userId])
      .then((res) => {
        if (res.code === 200) {
          url = res.data[0].userPortrait;
          userName = res.data[0].realName;
        }
      })
      .catch((err) => {
        console.log(err);
      });

    const userInfo = {
      id: userId,
      name: userName,
      portraitUri: url,
    };

    return Promise.resolve(userInfo);
  },

  // 获取会话详情
  // (会话 targetId 和会话类型 conversationType ) - 包括头像，会话(单聊和群聊)名称，群组头像
  getConversationProfile: async (conversations) => {
    let ids = [];
    let promises = [];
    conversations.forEach((conversation) => {
      ids.push(conversation.targetId);
    });
    await getUserListByIds(ids)
      .then((res) => {
        if (res.code === 200) {
          conversations.forEach((conversation) => {
            let index = res.data.findIndex(
              (item) => conversation.targetId == item.id
            );
            const converationInfo = {
              ...conversation,
              name: res.data[index].realName,
              portraitUri: res.data[index].userPortrait,
            };
            promises.push(Promise.resolve(converationInfo));
          });
          // res.data.forEach((item) => {

          //   let param = {
          //     channelId: "",
          //     conversationType: "1",
          //     targetId: item.id,
          //     name: item.realName,
          //     portraitUri: item.userPortrait,
          //   };
          //   console.log("getConversationProfile", param);

          //   promises.push(Promise.resolve(param));
          // });
        }
      })
      .catch((err) => {
        console.log(err);
      });

    return Promise.all(promises);
  },

  // 获取群组详情
  getGroupMembers: (conversation) => {
    // 通过 conversation 的 targetid 获取群组成员信息
    // groupMembers 为群组成员 list，需要构建成对象数组。
    // 特别注意：如果传递的群组成员信息不准确会影响 @ 信息的发送和群组成员昵称的展示
    const groupMembers = [
      {
        id: `【成员】成员 ID`,
        name: `【成员】name`,
        portraitUri: `【成员】头像 URI`,
      },
    ];
    return Promise.resolve(groupMembers);
  },
};

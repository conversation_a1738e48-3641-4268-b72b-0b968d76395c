<template>
  <div class="content">
    <div class="content_banner">
      人才服务
    </div>
    <div class="card-container content_card">
      <div class="card-title">复合材料技能鉴定中心</div>
      <el-form ref="form" :rules="rules" :model="form" label-position="top">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="姓名" prop="name">
              <el-input v-model="form.name" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="身份证号" prop="idCard">
              <el-input v-model="form.idCard" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="所在单位" prop="certificationType2">
              <el-input v-model="form.certificationType2" placeholder="请输入所在单位"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="学历" prop="education">
              <el-select v-model="form.education" placeholder="请选择学历" clearable style="width: 100%">
                <el-option v-for="dict in educationList" :key="dict.dictLabel" :label="dict.dictLabel"
                  :value="dict.dictValue" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="联系方式" prop="contact">
              <el-input v-model="form.contact" placeholder="请输入联系方式"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="鉴定工种" prop="certificationType1">
              <el-select v-model="form.certificationType1" placeholder="请选择鉴定工种" clearable style="width: 100%">
                <el-option v-for="dict in positionTypeList" :key="dict.dictLabel" :label="dict.dictLabel"
                  :value="dict.dictValue" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="鉴定技能等级" prop="skillLevel">
              <el-select v-model="form.skillLevel" placeholder="请选择鉴定技能等级" clearable style="width: 100%">
                <el-option v-for="dict in skillLevelList" :key="dict.dictLabel" :label="dict.dictLabel"
                  :value="dict.dictValue" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item class="footer-submit">
              <el-button type="primary" @click="onSubmit">提交</el-button>
              <el-button style="margin-left: 140px" @click.once="onCancel">取消</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
</template>
<script>
import { listData } from "@/api/system/dict/data";
import { applySkill } from "@/api/serviceSharing";
import { getInfo } from "@/api/login";

export default {
  data() {
    return {
      form: {},
      rules: {
        name: [{ required: true, message: "姓名不能为空", trigger: "blur" }],
        certificationType1: [
          { required: true, message: "鉴定工种不能为空", trigger: "change" },
        ],
      },
      positionTypeList: [], // 岗位分类
      educationList: [], // 最高学历
      skillLevelList: [], // 职称
    };
  },
  created() {
    this.getPositionType();
    this.getEducation();
    this.getSkillLevel();
  },
  methods: {
    // 岗位分类
    getPositionType() {
      let params = { dictType: "certification_type" };
      listData(params).then((response) => {
        this.positionTypeList = response.rows;
      });
    },
    // 最高学历
    getEducation() {
      let params = { dictType: "education" };
      listData(params).then((response) => {
        this.educationList = response.rows;
      });
    },
    // 技能等级
    getSkillLevel() {
      let params = { dictType: "skill_level" };
      listData(params).then((response) => {
        this.skillLevelList = response.rows;
      });
    },
    onSubmit() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          applySkill(this.form).then((response) => {
            if (response.code === 200) {
              this.$message.success('申请鉴定成功!')
              this.onCancel()
            } else {
              this.$message.warning(response.msg)
            }
          })
        }
      });
    },
    onCancel() {
      this.$router.go(-1);
    },
  },
};
</script>
<style scoped lang="scss">
.content {
  width: 100%;
  padding-bottom: 60px;
  background-color: #f2f2f2;
}

.content_banner {
  width: 100%;
  height: 300px;
  background-image: url("../../../../assets/release/banner.png");
  background-size: 100% 100%;
  text-align: center;
  margin: 0 auto;
  font-family: Source Han Sans CN;
  font-weight: 500;
  font-size: 40px;
  color: #000;
  line-height: 300px;

  .imgContent {
    width: 100%;
    display: flex;
    justify-content: center;
    margin-top: 10px;

    .imgStyle {
      width: 1256px;
      height: 206px;
      position: relative;
    }
  }
}

.content_card {
  // height: 1530px;
  background: #ffffff;
  border-radius: 2px;
  margin-top: 30px;
  padding: 29px 60px 57px 60px;

  .card-title {
    font-weight: 500;
    font-size: 22px;
    color: #222222;
    line-height: 70px;
  }
}

.addStyle {
  font-family: Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #21c9b8;
  margin-left: auto;
  cursor: pointer;
}

.footer-submit {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin-top: 60px;

  .el-button {
    width: 140px;
    height: 50px;
  }
}
</style>

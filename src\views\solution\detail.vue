<template>
  <div class="notice-detail-container">
    <div class="soDetailBanner" :style="{ 'background-image': `url(${form.solutionBanner})` }">
      <div class="bannerSolutionFlex" style="height: 100%">
        <div>
          <p class="solutionTitle" style="color:#333;">{{ form.solutionName }}</p>
          <p class="solutionEng" style="color:#333;">{{ form.solutionIntroduction }}</p>
        </div>
      </div>
    </div>
    <div class="schemeBox" v-if="form.solutionOverview">
      <div class="serveContent">
        <div class="newsHeader">
          <div class="titleAll">方案概述</div>
        </div>
        <div class="serveBottom">
          <div class="sketchLeft">
            <p style="display: inline-block;position: absolute; width: 505px;height: 297px;background: #21c9b8;"></p>
            <!-- <img src="@/assets/solution/sketchBg.png" class="sketchBg"> -->
            <img width="506" v-if='form.solutionImg' height="296" :src="form.solutionImg" class="sketch">
            <img width="506" v-else height="296" src="@/assets/solution/default.jpg" class="sketch">
          </div>
          <div class="sketchRight">
            <div style="text-align: end;height: 58px;">
              <img src="@/assets/solution/comma.png">
            </div>
            {{ form.solutionOverview }}
            <div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="painSpotBox" v-if="painList != null && painList.length != 0">
      <div class="serveContent">
        <div class="newsHeader">
          <div class="titleAll">行业痛点</div>
        </div>
        <div class="painContent">
          <div v-for="(item, index) in painList" :key="index" class="painDiv">
            <!-- <div><img :src="`../images/icon/fa0${index + 1}.svg`" style="width: 40px"></div> -->
            <div class="painDivTitle">{{ item.solutionPainName }}</div>
            <div class="textOverflow3">{{ item.solutionPainContent }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="advantageBox" v-if="advantageList != null && advantageList.length != 0">
      <div class="serveContent">
        <div class="newsHeader">
          <div class="titleAll">方案优势</div>
        </div>
        <div>
          <el-carousel ref="carousel" :autoplay="false" indicator-position="none" @change="changeadvantageListHover">
            <el-carousel-item v-for="(item, index) in advantageList" :key="index">
              <div class="serveBottom">
                <div class="advantageLeft">
                  <div class="advantageTitleBox">
                    <img src="@/assets/solution/advantageIcon.svg">
                    <div class="advantageTitle">{{ item.solutionAdvantageName }}</div>
                  </div>
                  <div class="advsubtitle">{{ item.solutionAdvantageType }}</div>
                  <div>{{ item.solutionAdvantageContent }}</div>
                </div>
                <div style="width: 552px;height: 358px">
                  <img :src="item.solutionAdvantageImage ? item.solutionAdvantageImage : gjImgdefault"
                    style="width: 100%;height: 100%">
                </div>
              </div>
            </el-carousel-item>
          </el-carousel>
        </div>
      </div>
    </div>
    <div class="caseBoxdetail" v-if="caseList != null && caseList.length != 0">
      <div class="serveContent">
        <div class="newsHeader">
          <div class="titleAll">实施案例</div>
        </div>
      </div>
      <div class="casecontent" v-if="caseList.length > 0">
        <div class="serveContent serveBottom">
          <div class="caseLeft">
            <div v-for="(item, index) in caseList" :key="index"
              :class="['caseLeftbtn', caseIndex == index ? 'caseLeftbtnhover' : '']" @click="changeCaseIndex(index)"
              style="display: flex;align-items: center;">
              <img style="margin-left: 10px;width: 17px" src="@/assets/solution/caseicon.png">
              <span style="margin-left: 20px;" class="textOverflow1">{{ item.solutionCaseName }}</span>
            </div>
          </div>
          <div class="caseRight">
            <div class="caseRightTitle">{{ caseList[caseIndex].solutionCaseName }}</div>
            <div class="caseRightContent">{{ caseList[caseIndex].solutionCaseContent }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { solutionDetail, solutionDicts } from "@/api/solution";
import "@/assets/styles/index.css";

export default {
  name: "policyPage",
  data() {
    return {
      showLogin: false,
      userinfo: [],
      token: '',
      productList: [],
      cmsList: [],
      cmsData: [],
      demandList: [],
      input: '',
      requireList: [],
      queryIndex: 0,
      total: 0,
      pageSize: 10,
      pageNum: 1,
      titleContent: '',
      requirementTypeCodeArray: [],
      form: {},
      imageUrl: '',
      aaa: '1',
      dataList: [],
      painList: [],
      advantageList: [],
      advantageListHover: 0,
      benefitList: [],
      caseList: [],
      caseIndex: 0,
      gjImgdefault: '@/assets/solution/gjimgdefault.png'
    };
  },
  created() {
    if (this.$route.query.id) {
      this.id = this.$route.query.id
      this.getDemandList()
    }
  },
  methods: {
    async getDemandList() {
      let res = await solutionDetail({ solutionId: this.id });
      if (res.code == 200) {
        this.form = res.data;
        this.painList = res.data.alSolutionPainVOs;
        this.caseList = res.data.alSolutionCaseVOs;
        this.advantageList = res.data.alSolutionAdvantageVOs;
      }
    },
    async getDicts() {
      let res = await solutionDicts();
      if (res.code == 200) {
        this.requireList = res.data;
      }
    },
    currentChange(val) {
      this.pageNum = val
      this.getDemandList()
    },
    changeSolve(val) {
      this.aaa = val
    },
    changeadvantageListHover(val) {
      this.advantageListHover = val
    },
    lastStep() {
      this.$refs.carousel.setActiveItem(this.advantageListHover - 1)
    },
    nextStep() {
      this.$refs.carousel.setActiveItem(this.advantageListHover + 1)
    },
    changeCaseIndex(index) {
      this.caseIndex = index
    },
    getUrlKey: function (name) {
      return decodeURIComponent((new RegExp('[?|&]' + name + '=' + '([^&;]+?)(&|#|;|$)').exec(
        location.href) || [, ""])[
        1].replace(/\+/g, '%20')) || null;
    }
  },
};
</script>

<style lang="scss" scoped>
.notice-detail-container {
  width: 100%;
  padding: 0 0 100px;
  background: #f4f5f9;
}
</style>

<style lang="scss">
.notice-detail-container {
  .notice-info-content {
    word-break: break-all;
    font-size: 16px;
    line-height: 28px;
    color: #333;
    font-family: PingFangSC-Regular, PingFang SC;

    img {
      max-width: 100%;
    }
  }
}

.swiper-pagination-bullet {
  background: #fff;
}

.swiper-wrapper {
  position: relative;
}

.swiper-container {
  width: 100%;
}

.swiper-container2 {
  width: 100%;
  overflow: hidden;
}

.soDetailBanner {
  background-repeat: no-repeat;
  background-size: 100%;
}
</style>

<template>
  <div class="activity-detail-container">
    <!-- banner图 -->
    <div class="activity-detail-banner">
      <img
        src="../../../../assets/activitySquare/activityDetailBanner.png"
        alt=""
      />
    </div>
    <div class="activity-detail-title-box">
      <div class="activity-detail-divider"></div>
      <div class="activity-detail-title">活动详情</div>
      <div class="activity-detail-divider"></div>
    </div>
    <div v-loading="loading" class="activity-detail-card">
      <div class="activity-detail-content">
        <div class="activity-detail-headline">
          <div class="headline-title">
            {{ data.activityName }}
          </div>
          <div class="headline-time">
            {{ getTime() }}
          </div>
        </div>
        <!-- 活动详情标题 -->
        <div class="activity-detail-caption" v-if="data.activityContent">
          <div class="caption-line"></div>
          <div class="caption-title">活动详情</div>
        </div>
        <!-- 活动详情 -->
        <div class="activity-detail-img">
          <img v-if="data.activityContent" :src="data.activityContent" alt="" />
        </div>
        <!-- 相关专家标题 -->
        <div
          class="activity-detail-caption"
          v-if="data.expertList && data.expertList.length > 0"
        >
          <div class="caption-line"></div>
          <div class="caption-title">相关专家</div>
        </div>
        <!-- 相关专家 -->
        <div class="activity-detai-list">
          <div
            v-for="(item, index) in data.expertList"
            :key="index"
            class="list-item-content"
            @click="goExpertLibraryDetail(item.id)"
          >
            <div class="list-item-box">
              <div class="item-headline">
                <div class="item-title">
                  {{ item.expertName }}
                </div>
              </div>
              <div class="activity-detai-label">
                <div
                  v-for="(val, index1) in item.techniqueTypeName"
                  :key="index1"
                  class="activity-label-item"
                >
                  <span v-if="index1 < 2" class="activity-label-type">{{
                    `#${val}`
                  }}</span>
                  <span v-else>…</span>
                </div>
              </div>
              <div class="activity-detai-box">
                {{ item.synopsis }}
              </div>
            </div>
            <div class="list-item-img">
              <img v-if="item.headPortrait" :src="item.headPortrait" alt="" />
              <img
                v-else
                src="../../../../assets/expertLibrary/defaultImg.png"
                alt=""
              />
            </div>
          </div>
        </div>
        <!-- 相关需求标题 -->
        <div
          class="activity-detail-caption"
          v-if="data.demandList && data.demandList.length > 0"
        >
          <div class="caption-line"></div>
          <div class="caption-title">相关需求</div>
        </div>
        <!-- 相关需求内容 -->
        <div class="activity-demand-info">
          <div
            v-for="(item, index) in data.demandList"
            :key="index"
            class="activity-demand-item"
            @click="goDemandDetail(item.id)"
          >
            <div class="activity-item-img">
              <img
                v-if="item.scenePicture && item.scenePicture.length > 0"
                :src="item.scenePicture[0].url"
                alt=""
              />
              <img
                v-else
                src="../../../../assets/purchaseSales/demandDefault.png"
                alt=""
              />
            </div>
            <div class="activity-item-content">
              <div class="activity-item-title">
                {{ item.demandTitle }}
              </div>
              <div class="activity-item-content-tag">
                <div
                  v-for="(val, num) in item.applicationArea"
                  :key="num"
                  class="activity-item-tag"
                >
                  {{ val }}
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 相关供给标题 -->
        <div
          class="activity-detail-caption related-supply-title"
          v-if="data.supplyList && data.supplyList.length > 0"
        >
          <div class="caption-line"></div>
          <div class="caption-title">相关供给</div>
        </div>
        <!-- 相关供给内容 -->
        <div class="activity-demand-info">
          <div
            v-for="(item, index) in data.supplyList"
            :key="index"
            class="activity-demand-item"
            @click="goResourceDetail(item.id)"
          >
            <div class="activity-item-img">
              <img
                v-if="item.productPhoto && item.productPhoto.length > 0"
                :src="item.productPhoto[0].url"
                alt=""
              />
              <img
                v-else
                src="../../../../assets/purchaseSales/resourceDefault.png"
                alt=""
              />
            </div>
            <div class="activity-item-content">
              <div class="activity-item-title">
                {{ item.supplyName }}
              </div>
              <div class="activity-item-content-tag">
                <div
                  v-for="(val, num) in item.applicationArea"
                  :key="num"
                  class="activity-item-tag"
                >
                  {{ val }}
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 立即报名按钮 -->
        <div class="activity-area-btn">
          <el-button
            v-if="data.isEnroll === 1"
            disabled
            type="info"
            class="activity-disabled-btn"
            >您已报名</el-button
          >
          <el-button v-else class="activity-sign-up" @click="signUp"
            >立即报名</el-button
          >
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getActivityDetail, addActivityEnroll } from "@/api/purchaseSales";
import { mapGetters } from "vuex";
import { getInfo } from "@/api/login";

export default {
  data() {
    return {
      loading: false,
      data: {},
    };
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      this.token ? this.getInfo() : this.getActivityDetail();
    },
    // 详情接口
    getActivityDetail(id) {
      this.loading = true;
      let userId = id ? id : null;
      getActivityDetail({ id: this.$route.query.id, userId: userId })
        .then((res) => {
          this.loading = false;
          this.data = res.data || {};
          if (this.data.expertList && this.data.expertList.length > 0) {
            this.data.expertList.forEach((item) => {
              item.techniqueTypeName = item.techniqueTypeName.split(",");
            });
          }
          if (this.data.demandList && this.data.demandList.length > 0) {
            this.data.demandList.forEach((item) => {
              item.scenePicture = JSON.parse(item.scenePicture);
              item.applicationArea = item.applicationArea.split(",");
            });
          }
          if (this.data.supplyList && this.data.supplyList.length > 0) {
            this.data.supplyList.forEach((item) => {
              item.productPhoto = JSON.parse(item.productPhoto);
              item.applicationArea = item.applicationArea.split(",");
            });
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
    getInfo() {
      getInfo().then((res) => {
        let id = res.user.userId || null;
        this.getActivityDetail(id);
      });
    },
    // 时间展示
    getTime() {
      let info = "--";
      if (this.data.startTime && this.data.endTime) {
        info = `${this.data.startTime}至${this.data.endTime}`;
      } else if (this.data.startTime || this.data.endTime) {
        info = this.data.startTime || this.data.endTime;
      }
      return info;
    },
    // 跳转到专家详情页面
    goExpertLibraryDetail(id) {
      let routeData = this.$router.resolve({
        path: "/expertDetail",
        query: { id },
      });
      window.open(routeData.href, "_blank");
    },
    // 跳转到需求详情页面
    goDemandDetail(id) {
      let routeData = this.$router.resolve({
        path: "/demandHallDetail",
        query: { id },
      });
      window.open(routeData.href, "_blank");
    },
    // 跳转到资源详情页面
    goResourceDetail(id) {
      let routeData = this.$router.resolve({
        path: "/resourceHallDetail",
        query: { id },
      });
      window.open(routeData.href, "_blank");
    },
    // 立即报名
    signUp() {
      // 判断是否登录
      if (!this.token) {
        this.$confirm("请先登录", "提示", {
          confirmButtonText: "去登录",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          this.$store.dispatch("LogOut").then(() => {
            location.href = "/login";
          });
        });
        return;
      }
      this.$confirm(
        `您参加的是${this.data.activityName},请确认。报名成功后，平台客服会与您对接`,
        "提示",
        {
          confirmButtonText: "我要参加",
          cancelButtonText: "取消",
          type: "warning",
        }
      ).then(() => {
        this.loading = true;
        addActivityEnroll({ activityId: this.$route.query.id })
          .then(() => {
            this.loading = false;
            this.$message.success("报名成功");
            this.init();
          })
          .catch(() => {
            this.loading = false;
          });
      });
    },
  },
  computed: {
    ...mapGetters(["token"]),
  },
};
</script>

<style lang="scss" scoped>
.activity-detail-container {
  width: 100%;
  padding-bottom: 60px;
  background: #f4f5f9;
  .activity-detail-banner {
    width: 100%;
    height: 25.93vh;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .activity-detail-title-box {
    width: 336px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 60px 0 40px;
    .activity-detail-title {
      font-size: 40px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #333;
      line-height: 40px;
      padding: 0 40px;
    }
    .activity-detail-divider {
      width: 48px;
      height: 4px;
      background: #21c9b8;
    }
  }
  .activity-detail-card {
    width: 1200px;
    background: #fff;
    margin: 0 auto;
    .activity-detail-content {
      padding: 52px 116px 60px;
      .activity-detail-headline {
        font-family: PingFangSC-Regular, PingFang SC;
        padding-bottom: 32px;
        border-bottom: 1px solid #e8e8e8;
        margin-bottom: 40px;
        .headline-title {
          font-size: 32px;
          font-weight: 600;
          color: #333;
          line-height: 48px;
          word-wrap: break-word;
        }
        .headline-time {
          color: #333;
          line-height: 14px;
          padding-top: 16px;
        }
      }
      .activity-detail-caption {
        display: flex;
        align-items: center;
        .caption-line {
          width: 4px;
          height: 20px;
          background: #21c9b8;
        }
        .caption-title {
          font-size: 24px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #333;
          line-height: 24px;
          padding-left: 8px;
        }
      }
      .related-supply-title {
        margin-top: 60px;
      }
      .activity-demand-info {
        display: flex;
        flex-wrap: wrap;
        .activity-demand-item {
          width: 222px;
          margin: 40px 18px 0 0;
          background: #f8f9fb;
          .activity-item-img {
            width: 100%;
            height: 160px;
            img {
              width: 100%;
              height: 100%;
            }
          }
          .activity-item-content {
            padding: 16px 16px 14px;
            .activity-item-title {
              width: 190px;
              height: 52px;
              font-size: 18px;
              font-family: PingFangSC-Medium, PingFang SC;
              font-weight: 500;
              color: #333;
              line-height: 26px;
              overflow: hidden;
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 2;
              text-overflow: ellipsis;
            }
            .activity-item-content-tag {
              display: flex;
              flex-wrap: wrap;
              .activity-item-tag {
                max-width: 190px;
                padding: 6px 12px;
                font-size: 12px;
                font-family: PingFangSC-Regular, PingFang SC;
                color: #214dc5;
                line-height: 12px;
                background: rgba(33, 77, 197, 0.1);
                border-radius: 4px;
                margin: 12px 12px 0 0;
                word-wrap: break-word;
              }
            }
          }
          &:hover {
            cursor: pointer;
            .activity-item-title {
              color: #21c9b8;
            }
          }
        }
      }
      .activity-detail-img {
        width: 960px;
        // height: 1160px;
        height: 100%;
        margin: 40px 0 60px;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .activity-detai-list {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        width: 100%;
        margin-bottom: 60px;
        .list-item-content {
          display: flex;
          justify-content: space-between;
          width: 462px;
          background: #fff;
          box-shadow: 0px 8px 32px 0px rgba(38, 74, 116, 0.1);
          margin-top: 40px;
          padding: 22px 25px 23px 26px;
          min-height: 179px;
          .list-item-box {
            flex: 1;
            .item-headline {
              display: flex;
              justify-content: space-between;
              align-items: center;
              .item-title {
                width: 200px;
                font-size: 26px;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 500;
                color: #333;
                line-height: 26px;
                text-overflow: ellipsis;
                white-space: nowrap;
                overflow: hidden;
                word-wrap: break-word;
              }
            }
            .activity-detai-label {
              display: flex;
              flex-wrap: wrap;
              margin: 0 0 15px;
              .activity-label-item {
                max-width: 280px;
                padding: 5px 12px;
                background: #f4f5f9;
                border-radius: 4px;
                font-size: 10px;
                font-family: PingFangSC-Regular, PingFang SC;
                color: #666;
                line-height: 10px;
                margin: 19px 12px 0 0;
                .activity-label-type {
                  word-wrap: break-word;
                }
              }
            }
            .activity-detai-box {
              width: 296px;
              font-size: 12px;
              font-family: PingFangSC-Regular, PingFang SC;
              color: #666;
              line-height: 24px;
              overflow: hidden;
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 2;
              text-overflow: ellipsis;
              word-wrap: break-word;
            }
          }
          .list-item-img {
            width: 96px;
            height: 134px;
            margin-left: 19px;
            img {
              width: 100%;
              height: 100%;
            }
          }
          &:hover {
            cursor: pointer;
          }
        }
      }
      .activity-area-btn {
        text-align: center;
        margin-top: 60px;
        .activity-sign-up {
          width: 400px;
          height: 50px;
          background: #21c9b8;
          border-radius: 4px;
          font-size: 20px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #fff;
          line-height: 20px;
        }
      }
      .activity-disabled-btn {
        width: 400px;
        height: 50px;
        border-radius: 4px;
        font-size: 20px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        line-height: 20px;
      }
    }
  }
}
</style>
